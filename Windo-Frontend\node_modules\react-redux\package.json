{"name": "react-redux", "version": "9.0.4", "description": "Official React bindings for Redux", "keywords": ["react", "reactjs", "redux"], "license": "MIT", "author": "<PERSON> <<EMAIL>> (https://github.com/gaearon)", "homepage": "https://github.com/reduxjs/react-redux", "repository": "github:reduxjs/react-redux", "bugs": "https://github.com/reduxjs/react-redux/issues", "module": "dist/react-redux.legacy-esm.js", "main": "dist/cjs/index.js", "react-native": "./dist/react-redux.legacy-esm.js", "types": "dist/react-redux.d.ts", "exports": {"./package.json": "./package.json", ".": {"types": "./dist/react-redux.d.ts", "react-server": "./dist/rsc.mjs", "react-native": "./dist/react-redux.legacy-esm.js", "import": "./dist/react-redux.mjs", "default": "./dist/cjs/index.js"}, "./alternate-renderers": {"types": "./dist/react-redux.d.ts", "import": "./dist/react-redux.mjs", "default": "./dist/cjs/index.js"}}, "sideEffects": false, "files": ["dist"], "scripts": {"build": "tsup", "clean": "rimraf lib dist es coverage", "api-types": "api-extractor run --local", "format": "prettier --write \"{src,test}/**/*.{js,ts,tsx}\" \"docs/**/*.md\"", "lint": "eslint src --ext ts,tsx,js test/utils test/components test/hooks", "prepare": "yarn clean && yarn build", "pretest": "yarn lint", "test": "jest", "type-tests": "yarn tsc -p test/typetests/tsconfig.json", "coverage": "codecov"}, "peerDependencies": {"@types/react": "^18.2.25", "react": "^18.0", "react-native": ">=0.69", "redux": "^5.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "react-native": {"optional": true}, "redux": {"optional": true}}, "dependencies": {"@types/use-sync-external-store": "^0.0.3", "use-sync-external-store": "^1.0.0"}, "devDependencies": {"@babel/cli": "^7.12.1", "@babel/core": "^7.12.3", "@babel/plugin-proposal-decorators": "^7.12.1", "@babel/plugin-proposal-object-rest-spread": "^7.12.1", "@babel/plugin-transform-flow-strip-types": "^7.22.5", "@babel/plugin-transform-react-display-name": "^7.12.1", "@babel/plugin-transform-react-jsx": "^7.12.1", "@babel/plugin-transform-runtime": "^7.12.1", "@babel/preset-env": "^7.12.1", "@babel/preset-typescript": "^7.14.5", "@microsoft/api-extractor": "^7.18.1", "@reduxjs/toolkit": "^2.0.0-beta.4", "@testing-library/jest-dom": "^5.11.5", "@testing-library/jest-native": "^3.4.3", "@testing-library/react": "13.0.0", "@testing-library/react-12": "npm:@testing-library/react@^12", "@testing-library/react-hooks": "^3.4.2", "@testing-library/react-native": "^7.1.0", "@types/react": "18.2.25", "@types/react-native": "^0.67.4", "@typescript-eslint/eslint-plugin": "^4.28.0", "@typescript-eslint/parser": "^4.28.0", "babel-eslint": "^10.1.0", "babel-jest": "^29", "codecov": "^3.8.0", "cross-env": "^7.0.2", "eslint": "^7.12.0", "eslint-config-prettier": "^6.14.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.1.4", "eslint-plugin-react": "^7.21.5", "glob": "^7.1.6", "jest": "^29", "jest-environment-jsdom": "^29.5.0", "metro-react-native-babel-preset": "^0.76.6", "prettier": "^2.1.2", "react": "18.2.0", "react-dom": "18.2.0", "react-native": "^0.71.11", "react-test-renderer": "18.0.0", "redux": "^5.0.0", "rimraf": "^3.0.2", "ts-jest": "^29", "tsup": "^7.0.0", "typescript": "^5.0"}}