{"version": 3, "sources": ["../../use-sync-external-store/cjs/use-sync-external-store-with-selector.production.js", "../../use-sync-external-store/cjs/use-sync-external-store-with-selector.development.js", "../../use-sync-external-store/with-selector.js", "../../react-redux/src/index.ts", "../../react-redux/src/utils/react.ts", "../../react-redux/src/components/Context.ts", "../../react-redux/src/utils/useSyncExternalStore.ts", "../../react-redux/src/hooks/useReduxContext.ts", "../../react-redux/src/hooks/useSelector.ts", "../../react-redux/src/utils/react-is.ts", "../../react-redux/src/utils/warning.ts", "../../react-redux/src/connect/verifySubselectors.ts", "../../react-redux/src/connect/selectorFactory.ts", "../../react-redux/src/utils/bindActionCreators.ts", "../../react-redux/src/utils/isPlainObject.ts", "../../react-redux/src/utils/verifyPlainObject.ts", "../../react-redux/src/connect/wrapMapToProps.ts", "../../react-redux/src/connect/invalidArgFactory.ts", "../../react-redux/src/connect/mapDispatchToProps.ts", "../../react-redux/src/connect/mapStateToProps.ts", "../../react-redux/src/connect/mergeProps.ts", "../../react-redux/src/utils/batch.ts", "../../react-redux/src/utils/Subscription.ts", "../../react-redux/src/utils/useIsomorphicLayoutEffect.ts", "../../react-redux/src/utils/shallowEqual.ts", "../../react-redux/src/utils/hoistStatics.ts", "../../react-redux/src/components/connect.tsx", "../../react-redux/src/components/Provider.tsx", "../../react-redux/src/hooks/useStore.ts", "../../react-redux/src/hooks/useDispatch.ts", "../../react-redux/src/exports.ts"], "sourcesContent": ["/**\n * @license React\n * use-sync-external-store-with-selector.production.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\nvar React = require(\"react\");\nfunction is(x, y) {\n  return (x === y && (0 !== x || 1 / x === 1 / y)) || (x !== x && y !== y);\n}\nvar objectIs = \"function\" === typeof Object.is ? Object.is : is,\n  useSyncExternalStore = React.useSyncExternalStore,\n  useRef = React.useRef,\n  useEffect = React.useEffect,\n  useMemo = React.useMemo,\n  useDebugValue = React.useDebugValue;\nexports.useSyncExternalStoreWithSelector = function (\n  subscribe,\n  getSnapshot,\n  getServerSnapshot,\n  selector,\n  isEqual\n) {\n  var instRef = useRef(null);\n  if (null === instRef.current) {\n    var inst = { hasValue: !1, value: null };\n    instRef.current = inst;\n  } else inst = instRef.current;\n  instRef = useMemo(\n    function () {\n      function memoizedSelector(nextSnapshot) {\n        if (!hasMemo) {\n          hasMemo = !0;\n          memoizedSnapshot = nextSnapshot;\n          nextSnapshot = selector(nextSnapshot);\n          if (void 0 !== isEqual && inst.hasValue) {\n            var currentSelection = inst.value;\n            if (isEqual(currentSelection, nextSnapshot))\n              return (memoizedSelection = currentSelection);\n          }\n          return (memoizedSelection = nextSnapshot);\n        }\n        currentSelection = memoizedSelection;\n        if (objectIs(memoizedSnapshot, nextSnapshot)) return currentSelection;\n        var nextSelection = selector(nextSnapshot);\n        if (void 0 !== isEqual && isEqual(currentSelection, nextSelection))\n          return (memoizedSnapshot = nextSnapshot), currentSelection;\n        memoizedSnapshot = nextSnapshot;\n        return (memoizedSelection = nextSelection);\n      }\n      var hasMemo = !1,\n        memoizedSnapshot,\n        memoizedSelection,\n        maybeGetServerSnapshot =\n          void 0 === getServerSnapshot ? null : getServerSnapshot;\n      return [\n        function () {\n          return memoizedSelector(getSnapshot());\n        },\n        null === maybeGetServerSnapshot\n          ? void 0\n          : function () {\n              return memoizedSelector(maybeGetServerSnapshot());\n            }\n      ];\n    },\n    [getSnapshot, getServerSnapshot, selector, isEqual]\n  );\n  var value = useSyncExternalStore(subscribe, instRef[0], instRef[1]);\n  useEffect(\n    function () {\n      inst.hasValue = !0;\n      inst.value = value;\n    },\n    [value]\n  );\n  useDebugValue(value);\n  return value;\n};\n", "/**\n * @license React\n * use-sync-external-store-with-selector.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function is(x, y) {\n      return (x === y && (0 !== x || 1 / x === 1 / y)) || (x !== x && y !== y);\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    var React = require(\"react\"),\n      objectIs = \"function\" === typeof Object.is ? Object.is : is,\n      useSyncExternalStore = React.useSyncExternalStore,\n      useRef = React.useRef,\n      useEffect = React.useEffect,\n      useMemo = React.useMemo,\n      useDebugValue = React.useDebugValue;\n    exports.useSyncExternalStoreWithSelector = function (\n      subscribe,\n      getSnapshot,\n      getServerSnapshot,\n      selector,\n      isEqual\n    ) {\n      var instRef = useRef(null);\n      if (null === instRef.current) {\n        var inst = { hasValue: !1, value: null };\n        instRef.current = inst;\n      } else inst = instRef.current;\n      instRef = useMemo(\n        function () {\n          function memoizedSelector(nextSnapshot) {\n            if (!hasMemo) {\n              hasMemo = !0;\n              memoizedSnapshot = nextSnapshot;\n              nextSnapshot = selector(nextSnapshot);\n              if (void 0 !== isEqual && inst.hasValue) {\n                var currentSelection = inst.value;\n                if (isEqual(currentSelection, nextSnapshot))\n                  return (memoizedSelection = currentSelection);\n              }\n              return (memoizedSelection = nextSnapshot);\n            }\n            currentSelection = memoizedSelection;\n            if (objectIs(memoizedSnapshot, nextSnapshot))\n              return currentSelection;\n            var nextSelection = selector(nextSnapshot);\n            if (void 0 !== isEqual && isEqual(currentSelection, nextSelection))\n              return (memoizedSnapshot = nextSnapshot), currentSelection;\n            memoizedSnapshot = nextSnapshot;\n            return (memoizedSelection = nextSelection);\n          }\n          var hasMemo = !1,\n            memoizedSnapshot,\n            memoizedSelection,\n            maybeGetServerSnapshot =\n              void 0 === getServerSnapshot ? null : getServerSnapshot;\n          return [\n            function () {\n              return memoizedSelector(getSnapshot());\n            },\n            null === maybeGetServerSnapshot\n              ? void 0\n              : function () {\n                  return memoizedSelector(maybeGetServerSnapshot());\n                }\n          ];\n        },\n        [getSnapshot, getServerSnapshot, selector, isEqual]\n      );\n      var value = useSyncExternalStore(subscribe, instRef[0], instRef[1]);\n      useEffect(\n        function () {\n          inst.hasValue = !0;\n          inst.value = value;\n        },\n        [value]\n      );\n      useDebugValue(value);\n      return value;\n    };\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  })();\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/use-sync-external-store-with-selector.production.js');\n} else {\n  module.exports = require('./cjs/use-sync-external-store-with-selector.development.js');\n}\n", "// The primary entry point assumes we are working with React 18, and thus have\r\n// useSyncExternalStore available. We can import that directly from React itself.\r\n// The useSyncExternalStoreWithSelector has to be imported, but we can use the\r\n// non-shim version. This shaves off the byte size of the shim.\r\n\r\nimport * as React from 'react'\r\nimport { useSyncExternalStoreWithSelector } from 'use-sync-external-store/with-selector.js'\r\n\r\nimport { initializeUseSelector } from './hooks/useSelector'\r\nimport { initializeConnect } from './components/connect'\r\n\r\ninitializeUseSelector(useSyncExternalStoreWithSelector)\r\ninitializeConnect(React.useSyncExternalStore)\r\n\r\nexport * from './exports'\r\n", "import * as ReactOriginal from 'react'\r\nimport type * as ReactNamespace from 'react'\r\n\r\nexport const React: typeof ReactNamespace =\r\n  // prettier-ignore\r\n  // @ts-ignore\r\n  'default' in ReactOriginal ? ReactOriginal['default'] : ReactOriginal as any\r\n", "import type { Context } from 'react'\r\nimport { React } from '../utils/react'\r\nimport type { Action, Store, UnknownAction } from 'redux'\r\nimport type { Subscription } from '../utils/Subscription'\r\nimport type { ProviderProps } from './Provider'\r\n\r\nexport interface ReactReduxContextValue<\r\n  SS = any,\r\n  A extends Action<string> = UnknownAction\r\n> extends Pick<ProviderProps, 'stabilityCheck' | 'identityFunctionCheck'> {\r\n  store: Store<SS, A>\r\n  subscription: Subscription\r\n  getServerState?: () => SS\r\n}\r\n\r\nconst ContextKey = Symbol.for(`react-redux-context`)\r\nconst gT: {\r\n  [ContextKey]?: Map<\r\n    typeof React.createContext,\r\n    Context<ReactReduxContextValue | null>\r\n  >\r\n} = (\r\n  typeof globalThis !== 'undefined'\r\n    ? globalThis\r\n    : /* fall back to a per-module scope (pre-8.1 behaviour) if `globalThis` is not available */ {}\r\n) as any\r\n\r\nfunction getContext(): Context<ReactReduxContextValue | null> {\r\n  if (!React.createContext) return {} as any\r\n\r\n  const contextMap = (gT[ContextKey] ??= new Map<\r\n    typeof React.createContext,\r\n    Context<ReactReduxContextValue | null>\r\n  >())\r\n  let realContext = contextMap.get(React.createContext)\r\n  if (!realContext) {\r\n    realContext = React.createContext<ReactReduxContextValue | null>(\r\n      null as any\r\n    )\r\n    if (process.env.NODE_ENV !== 'production') {\r\n      realContext.displayName = 'ReactRedux'\r\n    }\r\n    contextMap.set(React.createContext, realContext)\r\n  }\r\n  return realContext\r\n}\r\n\r\nexport const ReactReduxContext = /*#__PURE__*/ getContext()\r\n\r\nexport type ReactReduxContextInstance = typeof ReactReduxContext\r\n\r\nexport default ReactReduxContext\r\n", "import type { useSyncExternalStore } from 'use-sync-external-store'\r\nimport type { useSyncExternalStoreWithSelector } from 'use-sync-external-store/with-selector'\r\n\r\nexport const notInitialized = () => {\r\n  throw new Error('uSES not initialized!')\r\n}\r\n\r\nexport type uSES = typeof useSyncExternalStore\r\nexport type uSESWS = typeof useSyncExternalStoreWithSelector\r\n", "import { React } from '../utils/react'\r\nimport { ReactReduxContext } from '../components/Context'\r\nimport type { ReactReduxContextValue } from '../components/Context'\r\n\r\n/**\r\n * Hook factory, which creates a `useReduxContext` hook bound to a given context. This is a low-level\r\n * hook that you should usually not need to call directly.\r\n *\r\n * @param {React.Context} [context=ReactReduxContext] Context passed to your `<Provider>`.\r\n * @returns {Function} A `useReduxContext` hook bound to the specified context.\r\n */\r\nexport function createReduxContextHook(context = ReactReduxContext) {\r\n  return function useReduxContext(): ReactReduxContextValue {\r\n    const contextValue = React.useContext(context)\r\n\r\n    if (process.env.NODE_ENV !== 'production' && !contextValue) {\r\n      throw new Error(\r\n        'could not find react-redux context value; please ensure the component is wrapped in a <Provider>'\r\n      )\r\n    }\r\n\r\n    return contextValue!\r\n  }\r\n}\r\n\r\n/**\r\n * A hook to access the value of the `ReactReduxContext`. This is a low-level\r\n * hook that you should usually not need to call directly.\r\n *\r\n * @returns {any} the value of the `ReactReduxContext`\r\n *\r\n * @example\r\n *\r\n * import React from 'react'\r\n * import { useReduxContext } from 'react-redux'\r\n *\r\n * export const CounterComponent = () => {\r\n *   const { store } = useReduxContext()\r\n *   return <div>{store.getState()}</div>\r\n * }\r\n */\r\nexport const useReduxContext = /*#__PURE__*/ createReduxContextHook()\r\n", "//import * as React from 'react'\r\nimport { React } from '../utils/react'\r\n\r\nimport type { ReactReduxContextValue } from '../components/Context'\r\nimport { ReactReduxContext } from '../components/Context'\r\nimport type { EqualityFn, NoInfer } from '../types'\r\nimport type { uSESWS } from '../utils/useSyncExternalStore'\r\nimport { notInitialized } from '../utils/useSyncExternalStore'\r\nimport {\r\n  createReduxContextHook,\r\n  useReduxContext as useDefaultReduxContext,\r\n} from './useReduxContext'\r\n\r\n/**\r\n * The frequency of development mode checks.\r\n *\r\n * @since 8.1.0\r\n * @internal\r\n */\r\nexport type DevModeCheckFrequency = 'never' | 'once' | 'always'\r\n\r\n/**\r\n * Represents the configuration for development mode checks.\r\n *\r\n * @since 9.0.0\r\n * @internal\r\n */\r\nexport interface DevModeChecks {\r\n  /**\r\n   * Overrides the global stability check for the selector.\r\n   * - `once` - Run only the first time the selector is called.\r\n   * - `always` - Run every time the selector is called.\r\n   * - `never` - Never run the stability check.\r\n   *\r\n   * @default 'once'\r\n   *\r\n   * @since 8.1.0\r\n   */\r\n  stabilityCheck: DevModeCheckFrequency\r\n\r\n  /**\r\n   * Overrides the global identity function check for the selector.\r\n   * - `once` - Run only the first time the selector is called.\r\n   * - `always` - Run every time the selector is called.\r\n   * - `never` - Never run the identity function check.\r\n   *\r\n   * **Note**: Previously referred to as `noopCheck`.\r\n   *\r\n   * @default 'once'\r\n   *\r\n   * @since 9.0.0\r\n   */\r\n  identityFunctionCheck: DevModeCheckFrequency\r\n}\r\n\r\nexport interface UseSelectorOptions<Selected = unknown> {\r\n  equalityFn?: EqualityFn<Selected>\r\n\r\n  /**\r\n   * `useSelector` performs additional checks in development mode to help\r\n   * identify and warn about potential issues in selector behavior. This\r\n   * option allows you to customize the behavior of these checks per selector.\r\n   *\r\n   * @since 9.0.0\r\n   */\r\n  devModeChecks?: Partial<DevModeChecks>\r\n}\r\n\r\nexport interface UseSelector {\r\n  <TState = unknown, Selected = unknown>(\r\n    selector: (state: TState) => Selected,\r\n    equalityFn?: EqualityFn<Selected>\r\n  ): Selected\r\n  <TState = unknown, Selected = unknown>(\r\n    selector: (state: TState) => Selected,\r\n    options?: UseSelectorOptions<Selected>\r\n  ): Selected\r\n}\r\n\r\nlet useSyncExternalStoreWithSelector = notInitialized as uSESWS\r\nexport const initializeUseSelector = (fn: uSESWS) => {\r\n  useSyncExternalStoreWithSelector = fn\r\n}\r\n\r\nconst refEquality: EqualityFn<any> = (a, b) => a === b\r\n\r\n/**\r\n * Hook factory, which creates a `useSelector` hook bound to a given context.\r\n *\r\n * @param {React.Context} [context=ReactReduxContext] Context passed to your `<Provider>`.\r\n * @returns {Function} A `useSelector` hook bound to the specified context.\r\n */\r\nexport function createSelectorHook(\r\n  context: React.Context<ReactReduxContextValue<\r\n    any,\r\n    any\r\n  > | null> = ReactReduxContext\r\n): UseSelector {\r\n  const useReduxContext =\r\n    context === ReactReduxContext\r\n      ? useDefaultReduxContext\r\n      : createReduxContextHook(context)\r\n\r\n  return function useSelector<TState, Selected extends unknown>(\r\n    selector: (state: TState) => Selected,\r\n    equalityFnOrOptions:\r\n      | EqualityFn<NoInfer<Selected>>\r\n      | UseSelectorOptions<NoInfer<Selected>> = {}\r\n  ): Selected {\r\n    const { equalityFn = refEquality, devModeChecks = {} } =\r\n      typeof equalityFnOrOptions === 'function'\r\n        ? { equalityFn: equalityFnOrOptions }\r\n        : equalityFnOrOptions\r\n    if (process.env.NODE_ENV !== 'production') {\r\n      if (!selector) {\r\n        throw new Error(`You must pass a selector to useSelector`)\r\n      }\r\n      if (typeof selector !== 'function') {\r\n        throw new Error(`You must pass a function as a selector to useSelector`)\r\n      }\r\n      if (typeof equalityFn !== 'function') {\r\n        throw new Error(\r\n          `You must pass a function as an equality function to useSelector`\r\n        )\r\n      }\r\n    }\r\n\r\n    const {\r\n      store,\r\n      subscription,\r\n      getServerState,\r\n      stabilityCheck,\r\n      identityFunctionCheck,\r\n    } = useReduxContext()\r\n\r\n    const firstRun = React.useRef(true)\r\n\r\n    const wrappedSelector = React.useCallback<typeof selector>(\r\n      {\r\n        [selector.name](state: TState) {\r\n          const selected = selector(state)\r\n          if (process.env.NODE_ENV !== 'production') {\r\n            const {\r\n              identityFunctionCheck: finalIdentityFunctionCheck,\r\n              stabilityCheck: finalStabilityCheck,\r\n            } = {\r\n              stabilityCheck,\r\n              identityFunctionCheck,\r\n              ...devModeChecks,\r\n            }\r\n            if (\r\n              finalStabilityCheck === 'always' ||\r\n              (finalStabilityCheck === 'once' && firstRun.current)\r\n            ) {\r\n              const toCompare = selector(state)\r\n              if (!equalityFn(selected, toCompare)) {\r\n                let stack: string | undefined = undefined\r\n                try {\r\n                  throw new Error()\r\n                } catch (e) {\r\n                  ;({ stack } = e as Error)\r\n                }\r\n                console.warn(\r\n                  'Selector ' +\r\n                    (selector.name || 'unknown') +\r\n                    ' returned a different result when called with the same parameters. This can lead to unnecessary rerenders.' +\r\n                    '\\nSelectors that return a new reference (such as an object or an array) should be memoized: https://redux.js.org/usage/deriving-data-selectors#optimizing-selectors-with-memoization',\r\n                  {\r\n                    state,\r\n                    selected,\r\n                    selected2: toCompare,\r\n                    stack,\r\n                  }\r\n                )\r\n              }\r\n            }\r\n            if (\r\n              finalIdentityFunctionCheck === 'always' ||\r\n              (finalIdentityFunctionCheck === 'once' && firstRun.current)\r\n            ) {\r\n              // @ts-ignore\r\n              if (selected === state) {\r\n                let stack: string | undefined = undefined\r\n                try {\r\n                  throw new Error()\r\n                } catch (e) {\r\n                  ;({ stack } = e as Error)\r\n                }\r\n                console.warn(\r\n                  'Selector ' +\r\n                    (selector.name || 'unknown') +\r\n                    ' returned the root state when called. This can lead to unnecessary rerenders.' +\r\n                    '\\nSelectors that return the entire state are almost certainly a mistake, as they will cause a rerender whenever *anything* in state changes.',\r\n                  { stack }\r\n                )\r\n              }\r\n            }\r\n            if (firstRun.current) firstRun.current = false\r\n          }\r\n          return selected\r\n        },\r\n      }[selector.name],\r\n      [selector, stabilityCheck, devModeChecks.stabilityCheck]\r\n    )\r\n\r\n    const selectedState = useSyncExternalStoreWithSelector(\r\n      subscription.addNestedSub,\r\n      store.getState,\r\n      getServerState || store.getState,\r\n      wrappedSelector,\r\n      equalityFn\r\n    )\r\n\r\n    React.useDebugValue(selectedState)\r\n\r\n    return selectedState\r\n  }\r\n}\r\n\r\n/**\r\n * A hook to access the redux store's state. This hook takes a selector function\r\n * as an argument. The selector is called with the store state.\r\n *\r\n * This hook takes an optional equality comparison function as the second parameter\r\n * that allows you to customize the way the selected state is compared to determine\r\n * whether the component needs to be re-rendered.\r\n *\r\n * @param {Function} selector the selector function\r\n * @param {Function=} equalityFn the function that will be used to determine equality\r\n *\r\n * @returns {any} the selected state\r\n *\r\n * @example\r\n *\r\n * import React from 'react'\r\n * import { useSelector } from 'react-redux'\r\n *\r\n * export const CounterComponent = () => {\r\n *   const counter = useSelector(state => state.counter)\r\n *   return <div>{counter}</div>\r\n * }\r\n */\r\nexport const useSelector = /*#__PURE__*/ createSelectorHook()\r\n", "import type { ElementType, MemoExoticComponent, ReactElement } from 'react'\r\n\r\n// Directly ported from:\r\n// https://unpkg.com/browse/react-is@18.3.0-canary-ee68446ff-20231115/cjs/react-is.production.js\r\n// It's very possible this could change in the future, but given that\r\n// we only use these in `connect`, this is a low priority.\r\n\r\nconst REACT_ELEMENT_TYPE = Symbol.for('react.element')\r\nconst REACT_PORTAL_TYPE = Symbol.for('react.portal')\r\nconst REACT_FRAGMENT_TYPE = Symbol.for('react.fragment')\r\nconst REACT_STRICT_MODE_TYPE = Symbol.for('react.strict_mode')\r\nconst REACT_PROFILER_TYPE = Symbol.for('react.profiler')\r\nconst REACT_PROVIDER_TYPE = Symbol.for('react.provider')\r\nconst REACT_CONTEXT_TYPE = Symbol.for('react.context')\r\nconst REACT_SERVER_CONTEXT_TYPE = Symbol.for('react.server_context')\r\nconst REACT_FORWARD_REF_TYPE = Symbol.for('react.forward_ref')\r\nconst REACT_SUSPENSE_TYPE = Symbol.for('react.suspense')\r\nconst REACT_SUSPENSE_LIST_TYPE = Symbol.for('react.suspense_list')\r\nconst REACT_MEMO_TYPE = Symbol.for('react.memo')\r\nconst REACT_LAZY_TYPE = Symbol.for('react.lazy')\r\nconst REACT_OFFSCREEN_TYPE = Symbol.for('react.offscreen')\r\nconst REACT_CLIENT_REFERENCE = Symbol.for('react.client.reference')\r\n\r\nexport const ForwardRef = REACT_FORWARD_REF_TYPE\r\nexport const Memo = REACT_MEMO_TYPE\r\n\r\nexport function isValidElementType(type: any): type is ElementType {\r\n  if (typeof type === 'string' || typeof type === 'function') {\r\n    return true\r\n  } // Note: typeof might be other than 'symbol' or 'number' (e.g. if it's a polyfill).\r\n\r\n  if (\r\n    type === REACT_FRAGMENT_TYPE ||\r\n    type === REACT_PROFILER_TYPE ||\r\n    type === REACT_STRICT_MODE_TYPE ||\r\n    type === REACT_SUSPENSE_TYPE ||\r\n    type === REACT_SUSPENSE_LIST_TYPE ||\r\n    type === REACT_OFFSCREEN_TYPE\r\n  ) {\r\n    return true\r\n  }\r\n\r\n  if (typeof type === 'object' && type !== null) {\r\n    if (\r\n      type.$$typeof === REACT_LAZY_TYPE ||\r\n      type.$$typeof === REACT_MEMO_TYPE ||\r\n      type.$$typeof === REACT_PROVIDER_TYPE ||\r\n      type.$$typeof === REACT_CONTEXT_TYPE ||\r\n      type.$$typeof === REACT_FORWARD_REF_TYPE || // This needs to include all possible module reference object\r\n      // types supported by any Flight configuration anywhere since\r\n      // we don't know which Flight build this will end up being used\r\n      // with.\r\n      type.$$typeof === REACT_CLIENT_REFERENCE ||\r\n      type.getModuleId !== undefined\r\n    ) {\r\n      return true\r\n    }\r\n  }\r\n\r\n  return false\r\n}\r\n\r\nfunction typeOf(object: any): symbol | undefined {\r\n  if (typeof object === 'object' && object !== null) {\r\n    const $$typeof = object.$$typeof\r\n\r\n    switch ($$typeof) {\r\n      case REACT_ELEMENT_TYPE: {\r\n        const type = object.type\r\n\r\n        switch (type) {\r\n          case REACT_FRAGMENT_TYPE:\r\n          case REACT_PROFILER_TYPE:\r\n          case REACT_STRICT_MODE_TYPE:\r\n          case REACT_SUSPENSE_TYPE:\r\n          case REACT_SUSPENSE_LIST_TYPE:\r\n            return type\r\n\r\n          default: {\r\n            const $$typeofType = type && type.$$typeof\r\n\r\n            switch ($$typeofType) {\r\n              case REACT_SERVER_CONTEXT_TYPE:\r\n              case REACT_CONTEXT_TYPE:\r\n              case REACT_FORWARD_REF_TYPE:\r\n              case REACT_LAZY_TYPE:\r\n              case REACT_MEMO_TYPE:\r\n              case REACT_PROVIDER_TYPE:\r\n                return $$typeofType\r\n\r\n              default:\r\n                return $$typeof\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      case REACT_PORTAL_TYPE: {\r\n        return $$typeof\r\n      }\r\n    }\r\n  }\r\n\r\n  return undefined\r\n}\r\n\r\nexport function isContextConsumer(object: any): object is ReactElement {\r\n  return typeOf(object) === REACT_CONTEXT_TYPE\r\n}\r\n\r\nexport function isMemo(object: any): object is MemoExoticComponent<any> {\r\n  return typeOf(object) === REACT_MEMO_TYPE\r\n}\r\n", "/**\r\n * Prints a warning in the console if it exists.\r\n *\r\n * @param {String} message The warning message.\r\n * @returns {void}\r\n */\r\nexport default function warning(message: string) {\r\n  /* eslint-disable no-console */\r\n  if (typeof console !== 'undefined' && typeof console.error === 'function') {\r\n    console.error(message)\r\n  }\r\n  /* eslint-enable no-console */\r\n  try {\r\n    // This error was thrown as a convenience so that if you enable\r\n    // \"break on all exceptions\" in your console,\r\n    // it would pause the execution at this line.\r\n    throw new Error(message)\r\n    /* eslint-disable no-empty */\r\n  } catch (e) {}\r\n  /* eslint-enable no-empty */\r\n}\r\n", "import warning from '../utils/warning'\r\n\r\nfunction verify(selector: unknown, methodName: string): void {\r\n  if (!selector) {\r\n    throw new Error(`Unexpected value for ${methodName} in connect.`)\r\n  } else if (\r\n    methodName === 'mapStateToProps' ||\r\n    methodName === 'mapDispatchToProps'\r\n  ) {\r\n    if (!Object.prototype.hasOwnProperty.call(selector, 'dependsOnOwnProps')) {\r\n      warning(\r\n        `The selector for ${methodName} of connect did not specify a value for dependsOnOwnProps.`\r\n      )\r\n    }\r\n  }\r\n}\r\n\r\nexport default function verifySubselectors(\r\n  mapStateToProps: unknown,\r\n  mapDispatchToProps: unknown,\r\n  mergeProps: unknown\r\n): void {\r\n  verify(mapStateToProps, 'mapStateToProps')\r\n  verify(mapDispatchToProps, 'mapDispatchToProps')\r\n  verify(mergeProps, 'mergeProps')\r\n}\r\n", "import type { Dispatch, Action } from 'redux'\r\nimport type { ComponentType } from 'react'\r\nimport verifySubselectors from './verifySubselectors'\r\nimport type { EqualityFn, ExtendedEqualityFn } from '../types'\r\n\r\nexport type SelectorFactory<S, TProps, TOwnProps, TFactoryOptions> = (\r\n  dispatch: Dispatch<Action<string>>,\r\n  factoryOptions: TFactoryOptions\r\n) => Selector<S, TProps, TOwnProps>\r\n\r\nexport type Selector<S, TProps, TOwnProps = null> = TOwnProps extends\r\n  | null\r\n  | undefined\r\n  ? (state: S) => TProps\r\n  : (state: S, ownProps: TOwnProps) => TProps\r\n\r\nexport type MapStateToProps<TStateProps, TOwnProps, State> = (\r\n  state: State,\r\n  ownProps: TOwnProps\r\n) => TStateProps\r\n\r\nexport type MapStateToPropsFactory<TStateProps, TOwnProps, State> = (\r\n  initialState: State,\r\n  ownProps: TOwnProps\r\n) => MapStateToProps<TStateProps, TOwnProps, State>\r\n\r\nexport type MapStateToPropsParam<TStateProps, TOwnProps, State> =\r\n  | MapStateToPropsFactory<TStateProps, TOwnProps, State>\r\n  | MapStateToProps<TStateProps, TOwnProps, State>\r\n  | null\r\n  | undefined\r\n\r\nexport type MapDispatchToPropsFunction<TDispatchProps, TOwnProps> = (\r\n  dispatch: Dispatch<Action<string>>,\r\n  ownProps: TOwnProps\r\n) => TDispatchProps\r\n\r\nexport type MapDispatchToProps<TDispatchProps, TOwnProps> =\r\n  | MapDispatchToPropsFunction<TDispatchProps, TOwnProps>\r\n  | TDispatchProps\r\n\r\nexport type MapDispatchToPropsFactory<TDispatchProps, TOwnProps> = (\r\n  dispatch: Dispatch<Action<string>>,\r\n  ownProps: TOwnProps\r\n) => MapDispatchToPropsFunction<TDispatchProps, TOwnProps>\r\n\r\nexport type MapDispatchToPropsParam<TDispatchProps, TOwnProps> =\r\n  | MapDispatchToPropsFactory<TDispatchProps, TOwnProps>\r\n  | MapDispatchToProps<TDispatchProps, TOwnProps>\r\n\r\nexport type MapDispatchToPropsNonObject<TDispatchProps, TOwnProps> =\r\n  | MapDispatchToPropsFactory<TDispatchProps, TOwnProps>\r\n  | MapDispatchToPropsFunction<TDispatchProps, TOwnProps>\r\n\r\nexport type MergeProps<TStateProps, TDispatchProps, TOwnProps, TMergedProps> = (\r\n  stateProps: TStateProps,\r\n  dispatchProps: TDispatchProps,\r\n  ownProps: TOwnProps\r\n) => TMergedProps\r\n\r\ninterface PureSelectorFactoryComparisonOptions<TStateProps, TOwnProps, State> {\r\n  readonly areStatesEqual: ExtendedEqualityFn<State, TOwnProps>\r\n  readonly areStatePropsEqual: EqualityFn<TStateProps>\r\n  readonly areOwnPropsEqual: EqualityFn<TOwnProps>\r\n}\r\n\r\nexport function pureFinalPropsSelectorFactory<\r\n  TStateProps,\r\n  TOwnProps,\r\n  TDispatchProps,\r\n  TMergedProps,\r\n  State\r\n>(\r\n  mapStateToProps: WrappedMapStateToProps<TStateProps, TOwnProps, State>,\r\n  mapDispatchToProps: WrappedMapDispatchToProps<TDispatchProps, TOwnProps>,\r\n  mergeProps: MergeProps<TStateProps, TDispatchProps, TOwnProps, TMergedProps>,\r\n  dispatch: Dispatch<Action<string>>,\r\n  {\r\n    areStatesEqual,\r\n    areOwnPropsEqual,\r\n    areStatePropsEqual,\r\n  }: PureSelectorFactoryComparisonOptions<TStateProps, TOwnProps, State>\r\n) {\r\n  let hasRunAtLeastOnce = false\r\n  let state: State\r\n  let ownProps: TOwnProps\r\n  let stateProps: TStateProps\r\n  let dispatchProps: TDispatchProps\r\n  let mergedProps: TMergedProps\r\n\r\n  function handleFirstCall(firstState: State, firstOwnProps: TOwnProps) {\r\n    state = firstState\r\n    ownProps = firstOwnProps\r\n    stateProps = mapStateToProps(state, ownProps)\r\n    dispatchProps = mapDispatchToProps(dispatch, ownProps)\r\n    mergedProps = mergeProps(stateProps, dispatchProps, ownProps)\r\n    hasRunAtLeastOnce = true\r\n    return mergedProps\r\n  }\r\n\r\n  function handleNewPropsAndNewState() {\r\n    stateProps = mapStateToProps(state, ownProps)\r\n\r\n    if (mapDispatchToProps.dependsOnOwnProps)\r\n      dispatchProps = mapDispatchToProps(dispatch, ownProps)\r\n\r\n    mergedProps = mergeProps(stateProps, dispatchProps, ownProps)\r\n    return mergedProps\r\n  }\r\n\r\n  function handleNewProps() {\r\n    if (mapStateToProps.dependsOnOwnProps)\r\n      stateProps = mapStateToProps(state, ownProps)\r\n\r\n    if (mapDispatchToProps.dependsOnOwnProps)\r\n      dispatchProps = mapDispatchToProps(dispatch, ownProps)\r\n\r\n    mergedProps = mergeProps(stateProps, dispatchProps, ownProps)\r\n    return mergedProps\r\n  }\r\n\r\n  function handleNewState() {\r\n    const nextStateProps = mapStateToProps(state, ownProps)\r\n    const statePropsChanged = !areStatePropsEqual(nextStateProps, stateProps)\r\n    stateProps = nextStateProps\r\n\r\n    if (statePropsChanged)\r\n      mergedProps = mergeProps(stateProps, dispatchProps, ownProps)\r\n\r\n    return mergedProps\r\n  }\r\n\r\n  function handleSubsequentCalls(nextState: State, nextOwnProps: TOwnProps) {\r\n    const propsChanged = !areOwnPropsEqual(nextOwnProps, ownProps)\r\n    const stateChanged = !areStatesEqual(\r\n      nextState,\r\n      state,\r\n      nextOwnProps,\r\n      ownProps\r\n    )\r\n    state = nextState\r\n    ownProps = nextOwnProps\r\n\r\n    if (propsChanged && stateChanged) return handleNewPropsAndNewState()\r\n    if (propsChanged) return handleNewProps()\r\n    if (stateChanged) return handleNewState()\r\n    return mergedProps\r\n  }\r\n\r\n  return function pureFinalPropsSelector(\r\n    nextState: State,\r\n    nextOwnProps: TOwnProps\r\n  ) {\r\n    return hasRunAtLeastOnce\r\n      ? handleSubsequentCalls(nextState, nextOwnProps)\r\n      : handleFirstCall(nextState, nextOwnProps)\r\n  }\r\n}\r\n\r\ninterface WrappedMapStateToProps<TStateProps, TOwnProps, State> {\r\n  (state: State, ownProps: TOwnProps): TStateProps\r\n  readonly dependsOnOwnProps: boolean\r\n}\r\n\r\ninterface WrappedMapDispatchToProps<TDispatchProps, TOwnProps> {\r\n  (dispatch: Dispatch<Action<string>>, ownProps: TOwnProps): TDispatchProps\r\n  readonly dependsOnOwnProps: boolean\r\n}\r\n\r\nexport interface InitOptions<TStateProps, TOwnProps, TMergedProps, State>\r\n  extends PureSelectorFactoryComparisonOptions<TStateProps, TOwnProps, State> {\r\n  readonly shouldHandleStateChanges: boolean\r\n  readonly displayName: string\r\n  readonly wrappedComponentName: string\r\n  readonly WrappedComponent: ComponentType<TOwnProps>\r\n  readonly areMergedPropsEqual: EqualityFn<TMergedProps>\r\n}\r\n\r\nexport interface SelectorFactoryOptions<\r\n  TStateProps,\r\n  TOwnProps,\r\n  TDispatchProps,\r\n  TMergedProps,\r\n  State\r\n> extends InitOptions<TStateProps, TOwnProps, TMergedProps, State> {\r\n  readonly initMapStateToProps: (\r\n    dispatch: Dispatch<Action<string>>,\r\n    options: InitOptions<TStateProps, TOwnProps, TMergedProps, State>\r\n  ) => WrappedMapStateToProps<TStateProps, TOwnProps, State>\r\n  readonly initMapDispatchToProps: (\r\n    dispatch: Dispatch<Action<string>>,\r\n    options: InitOptions<TStateProps, TOwnProps, TMergedProps, State>\r\n  ) => WrappedMapDispatchToProps<TDispatchProps, TOwnProps>\r\n  readonly initMergeProps: (\r\n    dispatch: Dispatch<Action<string>>,\r\n    options: InitOptions<TStateProps, TOwnProps, TMergedProps, State>\r\n  ) => MergeProps<TStateProps, TDispatchProps, TOwnProps, TMergedProps>\r\n}\r\n\r\n// TODO: Add more comments\r\n\r\n// The selector returned by selectorFactory will memoize its results,\r\n// allowing connect's shouldComponentUpdate to return false if final\r\n// props have not changed.\r\n\r\nexport default function finalPropsSelectorFactory<\r\n  TStateProps,\r\n  TOwnProps,\r\n  TDispatchProps,\r\n  TMergedProps,\r\n  State\r\n>(\r\n  dispatch: Dispatch<Action<string>>,\r\n  {\r\n    initMapStateToProps,\r\n    initMapDispatchToProps,\r\n    initMergeProps,\r\n    ...options\r\n  }: SelectorFactoryOptions<\r\n    TStateProps,\r\n    TOwnProps,\r\n    TDispatchProps,\r\n    TMergedProps,\r\n    State\r\n  >\r\n) {\r\n  const mapStateToProps = initMapStateToProps(dispatch, options)\r\n  const mapDispatchToProps = initMapDispatchToProps(dispatch, options)\r\n  const mergeProps = initMergeProps(dispatch, options)\r\n\r\n  if (process.env.NODE_ENV !== 'production') {\r\n    verifySubselectors(mapStateToProps, mapDispatchToProps, mergeProps)\r\n  }\r\n\r\n  return pureFinalPropsSelectorFactory<\r\n    TStateProps,\r\n    TOwnProps,\r\n    TDispatchProps,\r\n    TMergedProps,\r\n    State\r\n  >(mapStateToProps, mapDispatchToProps, mergeProps, dispatch, options)\r\n}\r\n", "import type { ActionCreatorsMapObject, Dispatch } from 'redux'\r\n\r\nexport default function bindActionCreators(\r\n  actionCreators: ActionCreatorsMapObject,\r\n  dispatch: Dispatch\r\n): ActionCreatorsMapObject {\r\n  const boundActionCreators: ActionCreatorsMapObject = {}\r\n\r\n  for (const key in actionCreators) {\r\n    const actionCreator = actionCreators[key]\r\n    if (typeof actionCreator === 'function') {\r\n      boundActionCreators[key] = (...args) => dispatch(actionCreator(...args))\r\n    }\r\n  }\r\n  return boundActionCreators\r\n}\r\n", "/**\r\n * @param {any} obj The object to inspect.\r\n * @returns {boolean} True if the argument appears to be a plain object.\r\n */\r\nexport default function isPlainObject(obj: unknown) {\r\n  if (typeof obj !== 'object' || obj === null) return false\r\n\r\n  let proto = Object.getPrototypeOf(obj)\r\n  if (proto === null) return true\r\n\r\n  let baseProto = proto\r\n  while (Object.getPrototypeOf(baseProto) !== null) {\r\n    baseProto = Object.getPrototypeOf(baseProto)\r\n  }\r\n\r\n  return proto === baseProto\r\n}\r\n", "import isPlainObject from './isPlainObject'\r\nimport warning from './warning'\r\n\r\nexport default function verifyPlainObject(\r\n  value: unknown,\r\n  displayName: string,\r\n  methodName: string\r\n) {\r\n  if (!isPlainObject(value)) {\r\n    warning(\r\n      `${methodName}() in ${displayName} must return a plain object. Instead received ${value}.`\r\n    )\r\n  }\r\n}\r\n", "import type { ActionCreatorsMapObject, Dispatch, ActionCreator } from 'redux'\r\n\r\nimport type { FixTypeLater } from '../types'\r\nimport verifyPlainObject from '../utils/verifyPlainObject'\r\n\r\ntype AnyState = { [key: string]: any }\r\ntype StateOrDispatch<S extends AnyState = AnyState> = S | Dispatch\r\n\r\ntype AnyProps = { [key: string]: any }\r\n\r\nexport type MapToProps<P extends AnyProps = AnyProps> = {\r\n  // eslint-disable-next-line no-unused-vars\r\n  (stateOrDispatch: StateOrDispatch, ownProps?: P): FixTypeLater\r\n  dependsOnOwnProps?: boolean\r\n}\r\n\r\nexport function wrapMapToPropsConstant(\r\n  // * Note:\r\n  //  It seems that the dispatch argument\r\n  //  could be a dispatch function in some cases (ex: whenMapDispatchToPropsIsMissing)\r\n  //  and a state object in some others (ex: whenMapStateToPropsIsMissing)\r\n  // eslint-disable-next-line no-unused-vars\r\n  getConstant: (dispatch: Dispatch) =>\r\n    | {\r\n        dispatch?: Dispatch\r\n        dependsOnOwnProps?: boolean\r\n      }\r\n    | ActionCreatorsMapObject\r\n    | ActionCreator<any>\r\n) {\r\n  return function initConstantSelector(dispatch: Dispatch) {\r\n    const constant = getConstant(dispatch)\r\n\r\n    function constantSelector() {\r\n      return constant\r\n    }\r\n    constantSelector.dependsOnOwnProps = false\r\n    return constantSelector\r\n  }\r\n}\r\n\r\n// dependsOnOwnProps is used by createMapToPropsProxy to determine whether to pass props as args\r\n// to the mapToProps function being wrapped. It is also used by makePurePropsSelector to determine\r\n// whether mapToProps needs to be invoked when props have changed.\r\n//\r\n// A length of one signals that mapToProps does not depend on props from the parent component.\r\n// A length of zero is assumed to mean mapToProps is getting args via arguments or ...args and\r\n// therefore not reporting its length accurately..\r\n// TODO Can this get pulled out so that we can subscribe directly to the store if we don't need ownProps?\r\nexport function getDependsOnOwnProps(mapToProps: MapToProps) {\r\n  return mapToProps.dependsOnOwnProps\r\n    ? Boolean(mapToProps.dependsOnOwnProps)\r\n    : mapToProps.length !== 1\r\n}\r\n\r\n// Used by whenMapStateToPropsIsFunction and whenMapDispatchToPropsIsFunction,\r\n// this function wraps mapToProps in a proxy function which does several things:\r\n//\r\n//  * Detects whether the mapToProps function being called depends on props, which\r\n//    is used by selectorFactory to decide if it should reinvoke on props changes.\r\n//\r\n//  * On first call, handles mapToProps if returns another function, and treats that\r\n//    new function as the true mapToProps for subsequent calls.\r\n//\r\n//  * On first call, verifies the first result is a plain object, in order to warn\r\n//    the developer that their mapToProps function is not returning a valid result.\r\n//\r\nexport function wrapMapToPropsFunc<P extends AnyProps = AnyProps>(\r\n  mapToProps: MapToProps,\r\n  methodName: string\r\n) {\r\n  return function initProxySelector(\r\n    dispatch: Dispatch,\r\n    { displayName }: { displayName: string }\r\n  ) {\r\n    const proxy = function mapToPropsProxy(\r\n      stateOrDispatch: StateOrDispatch,\r\n      ownProps?: P\r\n    ): MapToProps {\r\n      return proxy.dependsOnOwnProps\r\n        ? proxy.mapToProps(stateOrDispatch, ownProps)\r\n        : proxy.mapToProps(stateOrDispatch, undefined)\r\n    }\r\n\r\n    // allow detectFactoryAndVerify to get ownProps\r\n    proxy.dependsOnOwnProps = true\r\n\r\n    proxy.mapToProps = function detectFactoryAndVerify(\r\n      stateOrDispatch: StateOrDispatch,\r\n      ownProps?: P\r\n    ): MapToProps {\r\n      proxy.mapToProps = mapToProps\r\n      proxy.dependsOnOwnProps = getDependsOnOwnProps(mapToProps)\r\n      let props = proxy(stateOrDispatch, ownProps)\r\n\r\n      if (typeof props === 'function') {\r\n        proxy.mapToProps = props\r\n        proxy.dependsOnOwnProps = getDependsOnOwnProps(props)\r\n        props = proxy(stateOrDispatch, ownProps)\r\n      }\r\n\r\n      if (process.env.NODE_ENV !== 'production')\r\n        verifyPlainObject(props, displayName, methodName)\r\n\r\n      return props\r\n    }\r\n\r\n    return proxy\r\n  }\r\n}\r\n", "import type { Action, Dispatch } from 'redux'\r\n\r\nexport function createInvalidArgFactory(arg: unknown, name: string) {\r\n  return (\r\n    dispatch: Dispatch<Action<string>>,\r\n    options: { readonly wrappedComponentName: string }\r\n  ) => {\r\n    throw new Error(\r\n      `Invalid value of type ${typeof arg} for ${name} argument when connecting component ${\r\n        options.wrappedComponentName\r\n      }.`\r\n    )\r\n  }\r\n}\r\n", "import type { Action, Dispatch } from 'redux'\r\nimport bindActionCreators from '../utils/bindActionCreators'\r\nimport { wrapMapToPropsConstant, wrapMapToPropsFunc } from './wrapMapToProps'\r\nimport { createInvalidArgFactory } from './invalidArgFactory'\r\nimport type { MapDispatchToPropsParam } from './selectorFactory'\r\n\r\nexport function mapDispatchToPropsFactory<TDispatchProps, TOwnProps>(\r\n  mapDispatchToProps:\r\n    | MapDispatchToPropsParam<TDispatchProps, TOwnProps>\r\n    | undefined\r\n) {\r\n  return mapDispatchToProps && typeof mapDispatchToProps === 'object'\r\n    ? wrapMapToPropsConstant((dispatch: Dispatch<Action<string>>) =>\r\n        // @ts-ignore\r\n        bindActionCreators(mapDispatchToProps, dispatch)\r\n      )\r\n    : !mapDispatchToProps\r\n    ? wrapMapToPropsConstant((dispatch: Dispatch<Action<string>>) => ({\r\n        dispatch,\r\n      }))\r\n    : typeof mapDispatchToProps === 'function'\r\n    ? // @ts-ignore\r\n      wrapMapToPropsFunc(mapDispatchToProps, 'mapDispatchToProps')\r\n    : createInvalidArgFactory(mapDispatchToProps, 'mapDispatchToProps')\r\n}\r\n", "import { wrapMapToPropsConstant, wrapMapToPropsFunc } from './wrapMapToProps'\r\nimport { createInvalidArgFactory } from './invalidArgFactory'\r\nimport type { MapStateToPropsParam } from './selectorFactory'\r\n\r\nexport function mapStateToPropsFactory<TStateProps, TOwnProps, State>(\r\n  mapStateToProps: MapStateToPropsParam<TStateProps, TOwnProps, State>\r\n) {\r\n  return !mapStateToProps\r\n    ? wrapMapToPropsConstant(() => ({}))\r\n    : typeof mapStateToProps === 'function'\r\n    ? // @ts-ignore\r\n      wrapMapToPropsFunc(mapStateToProps, 'mapStateToProps')\r\n    : createInvalidArgFactory(mapStateToProps, 'mapStateToProps')\r\n}\r\n", "import type { Action, Dispatch } from 'redux'\r\nimport verifyPlainObject from '../utils/verifyPlainObject'\r\nimport { createInvalidArgFactory } from './invalidArgFactory'\r\nimport type { MergeProps } from './selectorFactory'\r\nimport type { EqualityFn } from '../types'\r\n\r\nexport function defaultMergeProps<\r\n  TStateProps,\r\n  TDispatchProps,\r\n  TOwnProps,\r\n  TMergedProps\r\n>(\r\n  stateProps: TStateProps,\r\n  dispatchProps: TDispatchProps,\r\n  ownProps: TOwnProps\r\n): TMergedProps {\r\n  // @ts-ignore\r\n  return { ...ownProps, ...stateProps, ...dispatchProps }\r\n}\r\n\r\nexport function wrapMergePropsFunc<\r\n  TStateProps,\r\n  TDispatchProps,\r\n  TOwnProps,\r\n  TMergedProps\r\n>(\r\n  mergeProps: MergeProps<TStateProps, TDispatchProps, TOwnProps, TMergedProps>\r\n): (\r\n  dispatch: Dispatch<Action<string>>,\r\n  options: {\r\n    readonly displayName: string\r\n    readonly areMergedPropsEqual: EqualityFn<TMergedProps>\r\n  }\r\n) => MergeProps<TStateProps, TDispatchProps, TOwnProps, TMergedProps> {\r\n  return function initMergePropsProxy(\r\n    dispatch,\r\n    { displayName, areMergedPropsEqual }\r\n  ) {\r\n    let hasRunOnce = false\r\n    let mergedProps: TMergedProps\r\n\r\n    return function mergePropsProxy(\r\n      stateProps: TStateProps,\r\n      dispatchProps: TDispatchProps,\r\n      ownProps: TOwnProps\r\n    ) {\r\n      const nextMergedProps = mergeProps(stateProps, dispatchProps, ownProps)\r\n\r\n      if (hasRunOnce) {\r\n        if (!areMergedPropsEqual(nextMergedProps, mergedProps))\r\n          mergedProps = nextMergedProps\r\n      } else {\r\n        hasRunOnce = true\r\n        mergedProps = nextMergedProps\r\n\r\n        if (process.env.NODE_ENV !== 'production')\r\n          verifyPlainObject(mergedProps, displayName, 'mergeProps')\r\n      }\r\n\r\n      return mergedProps\r\n    }\r\n  }\r\n}\r\n\r\nexport function mergePropsFactory<\r\n  TStateProps,\r\n  TDispatchProps,\r\n  TOwnProps,\r\n  TMergedProps\r\n>(\r\n  mergeProps?: MergeProps<TStateProps, TDispatchProps, TOwnProps, TMergedProps>\r\n) {\r\n  return !mergeProps\r\n    ? () => defaultMergeProps\r\n    : typeof mergeProps === 'function'\r\n    ? wrapMergePropsFunc(mergeProps)\r\n    : createInvalidArgFactory(mergeProps, 'mergeProps')\r\n}\r\n", "// Default to a dummy \"batch\" implementation that just runs the callback\r\nexport function defaultNoopBatch(callback: () => void) {\r\n  callback()\r\n}\r\n", "import { defaultNoopBatch as batch } from './batch'\r\n\r\n// encapsulates the subscription logic for connecting a component to the redux store, as\r\n// well as nesting subscriptions of descendant components, so that we can ensure the\r\n// ancestor components re-render before descendants\r\n\r\ntype VoidFunc = () => void\r\n\r\ntype Listener = {\r\n  callback: VoidFunc\r\n  next: Listener | null\r\n  prev: Listener | null\r\n}\r\n\r\nfunction createListenerCollection() {\r\n  let first: Listener | null = null\r\n  let last: Listener | null = null\r\n\r\n  return {\r\n    clear() {\r\n      first = null\r\n      last = null\r\n    },\r\n\r\n    notify() {\r\n      batch(() => {\r\n        let listener = first\r\n        while (listener) {\r\n          listener.callback()\r\n          listener = listener.next\r\n        }\r\n      })\r\n    },\r\n\r\n    get() {\r\n      let listeners: Listener[] = []\r\n      let listener = first\r\n      while (listener) {\r\n        listeners.push(listener)\r\n        listener = listener.next\r\n      }\r\n      return listeners\r\n    },\r\n\r\n    subscribe(callback: () => void) {\r\n      let isSubscribed = true\r\n\r\n      let listener: Listener = (last = {\r\n        callback,\r\n        next: null,\r\n        prev: last,\r\n      })\r\n\r\n      if (listener.prev) {\r\n        listener.prev.next = listener\r\n      } else {\r\n        first = listener\r\n      }\r\n\r\n      return function unsubscribe() {\r\n        if (!isSubscribed || first === null) return\r\n        isSubscribed = false\r\n\r\n        if (listener.next) {\r\n          listener.next.prev = listener.prev\r\n        } else {\r\n          last = listener.prev\r\n        }\r\n        if (listener.prev) {\r\n          listener.prev.next = listener.next\r\n        } else {\r\n          first = listener.next\r\n        }\r\n      }\r\n    },\r\n  }\r\n}\r\n\r\ntype ListenerCollection = ReturnType<typeof createListenerCollection>\r\n\r\nexport interface Subscription {\r\n  addNestedSub: (listener: VoidFunc) => VoidFunc\r\n  notifyNestedSubs: VoidFunc\r\n  handleChangeWrapper: VoidFunc\r\n  isSubscribed: () => boolean\r\n  onStateChange?: VoidFunc | null\r\n  trySubscribe: VoidFunc\r\n  tryUnsubscribe: VoidFunc\r\n  getListeners: () => ListenerCollection\r\n}\r\n\r\nconst nullListeners = {\r\n  notify() {},\r\n  get: () => [],\r\n} as unknown as ListenerCollection\r\n\r\nexport function createSubscription(store: any, parentSub?: Subscription) {\r\n  let unsubscribe: VoidFunc | undefined\r\n  let listeners: ListenerCollection = nullListeners\r\n\r\n  // Reasons to keep the subscription active\r\n  let subscriptionsAmount = 0\r\n\r\n  // Is this specific subscription subscribed (or only nested ones?)\r\n  let selfSubscribed = false\r\n\r\n  function addNestedSub(listener: () => void) {\r\n    trySubscribe()\r\n\r\n    const cleanupListener = listeners.subscribe(listener)\r\n\r\n    // cleanup nested sub\r\n    let removed = false\r\n    return () => {\r\n      if (!removed) {\r\n        removed = true\r\n        cleanupListener()\r\n        tryUnsubscribe()\r\n      }\r\n    }\r\n  }\r\n\r\n  function notifyNestedSubs() {\r\n    listeners.notify()\r\n  }\r\n\r\n  function handleChangeWrapper() {\r\n    if (subscription.onStateChange) {\r\n      subscription.onStateChange()\r\n    }\r\n  }\r\n\r\n  function isSubscribed() {\r\n    return selfSubscribed\r\n  }\r\n\r\n  function trySubscribe() {\r\n    subscriptionsAmount++\r\n    if (!unsubscribe) {\r\n      unsubscribe = parentSub\r\n        ? parentSub.addNestedSub(handleChangeWrapper)\r\n        : store.subscribe(handleChangeWrapper)\r\n\r\n      listeners = createListenerCollection()\r\n    }\r\n  }\r\n\r\n  function tryUnsubscribe() {\r\n    subscriptionsAmount--\r\n    if (unsubscribe && subscriptionsAmount === 0) {\r\n      unsubscribe()\r\n      unsubscribe = undefined\r\n      listeners.clear()\r\n      listeners = nullListeners\r\n    }\r\n  }\r\n\r\n  function trySubscribeSelf() {\r\n    if (!selfSubscribed) {\r\n      selfSubscribed = true\r\n      trySubscribe()\r\n    }\r\n  }\r\n\r\n  function tryUnsubscribeSelf() {\r\n    if (selfSubscribed) {\r\n      selfSubscribed = false\r\n      tryUnsubscribe()\r\n    }\r\n  }\r\n\r\n  const subscription: Subscription = {\r\n    addNestedSub,\r\n    notifyNestedSubs,\r\n    handleChangeWrapper,\r\n    isSubscribed,\r\n    trySubscribe: trySubscribeSelf,\r\n    tryUnsubscribe: tryUnsubscribeSelf,\r\n    getListeners: () => listeners,\r\n  }\r\n\r\n  return subscription\r\n}\r\n", "import { React } from '../utils/react'\r\n\r\n// <PERSON><PERSON> currently throws a warning when using useLayoutEffect on the server.\r\n// To get around it, we can conditionally useEffect on the server (no-op) and\r\n// useLayoutEffect in the browser. We need useLayoutEffect to ensure the store\r\n// subscription callback always has the selector from the latest render commit\r\n// available, otherwise a store update may happen between render and the effect,\r\n// which may cause missed updates; we also must ensure the store subscription\r\n// is created synchronously, otherwise a store update may occur before the\r\n// subscription is created and an inconsistent state may be observed\r\n\r\n// Matches logic in React's `shared/ExecutionEnvironment` file\r\nexport const canUseDOM = !!(\r\n  typeof window !== 'undefined' &&\r\n  typeof window.document !== 'undefined' &&\r\n  typeof window.document.createElement !== 'undefined'\r\n)\r\n\r\nexport const useIsomorphicLayoutEffect = canUseDOM\r\n  ? React.useLayoutEffect\r\n  : React.useEffect\r\n", "function is(x: unknown, y: unknown) {\r\n  if (x === y) {\r\n    return x !== 0 || y !== 0 || 1 / x === 1 / y\r\n  } else {\r\n    return x !== x && y !== y\r\n  }\r\n}\r\n\r\nexport default function shallowEqual(objA: any, objB: any) {\r\n  if (is(objA, objB)) return true\r\n\r\n  if (\r\n    typeof objA !== 'object' ||\r\n    objA === null ||\r\n    typeof objB !== 'object' ||\r\n    objB === null\r\n  ) {\r\n    return false\r\n  }\r\n\r\n  const keysA = Object.keys(objA)\r\n  const keysB = Object.keys(objB)\r\n\r\n  if (keysA.length !== keysB.length) return false\r\n\r\n  for (let i = 0; i < keysA.length; i++) {\r\n    if (\r\n      !Object.prototype.hasOwnProperty.call(objB, keysA[i]) ||\r\n      !is(objA[keysA[i]], objB[keysA[i]])\r\n    ) {\r\n      return false\r\n    }\r\n  }\r\n\r\n  return true\r\n}\r\n", "// Copied directly from:\r\n// https://github.com/mridgway/hoist-non-react-statics/blob/main/src/index.js\r\n// https://unpkg.com/browse/@types/hoist-non-react-statics@3.3.1/index.d.ts\r\n\r\n/**\r\n * Copyright 2015, Yahoo! Inc.\r\n * Copyrights licensed under the New BSD License. See the accompanying LICENSE file for terms.\r\n */\r\nimport type * as React from 'react'\r\nimport { ForwardRef, Memo, isMemo } from '../utils/react-is'\r\n\r\nconst REACT_STATICS = {\r\n  childContextTypes: true,\r\n  contextType: true,\r\n  contextTypes: true,\r\n  defaultProps: true,\r\n  displayName: true,\r\n  getDefaultProps: true,\r\n  getDerivedStateFromError: true,\r\n  getDerivedStateFromProps: true,\r\n  mixins: true,\r\n  propTypes: true,\r\n  type: true,\r\n} as const\r\n\r\nconst KNOWN_STATICS = {\r\n  name: true,\r\n  length: true,\r\n  prototype: true,\r\n  caller: true,\r\n  callee: true,\r\n  arguments: true,\r\n  arity: true,\r\n} as const\r\n\r\nconst FORWARD_REF_STATICS = {\r\n  $$typeof: true,\r\n  render: true,\r\n  defaultProps: true,\r\n  displayName: true,\r\n  propTypes: true,\r\n} as const\r\n\r\nconst MEMO_STATICS = {\r\n  $$typeof: true,\r\n  compare: true,\r\n  defaultProps: true,\r\n  displayName: true,\r\n  propTypes: true,\r\n  type: true,\r\n} as const\r\n\r\nconst TYPE_STATICS = {\r\n  [ForwardRef]: FORWARD_REF_STATICS,\r\n  [Memo]: MEMO_STATICS,\r\n} as const\r\n\r\nfunction getStatics(component: any) {\r\n  // React v16.11 and below\r\n  if (isMemo(component)) {\r\n    return MEMO_STATICS\r\n  }\r\n\r\n  // React v16.12 and above\r\n  return TYPE_STATICS[component['$$typeof']] || REACT_STATICS\r\n}\r\n\r\nexport type NonReactStatics<\r\n  S extends React.ComponentType<any>,\r\n  C extends {\r\n    [key: string]: true\r\n  } = {}\r\n> = {\r\n  [key in Exclude<\r\n    keyof S,\r\n    S extends React.MemoExoticComponent<any>\r\n      ? keyof typeof MEMO_STATICS | keyof C\r\n      : S extends React.ForwardRefExoticComponent<any>\r\n      ? keyof typeof FORWARD_REF_STATICS | keyof C\r\n      : keyof typeof REACT_STATICS | keyof typeof KNOWN_STATICS | keyof C\r\n  >]: S[key]\r\n}\r\n\r\nconst defineProperty = Object.defineProperty\r\nconst getOwnPropertyNames = Object.getOwnPropertyNames\r\nconst getOwnPropertySymbols = Object.getOwnPropertySymbols\r\nconst getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor\r\nconst getPrototypeOf = Object.getPrototypeOf\r\nconst objectPrototype = Object.prototype\r\n\r\nexport default function hoistNonReactStatics<\r\n  T extends React.ComponentType<any>,\r\n  S extends React.ComponentType<any>,\r\n  C extends {\r\n    [key: string]: true\r\n  } = {}\r\n>(targetComponent: T, sourceComponent: S): T & NonReactStatics<S, C> {\r\n  if (typeof sourceComponent !== 'string') {\r\n    // don't hoist over string (html) components\r\n\r\n    if (objectPrototype) {\r\n      const inheritedComponent = getPrototypeOf(sourceComponent)\r\n      if (inheritedComponent && inheritedComponent !== objectPrototype) {\r\n        hoistNonReactStatics(targetComponent, inheritedComponent)\r\n      }\r\n    }\r\n\r\n    let keys: (string | symbol)[] = getOwnPropertyNames(sourceComponent)\r\n\r\n    if (getOwnPropertySymbols) {\r\n      keys = keys.concat(getOwnPropertySymbols(sourceComponent))\r\n    }\r\n\r\n    const targetStatics = getStatics(targetComponent)\r\n    const sourceStatics = getStatics(sourceComponent)\r\n\r\n    for (let i = 0; i < keys.length; ++i) {\r\n      const key = keys[i]\r\n      if (\r\n        !KNOWN_STATICS[key as keyof typeof KNOWN_STATICS] &&\r\n        !(sourceStatics && sourceStatics[key as keyof typeof sourceStatics]) &&\r\n        !(targetStatics && targetStatics[key as keyof typeof targetStatics])\r\n      ) {\r\n        const descriptor = getOwnPropertyDescriptor(sourceComponent, key)\r\n        try {\r\n          // Avoid failures from read-only properties\r\n          defineProperty(targetComponent, key, descriptor!)\r\n        } catch (e) {\r\n          // ignore\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  return targetComponent as any\r\n}\r\n", "/* eslint-disable valid-jsdoc, @typescript-eslint/no-unused-vars */\r\nimport type { ComponentType } from 'react'\r\nimport { React } from '../utils/react'\r\nimport { isValidElementType, isContextConsumer } from '../utils/react-is'\r\n\r\nimport type { Store } from 'redux'\r\n\r\nimport type {\r\n  ConnectedComponent,\r\n  InferableComponentEnhancer,\r\n  InferableComponentEnhancerWithProps,\r\n  ResolveThunks,\r\n  DispatchProp,\r\n  ConnectPropsMaybeWithoutContext,\r\n} from '../types'\r\n\r\nimport type {\r\n  MapStateToPropsParam,\r\n  MapDispatchToPropsParam,\r\n  MergeProps,\r\n  MapDispatchToPropsNonObject,\r\n  SelectorFactoryOptions,\r\n} from '../connect/selectorFactory'\r\nimport defaultSelectorFactory from '../connect/selectorFactory'\r\nimport { mapDispatchToPropsFactory } from '../connect/mapDispatchToProps'\r\nimport { mapStateToPropsFactory } from '../connect/mapStateToProps'\r\nimport { mergePropsFactory } from '../connect/mergeProps'\r\n\r\nimport type { Subscription } from '../utils/Subscription'\r\nimport { createSubscription } from '../utils/Subscription'\r\nimport { useIsomorphicLayoutEffect } from '../utils/useIsomorphicLayoutEffect'\r\nimport shallowEqual from '../utils/shallowEqual'\r\nimport hoistStatics from '../utils/hoistStatics'\r\nimport warning from '../utils/warning'\r\n\r\nimport type {\r\n  ReactReduxContextValue,\r\n  ReactReduxContextInstance,\r\n} from './Context'\r\nimport { ReactReduxContext } from './Context'\r\n\r\nimport type { uSES } from '../utils/useSyncExternalStore'\r\nimport { notInitialized } from '../utils/useSyncExternalStore'\r\n\r\nlet useSyncExternalStore = notInitialized as uSES\r\nexport const initializeConnect = (fn: uSES) => {\r\n  useSyncExternalStore = fn\r\n}\r\n\r\n// Define some constant arrays just to avoid re-creating these\r\nconst EMPTY_ARRAY: [unknown, number] = [null, 0]\r\nconst NO_SUBSCRIPTION_ARRAY = [null, null]\r\n\r\n// Attempts to stringify whatever not-really-a-component value we were given\r\n// for logging in an error message\r\nconst stringifyComponent = (Comp: unknown) => {\r\n  try {\r\n    return JSON.stringify(Comp)\r\n  } catch (err) {\r\n    return String(Comp)\r\n  }\r\n}\r\n\r\ntype EffectFunc = (...args: any[]) => void | ReturnType<React.EffectCallback>\r\n\r\n// This is \"just\" a `useLayoutEffect`, but with two modifications:\r\n// - we need to fall back to `useEffect` in SSR to avoid annoying warnings\r\n// - we extract this to a separate function to avoid closing over values\r\n//   and causing memory leaks\r\nfunction useIsomorphicLayoutEffectWithArgs(\r\n  effectFunc: EffectFunc,\r\n  effectArgs: any[],\r\n  dependencies?: React.DependencyList\r\n) {\r\n  useIsomorphicLayoutEffect(() => effectFunc(...effectArgs), dependencies)\r\n}\r\n\r\n// Effect callback, extracted: assign the latest props values to refs for later usage\r\nfunction captureWrapperProps(\r\n  lastWrapperProps: React.MutableRefObject<unknown>,\r\n  lastChildProps: React.MutableRefObject<unknown>,\r\n  renderIsScheduled: React.MutableRefObject<boolean>,\r\n  wrapperProps: unknown,\r\n  // actualChildProps: unknown,\r\n  childPropsFromStoreUpdate: React.MutableRefObject<unknown>,\r\n  notifyNestedSubs: () => void\r\n) {\r\n  // We want to capture the wrapper props and child props we used for later comparisons\r\n  lastWrapperProps.current = wrapperProps\r\n  renderIsScheduled.current = false\r\n\r\n  // If the render was from a store update, clear out that reference and cascade the subscriber update\r\n  if (childPropsFromStoreUpdate.current) {\r\n    childPropsFromStoreUpdate.current = null\r\n    notifyNestedSubs()\r\n  }\r\n}\r\n\r\n// Effect callback, extracted: subscribe to the Redux store or nearest connected ancestor,\r\n// check for updates after dispatched actions, and trigger re-renders.\r\nfunction subscribeUpdates(\r\n  shouldHandleStateChanges: boolean,\r\n  store: Store,\r\n  subscription: Subscription,\r\n  childPropsSelector: (state: unknown, props: unknown) => unknown,\r\n  lastWrapperProps: React.MutableRefObject<unknown>,\r\n  lastChildProps: React.MutableRefObject<unknown>,\r\n  renderIsScheduled: React.MutableRefObject<boolean>,\r\n  isMounted: React.MutableRefObject<boolean>,\r\n  childPropsFromStoreUpdate: React.MutableRefObject<unknown>,\r\n  notifyNestedSubs: () => void,\r\n  // forceComponentUpdateDispatch: React.Dispatch<any>,\r\n  additionalSubscribeListener: () => void\r\n) {\r\n  // If we're not subscribed to the store, nothing to do here\r\n  if (!shouldHandleStateChanges) return () => {}\r\n\r\n  // Capture values for checking if and when this component unmounts\r\n  let didUnsubscribe = false\r\n  let lastThrownError: Error | null = null\r\n\r\n  // We'll run this callback every time a store subscription update propagates to this component\r\n  const checkForUpdates = () => {\r\n    if (didUnsubscribe || !isMounted.current) {\r\n      // Don't run stale listeners.\r\n      // Redux doesn't guarantee unsubscriptions happen until next dispatch.\r\n      return\r\n    }\r\n\r\n    // TODO We're currently calling getState ourselves here, rather than letting `uSES` do it\r\n    const latestStoreState = store.getState()\r\n\r\n    let newChildProps, error\r\n    try {\r\n      // Actually run the selector with the most recent store state and wrapper props\r\n      // to determine what the child props should be\r\n      newChildProps = childPropsSelector(\r\n        latestStoreState,\r\n        lastWrapperProps.current\r\n      )\r\n    } catch (e) {\r\n      error = e\r\n      lastThrownError = e as Error | null\r\n    }\r\n\r\n    if (!error) {\r\n      lastThrownError = null\r\n    }\r\n\r\n    // If the child props haven't changed, nothing to do here - cascade the subscription update\r\n    if (newChildProps === lastChildProps.current) {\r\n      if (!renderIsScheduled.current) {\r\n        notifyNestedSubs()\r\n      }\r\n    } else {\r\n      // Save references to the new child props.  Note that we track the \"child props from store update\"\r\n      // as a ref instead of a useState/useReducer because we need a way to determine if that value has\r\n      // been processed.  If this went into useState/useReducer, we couldn't clear out the value without\r\n      // forcing another re-render, which we don't want.\r\n      lastChildProps.current = newChildProps\r\n      childPropsFromStoreUpdate.current = newChildProps\r\n      renderIsScheduled.current = true\r\n\r\n      // TODO This is hacky and not how `uSES` is meant to be used\r\n      // Trigger the React `useSyncExternalStore` subscriber\r\n      additionalSubscribeListener()\r\n    }\r\n  }\r\n\r\n  // Actually subscribe to the nearest connected ancestor (or store)\r\n  subscription.onStateChange = checkForUpdates\r\n  subscription.trySubscribe()\r\n\r\n  // Pull data from the store after first render in case the store has\r\n  // changed since we began.\r\n  checkForUpdates()\r\n\r\n  const unsubscribeWrapper = () => {\r\n    didUnsubscribe = true\r\n    subscription.tryUnsubscribe()\r\n    subscription.onStateChange = null\r\n\r\n    if (lastThrownError) {\r\n      // It's possible that we caught an error due to a bad mapState function, but the\r\n      // parent re-rendered without this component and we're about to unmount.\r\n      // This shouldn't happen as long as we do top-down subscriptions correctly, but\r\n      // if we ever do those wrong, this throw will surface the error in our tests.\r\n      // In that case, throw the error from here so it doesn't get lost.\r\n      throw lastThrownError\r\n    }\r\n  }\r\n\r\n  return unsubscribeWrapper\r\n}\r\n\r\n// Reducer initial state creation for our update reducer\r\nconst initStateUpdates = () => EMPTY_ARRAY\r\n\r\nexport interface ConnectProps {\r\n  /** A custom Context instance that the component can use to access the store from an alternate Provider using that same Context instance */\r\n  context?: ReactReduxContextInstance\r\n  /** A Redux store instance to be used for subscriptions instead of the store from a Provider */\r\n  store?: Store\r\n}\r\n\r\ninterface InternalConnectProps extends ConnectProps {\r\n  reactReduxForwardedRef?: React.ForwardedRef<unknown>\r\n}\r\n\r\nfunction strictEqual(a: unknown, b: unknown) {\r\n  return a === b\r\n}\r\n\r\n/**\r\n * Infers the type of props that a connector will inject into a component.\r\n */\r\nexport type ConnectedProps<TConnector> =\r\n  TConnector extends InferableComponentEnhancerWithProps<\r\n    infer TInjectedProps,\r\n    any\r\n  >\r\n    ? unknown extends TInjectedProps\r\n      ? TConnector extends InferableComponentEnhancer<infer TInjectedProps>\r\n        ? TInjectedProps\r\n        : never\r\n      : TInjectedProps\r\n    : never\r\n\r\nexport interface ConnectOptions<\r\n  State = unknown,\r\n  TStateProps = {},\r\n  TOwnProps = {},\r\n  TMergedProps = {}\r\n> {\r\n  forwardRef?: boolean\r\n  context?: typeof ReactReduxContext\r\n  areStatesEqual?: (\r\n    nextState: State,\r\n    prevState: State,\r\n    nextOwnProps: TOwnProps,\r\n    prevOwnProps: TOwnProps\r\n  ) => boolean\r\n\r\n  areOwnPropsEqual?: (\r\n    nextOwnProps: TOwnProps,\r\n    prevOwnProps: TOwnProps\r\n  ) => boolean\r\n\r\n  areStatePropsEqual?: (\r\n    nextStateProps: TStateProps,\r\n    prevStateProps: TStateProps\r\n  ) => boolean\r\n  areMergedPropsEqual?: (\r\n    nextMergedProps: TMergedProps,\r\n    prevMergedProps: TMergedProps\r\n  ) => boolean\r\n}\r\n\r\n/**\r\n * Connects a React component to a Redux store.\r\n *\r\n * - Without arguments, just wraps the component, without changing the behavior / props\r\n *\r\n * - If 2 params are passed (3rd param, mergeProps, is skipped), default behavior\r\n * is to override ownProps (as stated in the docs), so what remains is everything that's\r\n * not a state or dispatch prop\r\n *\r\n * - When 3rd param is passed, we don't know if ownProps propagate and whether they\r\n * should be valid component props, because it depends on mergeProps implementation.\r\n * As such, it is the user's responsibility to extend ownProps interface from state or\r\n * dispatch props or both when applicable\r\n *\r\n * @param mapStateToProps\r\n * @param mapDispatchToProps\r\n * @param mergeProps\r\n * @param options\r\n */\r\nexport interface Connect<DefaultState = unknown> {\r\n  // tslint:disable:no-unnecessary-generics\r\n  (): InferableComponentEnhancer<DispatchProp>\r\n\r\n  /** mapState only */\r\n  <TStateProps = {}, no_dispatch = {}, TOwnProps = {}, State = DefaultState>(\r\n    mapStateToProps: MapStateToPropsParam<TStateProps, TOwnProps, State>\r\n  ): InferableComponentEnhancerWithProps<TStateProps & DispatchProp, TOwnProps>\r\n\r\n  /** mapDispatch only (as a function) */\r\n  <no_state = {}, TDispatchProps = {}, TOwnProps = {}>(\r\n    mapStateToProps: null | undefined,\r\n    mapDispatchToProps: MapDispatchToPropsNonObject<TDispatchProps, TOwnProps>\r\n  ): InferableComponentEnhancerWithProps<TDispatchProps, TOwnProps>\r\n\r\n  /** mapDispatch only (as an object) */\r\n  <no_state = {}, TDispatchProps = {}, TOwnProps = {}>(\r\n    mapStateToProps: null | undefined,\r\n    mapDispatchToProps: MapDispatchToPropsParam<TDispatchProps, TOwnProps>\r\n  ): InferableComponentEnhancerWithProps<\r\n    ResolveThunks<TDispatchProps>,\r\n    TOwnProps\r\n  >\r\n\r\n  /** mapState and mapDispatch (as a function)*/\r\n  <TStateProps = {}, TDispatchProps = {}, TOwnProps = {}, State = DefaultState>(\r\n    mapStateToProps: MapStateToPropsParam<TStateProps, TOwnProps, State>,\r\n    mapDispatchToProps: MapDispatchToPropsNonObject<TDispatchProps, TOwnProps>\r\n  ): InferableComponentEnhancerWithProps<\r\n    TStateProps & TDispatchProps,\r\n    TOwnProps\r\n  >\r\n\r\n  /** mapState and mapDispatch (nullish) */\r\n  <TStateProps = {}, TDispatchProps = {}, TOwnProps = {}, State = DefaultState>(\r\n    mapStateToProps: MapStateToPropsParam<TStateProps, TOwnProps, State>,\r\n    mapDispatchToProps: null | undefined\r\n  ): InferableComponentEnhancerWithProps<TStateProps, TOwnProps>\r\n\r\n  /** mapState and mapDispatch (as an object) */\r\n  <TStateProps = {}, TDispatchProps = {}, TOwnProps = {}, State = DefaultState>(\r\n    mapStateToProps: MapStateToPropsParam<TStateProps, TOwnProps, State>,\r\n    mapDispatchToProps: MapDispatchToPropsParam<TDispatchProps, TOwnProps>\r\n  ): InferableComponentEnhancerWithProps<\r\n    TStateProps & ResolveThunks<TDispatchProps>,\r\n    TOwnProps\r\n  >\r\n\r\n  /** mergeProps only */\r\n  <no_state = {}, no_dispatch = {}, TOwnProps = {}, TMergedProps = {}>(\r\n    mapStateToProps: null | undefined,\r\n    mapDispatchToProps: null | undefined,\r\n    mergeProps: MergeProps<undefined, DispatchProp, TOwnProps, TMergedProps>\r\n  ): InferableComponentEnhancerWithProps<TMergedProps, TOwnProps>\r\n\r\n  /** mapState and mergeProps */\r\n  <\r\n    TStateProps = {},\r\n    no_dispatch = {},\r\n    TOwnProps = {},\r\n    TMergedProps = {},\r\n    State = DefaultState\r\n  >(\r\n    mapStateToProps: MapStateToPropsParam<TStateProps, TOwnProps, State>,\r\n    mapDispatchToProps: null | undefined,\r\n    mergeProps: MergeProps<TStateProps, DispatchProp, TOwnProps, TMergedProps>\r\n  ): InferableComponentEnhancerWithProps<TMergedProps, TOwnProps>\r\n\r\n  /** mapDispatch (as a object) and mergeProps */\r\n  <no_state = {}, TDispatchProps = {}, TOwnProps = {}, TMergedProps = {}>(\r\n    mapStateToProps: null | undefined,\r\n    mapDispatchToProps: MapDispatchToPropsParam<TDispatchProps, TOwnProps>,\r\n    mergeProps: MergeProps<undefined, TDispatchProps, TOwnProps, TMergedProps>\r\n  ): InferableComponentEnhancerWithProps<TMergedProps, TOwnProps>\r\n\r\n  /** mapState and options */\r\n  <TStateProps = {}, no_dispatch = {}, TOwnProps = {}, State = DefaultState>(\r\n    mapStateToProps: MapStateToPropsParam<TStateProps, TOwnProps, State>,\r\n    mapDispatchToProps: null | undefined,\r\n    mergeProps: null | undefined,\r\n    options: ConnectOptions<State, TStateProps, TOwnProps>\r\n  ): InferableComponentEnhancerWithProps<DispatchProp & TStateProps, TOwnProps>\r\n\r\n  /** mapDispatch (as a function) and options */\r\n  <TStateProps = {}, TDispatchProps = {}, TOwnProps = {}>(\r\n    mapStateToProps: null | undefined,\r\n    mapDispatchToProps: MapDispatchToPropsNonObject<TDispatchProps, TOwnProps>,\r\n    mergeProps: null | undefined,\r\n    options: ConnectOptions<{}, TStateProps, TOwnProps>\r\n  ): InferableComponentEnhancerWithProps<TDispatchProps, TOwnProps>\r\n\r\n  /** mapDispatch (as an object) and options*/\r\n  <TStateProps = {}, TDispatchProps = {}, TOwnProps = {}>(\r\n    mapStateToProps: null | undefined,\r\n    mapDispatchToProps: MapDispatchToPropsParam<TDispatchProps, TOwnProps>,\r\n    mergeProps: null | undefined,\r\n    options: ConnectOptions<{}, TStateProps, TOwnProps>\r\n  ): InferableComponentEnhancerWithProps<\r\n    ResolveThunks<TDispatchProps>,\r\n    TOwnProps\r\n  >\r\n\r\n  /** mapState,  mapDispatch (as a function), and options */\r\n  <TStateProps = {}, TDispatchProps = {}, TOwnProps = {}, State = DefaultState>(\r\n    mapStateToProps: MapStateToPropsParam<TStateProps, TOwnProps, State>,\r\n    mapDispatchToProps: MapDispatchToPropsNonObject<TDispatchProps, TOwnProps>,\r\n    mergeProps: null | undefined,\r\n    options: ConnectOptions<State, TStateProps, TOwnProps>\r\n  ): InferableComponentEnhancerWithProps<\r\n    TStateProps & TDispatchProps,\r\n    TOwnProps\r\n  >\r\n\r\n  /** mapState,  mapDispatch (as an object), and options */\r\n  <TStateProps = {}, TDispatchProps = {}, TOwnProps = {}, State = DefaultState>(\r\n    mapStateToProps: MapStateToPropsParam<TStateProps, TOwnProps, State>,\r\n    mapDispatchToProps: MapDispatchToPropsParam<TDispatchProps, TOwnProps>,\r\n    mergeProps: null | undefined,\r\n    options: ConnectOptions<State, TStateProps, TOwnProps>\r\n  ): InferableComponentEnhancerWithProps<\r\n    TStateProps & ResolveThunks<TDispatchProps>,\r\n    TOwnProps\r\n  >\r\n\r\n  /** mapState, mapDispatch, mergeProps, and options */\r\n  <\r\n    TStateProps = {},\r\n    TDispatchProps = {},\r\n    TOwnProps = {},\r\n    TMergedProps = {},\r\n    State = DefaultState\r\n  >(\r\n    mapStateToProps: MapStateToPropsParam<TStateProps, TOwnProps, State>,\r\n    mapDispatchToProps: MapDispatchToPropsParam<TDispatchProps, TOwnProps>,\r\n    mergeProps: MergeProps<\r\n      TStateProps,\r\n      TDispatchProps,\r\n      TOwnProps,\r\n      TMergedProps\r\n    >,\r\n    options?: ConnectOptions<State, TStateProps, TOwnProps, TMergedProps>\r\n  ): InferableComponentEnhancerWithProps<TMergedProps, TOwnProps>\r\n  // tslint:enable:no-unnecessary-generics\r\n}\r\n\r\nlet hasWarnedAboutDeprecatedPureOption = false\r\n\r\n/**\r\n * Connects a React component to a Redux store.\r\n *\r\n * - Without arguments, just wraps the component, without changing the behavior / props\r\n *\r\n * - If 2 params are passed (3rd param, mergeProps, is skipped), default behavior\r\n * is to override ownProps (as stated in the docs), so what remains is everything that's\r\n * not a state or dispatch prop\r\n *\r\n * - When 3rd param is passed, we don't know if ownProps propagate and whether they\r\n * should be valid component props, because it depends on mergeProps implementation.\r\n * As such, it is the user's responsibility to extend ownProps interface from state or\r\n * dispatch props or both when applicable\r\n *\r\n * @param mapStateToProps A function that extracts values from state\r\n * @param mapDispatchToProps Setup for dispatching actions\r\n * @param mergeProps Optional callback to merge state and dispatch props together\r\n * @param options Options for configuring the connection\r\n *\r\n */\r\nfunction connect<\r\n  TStateProps = {},\r\n  TDispatchProps = {},\r\n  TOwnProps = {},\r\n  TMergedProps = {},\r\n  State = unknown\r\n>(\r\n  mapStateToProps?: MapStateToPropsParam<TStateProps, TOwnProps, State>,\r\n  mapDispatchToProps?: MapDispatchToPropsParam<TDispatchProps, TOwnProps>,\r\n  mergeProps?: MergeProps<TStateProps, TDispatchProps, TOwnProps, TMergedProps>,\r\n  {\r\n    // The `pure` option has been removed, so TS doesn't like us destructuring this to check its existence.\r\n    // @ts-ignore\r\n    pure,\r\n    areStatesEqual = strictEqual,\r\n    areOwnPropsEqual = shallowEqual,\r\n    areStatePropsEqual = shallowEqual,\r\n    areMergedPropsEqual = shallowEqual,\r\n\r\n    // use React's forwardRef to expose a ref of the wrapped component\r\n    forwardRef = false,\r\n\r\n    // the context consumer to use\r\n    context = ReactReduxContext,\r\n  }: ConnectOptions<unknown, unknown, unknown, unknown> = {}\r\n): unknown {\r\n  if (process.env.NODE_ENV !== 'production') {\r\n    if (pure !== undefined && !hasWarnedAboutDeprecatedPureOption) {\r\n      hasWarnedAboutDeprecatedPureOption = true\r\n      warning(\r\n        'The `pure` option has been removed. `connect` is now always a \"pure/memoized\" component'\r\n      )\r\n    }\r\n  }\r\n\r\n  const Context = context\r\n\r\n  const initMapStateToProps = mapStateToPropsFactory(mapStateToProps)\r\n  const initMapDispatchToProps = mapDispatchToPropsFactory(mapDispatchToProps)\r\n  const initMergeProps = mergePropsFactory(mergeProps)\r\n\r\n  const shouldHandleStateChanges = Boolean(mapStateToProps)\r\n\r\n  const wrapWithConnect = <TProps,>(\r\n    WrappedComponent: ComponentType<TProps>\r\n  ) => {\r\n    type WrappedComponentProps = TProps &\r\n      ConnectPropsMaybeWithoutContext<TProps>\r\n\r\n    if (process.env.NODE_ENV !== 'production') {\r\n      const isValid = /*#__PURE__*/ isValidElementType(WrappedComponent)\r\n      if (!isValid)\r\n        throw new Error(\r\n          `You must pass a component to the function returned by connect. Instead received ${stringifyComponent(\r\n            WrappedComponent\r\n          )}`\r\n        )\r\n    }\r\n\r\n    const wrappedComponentName =\r\n      WrappedComponent.displayName || WrappedComponent.name || 'Component'\r\n\r\n    const displayName = `Connect(${wrappedComponentName})`\r\n\r\n    const selectorFactoryOptions: SelectorFactoryOptions<\r\n      any,\r\n      any,\r\n      any,\r\n      any,\r\n      State\r\n    > = {\r\n      shouldHandleStateChanges,\r\n      displayName,\r\n      wrappedComponentName,\r\n      WrappedComponent,\r\n      // @ts-ignore\r\n      initMapStateToProps,\r\n      // @ts-ignore\r\n      initMapDispatchToProps,\r\n      initMergeProps,\r\n      areStatesEqual,\r\n      areStatePropsEqual,\r\n      areOwnPropsEqual,\r\n      areMergedPropsEqual,\r\n    }\r\n\r\n    function ConnectFunction<TOwnProps>(\r\n      props: InternalConnectProps & TOwnProps\r\n    ) {\r\n      const [propsContext, reactReduxForwardedRef, wrapperProps] =\r\n        React.useMemo(() => {\r\n          // Distinguish between actual \"data\" props that were passed to the wrapper component,\r\n          // and values needed to control behavior (forwarded refs, alternate context instances).\r\n          // To maintain the wrapperProps object reference, memoize this destructuring.\r\n          const { reactReduxForwardedRef, ...wrapperProps } = props\r\n          return [props.context, reactReduxForwardedRef, wrapperProps]\r\n        }, [props])\r\n\r\n      const ContextToUse: ReactReduxContextInstance = React.useMemo(() => {\r\n        // Users may optionally pass in a custom context instance to use instead of our ReactReduxContext.\r\n        // Memoize the check that determines which context instance we should use.\r\n        let ResultContext = Context\r\n        if (propsContext?.Consumer) {\r\n          if (process.env.NODE_ENV !== 'production') {\r\n            const isValid = /*#__PURE__*/ isContextConsumer(\r\n              // @ts-ignore\r\n              <propsContext.Consumer />\r\n            )\r\n            if (!isValid) {\r\n              throw new Error(\r\n                'You must pass a valid React context consumer as `props.context`'\r\n              )\r\n            }\r\n            ResultContext = propsContext\r\n          }\r\n        }\r\n        return ResultContext\r\n      }, [propsContext, Context])\r\n\r\n      // Retrieve the store and ancestor subscription via context, if available\r\n      const contextValue = React.useContext(ContextToUse)\r\n\r\n      // The store _must_ exist as either a prop or in context.\r\n      // We'll check to see if it _looks_ like a Redux store first.\r\n      // This allows us to pass through a `store` prop that is just a plain value.\r\n      const didStoreComeFromProps =\r\n        Boolean(props.store) &&\r\n        Boolean(props.store!.getState) &&\r\n        Boolean(props.store!.dispatch)\r\n      const didStoreComeFromContext =\r\n        Boolean(contextValue) && Boolean(contextValue!.store)\r\n\r\n      if (\r\n        process.env.NODE_ENV !== 'production' &&\r\n        !didStoreComeFromProps &&\r\n        !didStoreComeFromContext\r\n      ) {\r\n        throw new Error(\r\n          `Could not find \"store\" in the context of ` +\r\n            `\"${displayName}\". Either wrap the root component in a <Provider>, ` +\r\n            `or pass a custom React context provider to <Provider> and the corresponding ` +\r\n            `React context consumer to ${displayName} in connect options.`\r\n        )\r\n      }\r\n\r\n      // Based on the previous check, one of these must be true\r\n      const store: Store = didStoreComeFromProps\r\n        ? props.store!\r\n        : contextValue!.store\r\n\r\n      const getServerState = didStoreComeFromContext\r\n        ? contextValue!.getServerState\r\n        : store.getState\r\n\r\n      const childPropsSelector = React.useMemo(() => {\r\n        // The child props selector needs the store reference as an input.\r\n        // Re-create this selector whenever the store changes.\r\n        return defaultSelectorFactory(store.dispatch, selectorFactoryOptions)\r\n      }, [store])\r\n\r\n      const [subscription, notifyNestedSubs] = React.useMemo(() => {\r\n        if (!shouldHandleStateChanges) return NO_SUBSCRIPTION_ARRAY\r\n\r\n        // This Subscription's source should match where store came from: props vs. context. A component\r\n        // connected to the store via props shouldn't use subscription from context, or vice versa.\r\n        const subscription = createSubscription(\r\n          store,\r\n          didStoreComeFromProps ? undefined : contextValue!.subscription\r\n        )\r\n\r\n        // `notifyNestedSubs` is duplicated to handle the case where the component is unmounted in\r\n        // the middle of the notification loop, where `subscription` will then be null. This can\r\n        // probably be avoided if Subscription's listeners logic is changed to not call listeners\r\n        // that have been unsubscribed in the  middle of the notification loop.\r\n        const notifyNestedSubs =\r\n          subscription.notifyNestedSubs.bind(subscription)\r\n\r\n        return [subscription, notifyNestedSubs]\r\n      }, [store, didStoreComeFromProps, contextValue])\r\n\r\n      // Determine what {store, subscription} value should be put into nested context, if necessary,\r\n      // and memoize that value to avoid unnecessary context updates.\r\n      const overriddenContextValue = React.useMemo(() => {\r\n        if (didStoreComeFromProps) {\r\n          // This component is directly subscribed to a store from props.\r\n          // We don't want descendants reading from this store - pass down whatever\r\n          // the existing context value is from the nearest connected ancestor.\r\n          return contextValue!\r\n        }\r\n\r\n        // Otherwise, put this component's subscription instance into context, so that\r\n        // connected descendants won't update until after this component is done\r\n        return {\r\n          ...contextValue,\r\n          subscription,\r\n        } as ReactReduxContextValue\r\n      }, [didStoreComeFromProps, contextValue, subscription])\r\n\r\n      // Set up refs to coordinate values between the subscription effect and the render logic\r\n      const lastChildProps = React.useRef<unknown>()\r\n      const lastWrapperProps = React.useRef(wrapperProps)\r\n      const childPropsFromStoreUpdate = React.useRef<unknown>()\r\n      const renderIsScheduled = React.useRef(false)\r\n      const isProcessingDispatch = React.useRef(false)\r\n      const isMounted = React.useRef(false)\r\n\r\n      const latestSubscriptionCallbackError = React.useRef<Error>()\r\n\r\n      useIsomorphicLayoutEffect(() => {\r\n        isMounted.current = true\r\n        return () => {\r\n          isMounted.current = false\r\n        }\r\n      }, [])\r\n\r\n      const actualChildPropsSelector = React.useMemo(() => {\r\n        const selector = () => {\r\n          // Tricky logic here:\r\n          // - This render may have been triggered by a Redux store update that produced new child props\r\n          // - However, we may have gotten new wrapper props after that\r\n          // If we have new child props, and the same wrapper props, we know we should use the new child props as-is.\r\n          // But, if we have new wrapper props, those might change the child props, so we have to recalculate things.\r\n          // So, we'll use the child props from store update only if the wrapper props are the same as last time.\r\n          if (\r\n            childPropsFromStoreUpdate.current &&\r\n            wrapperProps === lastWrapperProps.current\r\n          ) {\r\n            return childPropsFromStoreUpdate.current\r\n          }\r\n\r\n          // TODO We're reading the store directly in render() here. Bad idea?\r\n          // This will likely cause Bad Things (TM) to happen in Concurrent Mode.\r\n          // Note that we do this because on renders _not_ caused by store updates, we need the latest store state\r\n          // to determine what the child props should be.\r\n          return childPropsSelector(store.getState(), wrapperProps)\r\n        }\r\n        return selector\r\n      }, [store, wrapperProps])\r\n\r\n      // We need this to execute synchronously every time we re-render. However, React warns\r\n      // about useLayoutEffect in SSR, so we try to detect environment and fall back to\r\n      // just useEffect instead to avoid the warning, since neither will run anyway.\r\n\r\n      const subscribeForReact = React.useMemo(() => {\r\n        const subscribe = (reactListener: () => void) => {\r\n          if (!subscription) {\r\n            return () => {}\r\n          }\r\n\r\n          return subscribeUpdates(\r\n            shouldHandleStateChanges,\r\n            store,\r\n            subscription,\r\n            // @ts-ignore\r\n            childPropsSelector,\r\n            lastWrapperProps,\r\n            lastChildProps,\r\n            renderIsScheduled,\r\n            isMounted,\r\n            childPropsFromStoreUpdate,\r\n            notifyNestedSubs,\r\n            reactListener\r\n          )\r\n        }\r\n\r\n        return subscribe\r\n      }, [subscription])\r\n\r\n      useIsomorphicLayoutEffectWithArgs(captureWrapperProps, [\r\n        lastWrapperProps,\r\n        lastChildProps,\r\n        renderIsScheduled,\r\n        wrapperProps,\r\n        childPropsFromStoreUpdate,\r\n        notifyNestedSubs,\r\n      ])\r\n\r\n      let actualChildProps: Record<string, unknown>\r\n\r\n      try {\r\n        actualChildProps = useSyncExternalStore(\r\n          // TODO We're passing through a big wrapper that does a bunch of extra side effects besides subscribing\r\n          subscribeForReact,\r\n          // TODO This is incredibly hacky. We've already processed the store update and calculated new child props,\r\n          // TODO and we're just passing that through so it triggers a re-render for us rather than relying on `uSES`.\r\n          actualChildPropsSelector,\r\n          getServerState\r\n            ? () => childPropsSelector(getServerState(), wrapperProps)\r\n            : actualChildPropsSelector\r\n        )\r\n      } catch (err) {\r\n        if (latestSubscriptionCallbackError.current) {\r\n          ;(\r\n            err as Error\r\n          ).message += `\\nThe error may be correlated with this previous error:\\n${latestSubscriptionCallbackError.current.stack}\\n\\n`\r\n        }\r\n\r\n        throw err\r\n      }\r\n\r\n      useIsomorphicLayoutEffect(() => {\r\n        latestSubscriptionCallbackError.current = undefined\r\n        childPropsFromStoreUpdate.current = undefined\r\n        lastChildProps.current = actualChildProps\r\n      })\r\n\r\n      // Now that all that's done, we can finally try to actually render the child component.\r\n      // We memoize the elements for the rendered child component as an optimization.\r\n      const renderedWrappedComponent = React.useMemo(() => {\r\n        return (\r\n          // @ts-ignore\r\n          <WrappedComponent\r\n            {...actualChildProps}\r\n            ref={reactReduxForwardedRef}\r\n          />\r\n        )\r\n      }, [reactReduxForwardedRef, WrappedComponent, actualChildProps])\r\n\r\n      // If React sees the exact same element reference as last time, it bails out of re-rendering\r\n      // that child, same as if it was wrapped in React.memo() or returned false from shouldComponentUpdate.\r\n      const renderedChild = React.useMemo(() => {\r\n        if (shouldHandleStateChanges) {\r\n          // If this component is subscribed to store updates, we need to pass its own\r\n          // subscription instance down to our descendants. That means rendering the same\r\n          // Context instance, and putting a different value into the context.\r\n          return (\r\n            <ContextToUse.Provider value={overriddenContextValue}>\r\n              {renderedWrappedComponent}\r\n            </ContextToUse.Provider>\r\n          )\r\n        }\r\n\r\n        return renderedWrappedComponent\r\n      }, [ContextToUse, renderedWrappedComponent, overriddenContextValue])\r\n\r\n      return renderedChild\r\n    }\r\n\r\n    const _Connect = React.memo(ConnectFunction)\r\n\r\n    type ConnectedWrapperComponent = typeof _Connect & {\r\n      WrappedComponent: typeof WrappedComponent\r\n    }\r\n\r\n    // Add a hacky cast to get the right output type\r\n    const Connect = _Connect as unknown as ConnectedComponent<\r\n      typeof WrappedComponent,\r\n      WrappedComponentProps\r\n    >\r\n    Connect.WrappedComponent = WrappedComponent\r\n    Connect.displayName = ConnectFunction.displayName = displayName\r\n\r\n    if (forwardRef) {\r\n      const _forwarded = React.forwardRef(function forwardConnectRef(\r\n        props,\r\n        ref\r\n      ) {\r\n        // @ts-ignore\r\n        return <Connect {...props} reactReduxForwardedRef={ref} />\r\n      })\r\n\r\n      const forwarded = _forwarded as ConnectedWrapperComponent\r\n      forwarded.displayName = displayName\r\n      forwarded.WrappedComponent = WrappedComponent\r\n      return /*#__PURE__*/ hoistStatics(forwarded, WrappedComponent)\r\n    }\r\n\r\n    return /*#__PURE__*/ hoistStatics(Connect, WrappedComponent)\r\n  }\r\n\r\n  return wrapWithConnect\r\n}\r\n\r\nexport default connect as Connect\r\n", "import type { Context, ReactNode } from 'react'\r\nimport { React } from '../utils/react'\r\nimport type { Action, Store, UnknownAction } from 'redux'\r\nimport type { DevModeCheckFrequency } from '../hooks/useSelector'\r\nimport { createSubscription } from '../utils/Subscription'\r\nimport { useIsomorphicLayoutEffect } from '../utils/useIsomorphicLayoutEffect'\r\nimport type { ReactReduxContextValue } from './Context'\r\nimport { ReactReduxContext } from './Context'\r\n\r\nexport interface ProviderProps<\r\n  A extends Action<string> = UnknownAction,\r\n  S = unknown\r\n> {\r\n  /**\r\n   * The single Redux store in your application.\r\n   */\r\n  store: Store<S, A>\r\n\r\n  /**\r\n   * An optional server state snapshot. Will be used during initial hydration render if available, to ensure that the UI output is consistent with the HTML generated on the server.\r\n   */\r\n  serverState?: S\r\n\r\n  /**\r\n   * Optional context to be used internally in react-redux. Use React.createContext() to create a context to be used.\r\n   * If this is used, you'll need to customize `connect` by supplying the same context provided to the Provider.\r\n   * Set the initial value to null, and the hooks will error\r\n   * if this is not overwritten by Provider.\r\n   */\r\n  context?: Context<ReactReduxContextValue<S, A> | null>\r\n\r\n  /**\r\n   * Determines the frequency of stability checks for all selectors.\r\n   * This setting overrides the global configuration for\r\n   * the `useSelector` stability check, allowing you to specify how often\r\n   * these checks should occur in development mode.\r\n   *\r\n   * @since 8.1.0\r\n   */\r\n  stabilityCheck?: DevModeCheckFrequency\r\n\r\n  /**\r\n   * Determines the frequency of identity function checks for all selectors.\r\n   * This setting overrides the global configuration for\r\n   * the `useSelector` identity function check, allowing you to specify how often\r\n   * these checks should occur in development mode.\r\n   *\r\n   * **Note**: Previously referred to as `noopCheck`.\r\n   *\r\n   * @since 9.0.0\r\n   */\r\n  identityFunctionCheck?: DevModeCheckFrequency\r\n\r\n  children: ReactNode\r\n}\r\n\r\nfunction Provider<A extends Action<string> = UnknownAction, S = unknown>({\r\n  store,\r\n  context,\r\n  children,\r\n  serverState,\r\n  stabilityCheck = 'once',\r\n  identityFunctionCheck = 'once',\r\n}: ProviderProps<A, S>) {\r\n  const contextValue = React.useMemo(() => {\r\n    const subscription = createSubscription(store)\r\n    return {\r\n      store,\r\n      subscription,\r\n      getServerState: serverState ? () => serverState : undefined,\r\n      stabilityCheck,\r\n      identityFunctionCheck,\r\n    }\r\n  }, [store, serverState, stabilityCheck, identityFunctionCheck])\r\n\r\n  const previousState = React.useMemo(() => store.getState(), [store])\r\n\r\n  useIsomorphicLayoutEffect(() => {\r\n    const { subscription } = contextValue\r\n    subscription.onStateChange = subscription.notifyNestedSubs\r\n    subscription.trySubscribe()\r\n\r\n    if (previousState !== store.getState()) {\r\n      subscription.notifyNestedSubs()\r\n    }\r\n    return () => {\r\n      subscription.tryUnsubscribe()\r\n      subscription.onStateChange = undefined\r\n    }\r\n  }, [contextValue, previousState])\r\n\r\n  const Context = context || ReactReduxContext\r\n\r\n  // @ts-ignore 'AnyAction' is assignable to the constraint of type 'A', but 'A' could be instantiated with a different subtype\r\n  return <Context.Provider value={contextValue}>{children}</Context.Provider>\r\n}\r\n\r\nexport default Provider\r\n", "import type { Context } from 'react'\r\nimport type { Action as BasicAction, UnknownAction, Store } from 'redux'\r\nimport type { ReactReduxContextValue } from '../components/Context'\r\nimport { ReactReduxContext } from '../components/Context'\r\nimport {\r\n  useReduxContext as useDefaultReduxContext,\r\n  createReduxContextHook,\r\n} from './useReduxContext'\r\n\r\n/**\r\n * Hook factory, which creates a `useStore` hook bound to a given context.\r\n *\r\n * @param {React.Context} [context=ReactReduxContext] Context passed to your `<Provider>`.\r\n * @returns {Function} A `useStore` hook bound to the specified context.\r\n */\r\nexport function createStoreHook<\r\n  S = unknown,\r\n  A extends BasicAction = UnknownAction\r\n  // @ts-ignore\r\n>(context?: Context<ReactReduxContextValue<S, A> | null> = ReactReduxContext) {\r\n  const useReduxContext =\r\n    // @ts-ignore\r\n    context === ReactReduxContext\r\n      ? useDefaultReduxContext\r\n      : // @ts-ignore\r\n        createReduxContextHook(context)\r\n  return function useStore<\r\n    State = S,\r\n    Action2 extends BasicAction = A\r\n    // @ts-ignore\r\n  >() {\r\n    const { store } = useReduxContext()\r\n    // @ts-ignore\r\n    return store as Store<State, Action2>\r\n  }\r\n}\r\n\r\n/**\r\n * A hook to access the redux store.\r\n *\r\n * @returns {any} the redux store\r\n *\r\n * @example\r\n *\r\n * import React from 'react'\r\n * import { useStore } from 'react-redux'\r\n *\r\n * export const ExampleComponent = () => {\r\n *   const store = useStore()\r\n *   return <div>{store.getState()}</div>\r\n * }\r\n */\r\nexport const useStore = /*#__PURE__*/ createStoreHook()\r\n", "import type { Action, Dispatch, UnknownAction } from 'redux'\r\nimport type { Context } from 'react'\r\n\r\nimport type { ReactReduxContextValue } from '../components/Context'\r\nimport { ReactReduxContext } from '../components/Context'\r\nimport { useStore as useDefaultStore, createStoreHook } from './useStore'\r\n\r\n/**\r\n * Hook factory, which creates a `useDispatch` hook bound to a given context.\r\n *\r\n * @param {React.Context} [context=ReactReduxContext] Context passed to your `<Provider>`.\r\n * @returns {Function} A `useDispatch` hook bound to the specified context.\r\n */\r\nexport function createDispatchHook<\r\n  S = unknown,\r\n  A extends Action<string> = UnknownAction\r\n  // @ts-ignore\r\n>(context?: Context<ReactReduxContextValue<S, A> | null> = ReactReduxContext) {\r\n  const useStore =\r\n    // @ts-ignore\r\n    context === ReactReduxContext ? useDefaultStore : createStoreHook(context)\r\n\r\n  return function useDispatch<\r\n    AppDispatch extends Dispatch<A> = Dispatch<A>\r\n  >(): AppDispatch {\r\n    const store = useStore()\r\n    // @ts-ignore\r\n    return store.dispatch\r\n  }\r\n}\r\n\r\n/**\r\n * A hook to access the redux `dispatch` function.\r\n *\r\n * @returns {any|function} redux store's `dispatch` function\r\n *\r\n * @example\r\n *\r\n * import React, { useCallback } from 'react'\r\n * import { useDispatch } from 'react-redux'\r\n *\r\n * export const CounterComponent = ({ value }) => {\r\n *   const dispatch = useDispatch()\r\n *   const increaseCounter = useCallback(() => dispatch({ type: 'increase-counter' }), [])\r\n *   return (\r\n *     <div>\r\n *       <span>{value}</span>\r\n *       <button onClick={increaseCounter}>Increase counter</button>\r\n *     </div>\r\n *   )\r\n * }\r\n */\r\nexport const useDispatch = /*#__PURE__*/ createDispatchHook()\r\n", "import Provider from './components/Provider'\r\nimport type { ProviderProps } from './components/Provider'\r\nimport connect from './components/connect'\r\nimport type {\r\n  Connect,\r\n  ConnectProps,\r\n  ConnectedProps,\r\n} from './components/connect'\r\nimport type {\r\n  SelectorFactory,\r\n  Selector,\r\n  MapStateToProps,\r\n  MapStateToPropsFactory,\r\n  MapStateToPropsParam,\r\n  MapDispatchToPropsFunction,\r\n  MapDispatchToProps,\r\n  MapDispatchToPropsFactory,\r\n  MapDispatchToPropsParam,\r\n  MapDispatchToPropsNonObject,\r\n  MergeProps,\r\n} from './connect/selectorFactory'\r\nimport { ReactReduxContext } from './components/Context'\r\nimport type { ReactReduxContextValue } from './components/Context'\r\n\r\nimport { useDispatch, createDispatchHook } from './hooks/useDispatch'\r\nimport { useSelector, createSelectorHook } from './hooks/useSelector'\r\nimport { useStore, createStoreHook } from './hooks/useStore'\r\n\r\nimport shallowEqual from './utils/shallowEqual'\r\nimport type { Subscription } from './utils/Subscription'\r\nimport { defaultNoopBatch } from './utils/batch'\r\n\r\nexport * from './types'\r\nexport type {\r\n  ProviderProps,\r\n  SelectorFactory,\r\n  Selector,\r\n  MapStateToProps,\r\n  MapStateToPropsFactory,\r\n  MapStateToPropsParam,\r\n  Connect,\r\n  ConnectProps,\r\n  ConnectedProps,\r\n  MapDispatchToPropsFunction,\r\n  MapDispatchToProps,\r\n  MapDispatchToPropsFactory,\r\n  MapDispatchToPropsParam,\r\n  MapDispatchToPropsNonObject,\r\n  MergeProps,\r\n  ReactReduxContextValue,\r\n  Subscription,\r\n}\r\n\r\n/**\r\n * @deprecated As of React 18, batching is enabled by default for ReactDOM and React Native.\r\n * This is now a no-op that immediately runs the callback.\r\n */\r\nconst batch = defaultNoopBatch\r\n\r\nexport {\r\n  Provider,\r\n  ReactReduxContext,\r\n  connect,\r\n  useDispatch,\r\n  createDispatchHook,\r\n  useSelector,\r\n  createSelectorHook,\r\n  useStore,\r\n  createStoreHook,\r\n  shallowEqual,\r\n  batch,\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA,QAAAA,eAAA;AAWA,QAAIC,SAAQ;AACZ,aAASC,IAAG,GAAG,GAAG;AAChB,aAAQ,MAAM,MAAM,MAAM,KAAK,IAAI,MAAM,IAAI,MAAQ,MAAM,KAAK,MAAM;AAAA,IACxE;AACA,QAAI,WAAW,eAAe,OAAO,OAAO,KAAK,OAAO,KAAKA;AAA7D,QACEC,wBAAuBF,OAAM;AAD/B,QAEE,SAASA,OAAM;AAFjB,QAGE,YAAYA,OAAM;AAHpB,QAIE,UAAUA,OAAM;AAJlB,QAKE,gBAAgBA,OAAM;AACxB,YAAQ,mCAAmC,SACzC,WACA,aACA,mBACA,UACA,SACA;AACA,UAAI,UAAU,OAAO,IAAI;AACzB,UAAI,SAAS,QAAQ,SAAS;AAC5B,YAAI,OAAO,EAAE,UAAU,OAAI,OAAO,KAAK;AACvC,gBAAQ,UAAU;AAAA,MACpB;AAAO,eAAO,QAAQ;AACtB,gBAAU;AAAA,QACR,WAAY;AACV,mBAAS,iBAAiB,cAAc;AACtC,gBAAI,CAAC,SAAS;AACZ,wBAAU;AACV,iCAAmB;AACnB,6BAAe,SAAS,YAAY;AACpC,kBAAI,WAAW,WAAW,KAAK,UAAU;AACvC,oBAAI,mBAAmB,KAAK;AAC5B,oBAAI,QAAQ,kBAAkB,YAAY;AACxC,yBAAQ,oBAAoB;AAAA,cAChC;AACA,qBAAQ,oBAAoB;AAAA,YAC9B;AACA,+BAAmB;AACnB,gBAAI,SAAS,kBAAkB,YAAY;AAAG,qBAAO;AACrD,gBAAI,gBAAgB,SAAS,YAAY;AACzC,gBAAI,WAAW,WAAW,QAAQ,kBAAkB,aAAa;AAC/D,qBAAQ,mBAAmB,cAAe;AAC5C,+BAAmB;AACnB,mBAAQ,oBAAoB;AAAA,UAC9B;AACA,cAAI,UAAU,OACZ,kBACA,mBACA,yBACE,WAAW,oBAAoB,OAAO;AAC1C,iBAAO;AAAA,YACL,WAAY;AACV,qBAAO,iBAAiB,YAAY,CAAC;AAAA,YACvC;AAAA,YACA,SAAS,yBACL,SACA,WAAY;AACV,qBAAO,iBAAiB,uBAAuB,CAAC;AAAA,YAClD;AAAA,UACN;AAAA,QACF;AAAA,QACA,CAAC,aAAa,mBAAmB,UAAU,OAAO;AAAA,MACpD;AACA,UAAI,QAAQE,sBAAqB,WAAW,QAAQ,CAAC,GAAG,QAAQ,CAAC,CAAC;AAClE;AAAA,QACE,WAAY;AACV,eAAK,WAAW;AAChB,eAAK,QAAQ;AAAA,QACf;AAAA,QACA,CAAC,KAAK;AAAA,MACR;AACA,oBAAc,KAAK;AACnB,aAAO;AAAA,IACT;AAAA;AAAA;;;ACnFA;AAAA;AAAA;AAAA,QAAAC,eAAA;AAWA,qBAAiB,QAAQ,IAAI,YAC1B,WAAY;AACX,eAASC,IAAG,GAAG,GAAG;AAChB,eAAQ,MAAM,MAAM,MAAM,KAAK,IAAI,MAAM,IAAI,MAAQ,MAAM,KAAK,MAAM;AAAA,MACxE;AACA,sBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,+BACxC,+BAA+B,4BAA4B,MAAM,CAAC;AACpE,UAAIC,SAAQ,iBACV,WAAW,eAAe,OAAO,OAAO,KAAK,OAAO,KAAKD,KACzDE,wBAAuBD,OAAM,sBAC7B,SAASA,OAAM,QACf,YAAYA,OAAM,WAClB,UAAUA,OAAM,SAChB,gBAAgBA,OAAM;AACxB,cAAQ,mCAAmC,SACzC,WACA,aACA,mBACA,UACA,SACA;AACA,YAAI,UAAU,OAAO,IAAI;AACzB,YAAI,SAAS,QAAQ,SAAS;AAC5B,cAAI,OAAO,EAAE,UAAU,OAAI,OAAO,KAAK;AACvC,kBAAQ,UAAU;AAAA,QACpB;AAAO,iBAAO,QAAQ;AACtB,kBAAU;AAAA,UACR,WAAY;AACV,qBAAS,iBAAiB,cAAc;AACtC,kBAAI,CAAC,SAAS;AACZ,0BAAU;AACV,mCAAmB;AACnB,+BAAe,SAAS,YAAY;AACpC,oBAAI,WAAW,WAAW,KAAK,UAAU;AACvC,sBAAI,mBAAmB,KAAK;AAC5B,sBAAI,QAAQ,kBAAkB,YAAY;AACxC,2BAAQ,oBAAoB;AAAA,gBAChC;AACA,uBAAQ,oBAAoB;AAAA,cAC9B;AACA,iCAAmB;AACnB,kBAAI,SAAS,kBAAkB,YAAY;AACzC,uBAAO;AACT,kBAAI,gBAAgB,SAAS,YAAY;AACzC,kBAAI,WAAW,WAAW,QAAQ,kBAAkB,aAAa;AAC/D,uBAAQ,mBAAmB,cAAe;AAC5C,iCAAmB;AACnB,qBAAQ,oBAAoB;AAAA,YAC9B;AACA,gBAAI,UAAU,OACZ,kBACA,mBACA,yBACE,WAAW,oBAAoB,OAAO;AAC1C,mBAAO;AAAA,cACL,WAAY;AACV,uBAAO,iBAAiB,YAAY,CAAC;AAAA,cACvC;AAAA,cACA,SAAS,yBACL,SACA,WAAY;AACV,uBAAO,iBAAiB,uBAAuB,CAAC;AAAA,cAClD;AAAA,YACN;AAAA,UACF;AAAA,UACA,CAAC,aAAa,mBAAmB,UAAU,OAAO;AAAA,QACpD;AACA,YAAI,QAAQC,sBAAqB,WAAW,QAAQ,CAAC,GAAG,QAAQ,CAAC,CAAC;AAClE;AAAA,UACE,WAAY;AACV,iBAAK,WAAW;AAChB,iBAAK,QAAQ;AAAA,UACf;AAAA,UACA,CAAC,KAAK;AAAA,QACR;AACA,sBAAc,KAAK;AACnB,eAAO;AAAA,MACT;AACA,sBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,8BACxC,+BAA+B,2BAA2B,MAAM,CAAC;AAAA,IACrE,EAAG;AAAA;AAAA;;;AC/FL;AAAA;AAAA;AAAA,QAAAC,eAAA;AAEA,QAAI,QAAQ,IAAI,aAAa,cAAc;AACzC,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;;;;ACDA,aAAuB;AACvB,2BAAiD;ACNjD,oBAA+B;AAGxB,IAAM;;;EAGX,aAAa,gBAA8B,2BAAa;;ACS1D,IAAM,aAAa,OAAO,IAAI,qBAAqB;AACnD,IAAM,KAMJ,OAAO,eAAe,cAClB;;EAC2F,CAAC;;AAGlG,SAAS,aAAqD;AAC5D,MAAI,CAAC,MAAM;AAAe,WAAO,CAAC;AAElC,QAAM,aAAc,GAAA,UAAA,MAAA,GAAA,UAAA,IAAmB,oBAAI,IAGzC;AACF,MAAI,cAAc,WAAW,IAAI,MAAM,aAAa;AACpD,MAAI,CAAC,aAAa;AAChB,kBAAc,MAAM;MAClB;IACF;AACA,QAAI,QAAQ,IAAI,aAAa,cAAc;AACzC,kBAAY,cAAc;IAC5B;AACA,eAAW,IAAI,MAAM,eAAe,WAAW;EACjD;AACA,SAAO;AACT;AAEO,IAAM,oBAAkC,WAAW;AC5CnD,IAAM,iBAAiB,MAAM;AAClC,QAAM,IAAI,MAAM,uBAAuB;AACzC;ACMO,SAAS,uBAAuB,UAAU,mBAAmB;AAClE,SAAO,SAASC,mBAA0C;AACxD,UAAM,eAAe,MAAM,WAAW,OAAO;AAE7C,QAAI,QAAQ,IAAI,aAAa,gBAAgB,CAAC,cAAc;AAC1D,YAAM,IAAI;QACR;MACF;IACF;AAEA,WAAO;EACT;AACF;AAkBO,IAAM,kBAAgC,uBAAuB;ACsCpE,IAAI,mCAAmC;AAChC,IAAM,wBAAwB,CAAC,OAAe;AACnD,qCAAmC;AACrC;AAEA,IAAM,cAA+B,CAAC,GAAG,MAAM,MAAM;AAQ9C,SAAS,mBACd,UAGY,mBACC;AACb,QAAMA,mBACJ,YAAY,oBACR,kBACA,uBAAuB,OAAO;AAEpC,SAAO,SAASC,aACd,UACA,sBAE4C,CAAC,GACnC;AACV,UAAM,EAAE,aAAa,aAAa,gBAAgB,CAAC,EAAE,IACnD,OAAO,wBAAwB,aAC3B,EAAE,YAAY,oBAAoB,IAClC;AACN,QAAI,QAAQ,IAAI,aAAa,cAAc;AACzC,UAAI,CAAC,UAAU;AACb,cAAM,IAAI,MAAM,yCAAyC;MAC3D;AACA,UAAI,OAAO,aAAa,YAAY;AAClC,cAAM,IAAI,MAAM,uDAAuD;MACzE;AACA,UAAI,OAAO,eAAe,YAAY;AACpC,cAAM,IAAI;UACR;QACF;MACF;IACF;AAEA,UAAM;MACJ;MACA;MACA;MACA;MACA;IACF,IAAID,iBAAgB;AAEpB,UAAM,WAAW,MAAM,OAAO,IAAI;AAElC,UAAM,kBAAkB,MAAM;MAC5B;QACE,CAAC,SAAS,IAAI,EAAE,OAAe;AAC7B,gBAAM,WAAW,SAAS,KAAK;AAC/B,cAAI,QAAQ,IAAI,aAAa,cAAc;AACzC,kBAAM;cACJ,uBAAuB;cACvB,gBAAgB;YAClB,IAAI;cACF;cACA;cACA,GAAG;YACL;AACA,gBACE,wBAAwB,YACvB,wBAAwB,UAAU,SAAS,SAC5C;AACA,oBAAM,YAAY,SAAS,KAAK;AAChC,kBAAI,CAAC,WAAW,UAAU,SAAS,GAAG;AACpC,oBAAI,QAA4B;AAChC,oBAAI;AACF,wBAAM,IAAI,MAAM;gBAClB,SAAS,GAAP;AACA;AAAC,mBAAC,EAAE,MAAM,IAAI;gBAChB;AACA,wBAAQ;kBACN,eACG,SAAS,QAAQ,aAClB;kBAEF;oBACE;oBACA;oBACA,WAAW;oBACX;kBACF;gBACF;cACF;YACF;AACA,gBACE,+BAA+B,YAC9B,+BAA+B,UAAU,SAAS,SACnD;AAEA,kBAAI,aAAa,OAAO;AACtB,oBAAI,QAA4B;AAChC,oBAAI;AACF,wBAAM,IAAI,MAAM;gBAClB,SAAS,GAAP;AACA;AAAC,mBAAC,EAAE,MAAM,IAAI;gBAChB;AACA,wBAAQ;kBACN,eACG,SAAS,QAAQ,aAClB;kBAEF,EAAE,MAAM;gBACV;cACF;YACF;AACA,gBAAI,SAAS;AAAS,uBAAS,UAAU;UAC3C;AACA,iBAAO;QACT;MACF,EAAE,SAAS,IAAI;MACf,CAAC,UAAU,gBAAgB,cAAc,cAAc;IACzD;AAEA,UAAM,gBAAgB;MACpB,aAAa;MACb,MAAM;MACN,kBAAkB,MAAM;MACxB;MACA;IACF;AAEA,UAAM,cAAc,aAAa;AAEjC,WAAO;EACT;AACF;AAyBO,IAAM,cAA4B,mBAAmB;AC3O5D,IAAM,qBAAqB,OAAO,IAAI,eAAe;AACrD,IAAM,oBAAoB,OAAO,IAAI,cAAc;AACnD,IAAM,sBAAsB,OAAO,IAAI,gBAAgB;AACvD,IAAM,yBAAyB,OAAO,IAAI,mBAAmB;AAC7D,IAAM,sBAAsB,OAAO,IAAI,gBAAgB;AACvD,IAAM,sBAAsB,OAAO,IAAI,gBAAgB;AACvD,IAAM,qBAAqB,OAAO,IAAI,eAAe;AACrD,IAAM,4BAA4B,OAAO,IAAI,sBAAsB;AACnE,IAAM,yBAAyB,OAAO,IAAI,mBAAmB;AAC7D,IAAM,sBAAsB,OAAO,IAAI,gBAAgB;AACvD,IAAM,2BAA2B,OAAO,IAAI,qBAAqB;AACjE,IAAM,kBAAkB,OAAO,IAAI,YAAY;AAC/C,IAAM,kBAAkB,OAAO,IAAI,YAAY;AAC/C,IAAM,uBAAuB,OAAO,IAAI,iBAAiB;AACzD,IAAM,yBAAyB,OAAO,IAAI,wBAAwB;AAE3D,IAAM,aAAa;AACnB,IAAM,OAAO;AAEb,SAAS,mBAAmB,MAAgC;AACjE,MAAI,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAC1D,WAAO;EACT;AAEA,MACE,SAAS,uBACT,SAAS,uBACT,SAAS,0BACT,SAAS,uBACT,SAAS,4BACT,SAAS,sBACT;AACA,WAAO;EACT;AAEA,MAAI,OAAO,SAAS,YAAY,SAAS,MAAM;AAC7C,QACE,KAAK,aAAa,mBAClB,KAAK,aAAa,mBAClB,KAAK,aAAa,uBAClB,KAAK,aAAa,sBAClB,KAAK,aAAa;;;;IAIlB,KAAK,aAAa,0BAClB,KAAK,gBAAgB,QACrB;AACA,aAAO;IACT;EACF;AAEA,SAAO;AACT;AAEA,SAAS,OAAO,QAAiC;AAC/C,MAAI,OAAO,WAAW,YAAY,WAAW,MAAM;AACjD,UAAM,WAAW,OAAO;AAExB,YAAQ,UAAU;MAChB,KAAK,oBAAoB;AACvB,cAAM,OAAO,OAAO;AAEpB,gBAAQ,MAAM;UACZ,KAAK;UACL,KAAK;UACL,KAAK;UACL,KAAK;UACL,KAAK;AACH,mBAAO;UAET,SAAS;AACP,kBAAM,eAAe,QAAQ,KAAK;AAElC,oBAAQ,cAAc;cACpB,KAAK;cACL,KAAK;cACL,KAAK;cACL,KAAK;cACL,KAAK;cACL,KAAK;AACH,uBAAO;cAET;AACE,uBAAO;YACX;UACF;QACF;MACF;MAEA,KAAK,mBAAmB;AACtB,eAAO;MACT;IACF;EACF;AAEA,SAAO;AACT;AAEO,SAAS,kBAAkB,QAAqC;AACrE,SAAO,OAAO,MAAM,MAAM;AAC5B;AAEO,SAAS,OAAO,QAAiD;AACtE,SAAO,OAAO,MAAM,MAAM;AAC5B;AC1Ge,SAAR,QAAyB,SAAiB;AAE/C,MAAI,OAAO,YAAY,eAAe,OAAO,QAAQ,UAAU,YAAY;AACzE,YAAQ,MAAM,OAAO;EACvB;AAEA,MAAI;AAIF,UAAM,IAAI,MAAM,OAAO;EAEzB,SAAS,GAAP;EAAW;AAEf;AClBA,SAAS,OAAO,UAAmB,YAA0B;AAC3D,MAAI,CAAC,UAAU;AACb,UAAM,IAAI,MAAM,wBAAwB,UAAA,cAAwB;EAClE,WACE,eAAe,qBACf,eAAe,sBACf;AACA,QAAI,CAAC,OAAO,UAAU,eAAe,KAAK,UAAU,mBAAmB,GAAG;AACxE;QACE,oBAAoB,UAAA;MACtB;IACF;EACF;AACF;AAEe,SAAR,mBACL,iBACA,oBACA,YACM;AACN,SAAO,iBAAiB,iBAAiB;AACzC,SAAO,oBAAoB,oBAAoB;AAC/C,SAAO,YAAY,YAAY;AACjC;ACyCO,SAAS,8BAOd,iBACA,oBACA,YACA,UACA;EACE;EACA;EACA;AACF,GACA;AACA,MAAI,oBAAoB;AACxB,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AAEJ,WAAS,gBAAgB,YAAmB,eAA0B;AACpE,YAAQ;AACR,eAAW;AACX,iBAAa,gBAAgB,OAAO,QAAQ;AAC5C,oBAAgB,mBAAmB,UAAU,QAAQ;AACrD,kBAAc,WAAW,YAAY,eAAe,QAAQ;AAC5D,wBAAoB;AACpB,WAAO;EACT;AAEA,WAAS,4BAA4B;AACnC,iBAAa,gBAAgB,OAAO,QAAQ;AAE5C,QAAI,mBAAmB;AACrB,sBAAgB,mBAAmB,UAAU,QAAQ;AAEvD,kBAAc,WAAW,YAAY,eAAe,QAAQ;AAC5D,WAAO;EACT;AAEA,WAAS,iBAAiB;AACxB,QAAI,gBAAgB;AAClB,mBAAa,gBAAgB,OAAO,QAAQ;AAE9C,QAAI,mBAAmB;AACrB,sBAAgB,mBAAmB,UAAU,QAAQ;AAEvD,kBAAc,WAAW,YAAY,eAAe,QAAQ;AAC5D,WAAO;EACT;AAEA,WAAS,iBAAiB;AACxB,UAAM,iBAAiB,gBAAgB,OAAO,QAAQ;AACtD,UAAM,oBAAoB,CAAC,mBAAmB,gBAAgB,UAAU;AACxE,iBAAa;AAEb,QAAI;AACF,oBAAc,WAAW,YAAY,eAAe,QAAQ;AAE9D,WAAO;EACT;AAEA,WAAS,sBAAsB,WAAkB,cAAyB;AACxE,UAAM,eAAe,CAAC,iBAAiB,cAAc,QAAQ;AAC7D,UAAM,eAAe,CAAC;MACpB;MACA;MACA;MACA;IACF;AACA,YAAQ;AACR,eAAW;AAEX,QAAI,gBAAgB;AAAc,aAAO,0BAA0B;AACnE,QAAI;AAAc,aAAO,eAAe;AACxC,QAAI;AAAc,aAAO,eAAe;AACxC,WAAO;EACT;AAEA,SAAO,SAAS,uBACd,WACA,cACA;AACA,WAAO,oBACH,sBAAsB,WAAW,YAAY,IAC7C,gBAAgB,WAAW,YAAY;EAC7C;AACF;AAgDe,SAAR,0BAOL,UACA;EACE;EACA;EACA;EACA,GAAG;AACL,GAOA;AACA,QAAM,kBAAkB,oBAAoB,UAAU,OAAO;AAC7D,QAAM,qBAAqB,uBAAuB,UAAU,OAAO;AACnE,QAAM,aAAa,eAAe,UAAU,OAAO;AAEnD,MAAI,QAAQ,IAAI,aAAa,cAAc;AACzC,uBAAmB,iBAAiB,oBAAoB,UAAU;EACpE;AAEA,SAAO,8BAML,iBAAiB,oBAAoB,YAAY,UAAU,OAAO;AACtE;AC/Oe,SAAR,mBACL,gBACA,UACyB;AACzB,QAAM,sBAA+C,CAAC;AAEtD,aAAW,OAAO,gBAAgB;AAChC,UAAM,gBAAgB,eAAe,GAAG;AACxC,QAAI,OAAO,kBAAkB,YAAY;AACvC,0BAAoB,GAAG,IAAI,IAAI,SAAS,SAAS,cAAc,GAAG,IAAI,CAAC;IACzE;EACF;AACA,SAAO;AACT;ACXe,SAAR,cAA+B,KAAc;AAClD,MAAI,OAAO,QAAQ,YAAY,QAAQ;AAAM,WAAO;AAEpD,MAAI,QAAQ,OAAO,eAAe,GAAG;AACrC,MAAI,UAAU;AAAM,WAAO;AAE3B,MAAI,YAAY;AAChB,SAAO,OAAO,eAAe,SAAS,MAAM,MAAM;AAChD,gBAAY,OAAO,eAAe,SAAS;EAC7C;AAEA,SAAO,UAAU;AACnB;ACbe,SAAR,kBACL,OACA,aACA,YACA;AACA,MAAI,CAAC,cAAc,KAAK,GAAG;AACzB;MACE,GAAG,UAAA,SAAmB,WAAA,iDAA4D,KAAA;IACpF;EACF;AACF;ACGO,SAAS,uBAMd,aAOA;AACA,SAAO,SAAS,qBAAqB,UAAoB;AACvD,UAAM,WAAW,YAAY,QAAQ;AAErC,aAAS,mBAAmB;AAC1B,aAAO;IACT;AACA,qBAAiB,oBAAoB;AACrC,WAAO;EACT;AACF;AAUO,SAAS,qBAAqB,YAAwB;AAC3D,SAAO,WAAW,oBACd,QAAQ,WAAW,iBAAiB,IACpC,WAAW,WAAW;AAC5B;AAcO,SAAS,mBACd,YACA,YACA;AACA,SAAO,SAAS,kBACd,UACA,EAAE,YAAY,GACd;AACA,UAAM,QAAQ,SAAS,gBACrB,iBACA,UACY;AACZ,aAAO,MAAM,oBACT,MAAM,WAAW,iBAAiB,QAAQ,IAC1C,MAAM,WAAW,iBAAiB,MAAS;IACjD;AAGA,UAAM,oBAAoB;AAE1B,UAAM,aAAa,SAAS,uBAC1B,iBACA,UACY;AACZ,YAAM,aAAa;AACnB,YAAM,oBAAoB,qBAAqB,UAAU;AACzD,UAAI,QAAQ,MAAM,iBAAiB,QAAQ;AAE3C,UAAI,OAAO,UAAU,YAAY;AAC/B,cAAM,aAAa;AACnB,cAAM,oBAAoB,qBAAqB,KAAK;AACpD,gBAAQ,MAAM,iBAAiB,QAAQ;MACzC;AAEA,UAAI,QAAQ,IAAI,aAAa;AAC3B,0BAAkB,OAAO,aAAa,UAAU;AAElD,aAAO;IACT;AAEA,WAAO;EACT;AACF;AC3GO,SAAS,wBAAwB,KAAc,MAAc;AAClE,SAAO,CACL,UACA,YACG;AACH,UAAM,IAAI;MACR,yBAAyB,OAAO,GAAA,QAAW,IAAA,uCACzC,QAAQ,oBAAA;IAEZ;EACF;AACF;ACPO,SAAS,0BACd,oBAGA;AACA,SAAO,sBAAsB,OAAO,uBAAuB,WACvD;IAAuB,CAAC;;MAEtB,mBAAmB,oBAAoB,QAAQ;;EACjD,IACA,CAAC,qBACD,uBAAuB,CAAC,cAAwC;IAC9D;EACF,EAAE,IACF,OAAO,uBAAuB;;IAE9B,mBAAmB,oBAAoB,oBAAoB;MAC3D,wBAAwB,oBAAoB,oBAAoB;AACtE;ACpBO,SAAS,uBACd,iBACA;AACA,SAAO,CAAC,kBACJ,uBAAuB,OAAO,CAAC,EAAE,IACjC,OAAO,oBAAoB;;IAE3B,mBAAmB,iBAAiB,iBAAiB;MACrD,wBAAwB,iBAAiB,iBAAiB;AAChE;ACPO,SAAS,kBAMd,YACA,eACA,UACc;AAEd,SAAO,EAAE,GAAG,UAAU,GAAG,YAAY,GAAG,cAAc;AACxD;AAEO,SAAS,mBAMd,YAOoE;AACpE,SAAO,SAAS,oBACd,UACA,EAAE,aAAa,oBAAoB,GACnC;AACA,QAAI,aAAa;AACjB,QAAI;AAEJ,WAAO,SAAS,gBACd,YACA,eACA,UACA;AACA,YAAM,kBAAkB,WAAW,YAAY,eAAe,QAAQ;AAEtE,UAAI,YAAY;AACd,YAAI,CAAC,oBAAoB,iBAAiB,WAAW;AACnD,wBAAc;MAClB,OAAO;AACL,qBAAa;AACb,sBAAc;AAEd,YAAI,QAAQ,IAAI,aAAa;AAC3B,4BAAkB,aAAa,aAAa,YAAY;MAC5D;AAEA,aAAO;IACT;EACF;AACF;AAEO,SAAS,kBAMd,YACA;AACA,SAAO,CAAC,aACJ,MAAM,oBACN,OAAO,eAAe,aACtB,mBAAmB,UAAU,IAC7B,wBAAwB,YAAY,YAAY;AACtD;AC5EO,SAAS,iBAAiB,UAAsB;AACrD,WAAS;AACX;ACWA,SAAS,2BAA2B;AAClC,MAAI,QAAyB;AAC7B,MAAI,OAAwB;AAE5B,SAAO;IACL,QAAQ;AACN,cAAQ;AACR,aAAO;IACT;IAEA,SAAS;AACP,uBAAM,MAAM;AACV,YAAI,WAAW;AACf,eAAO,UAAU;AACf,mBAAS,SAAS;AAClB,qBAAW,SAAS;QACtB;MACF,CAAC;IACH;IAEA,MAAM;AACJ,UAAI,YAAwB,CAAC;AAC7B,UAAI,WAAW;AACf,aAAO,UAAU;AACf,kBAAU,KAAK,QAAQ;AACvB,mBAAW,SAAS;MACtB;AACA,aAAO;IACT;IAEA,UAAU,UAAsB;AAC9B,UAAI,eAAe;AAEnB,UAAI,WAAsB,OAAO;QAC/B;QACA,MAAM;QACN,MAAM;MACR;AAEA,UAAI,SAAS,MAAM;AACjB,iBAAS,KAAK,OAAO;MACvB,OAAO;AACL,gBAAQ;MACV;AAEA,aAAO,SAAS,cAAc;AAC5B,YAAI,CAAC,gBAAgB,UAAU;AAAM;AACrC,uBAAe;AAEf,YAAI,SAAS,MAAM;AACjB,mBAAS,KAAK,OAAO,SAAS;QAChC,OAAO;AACL,iBAAO,SAAS;QAClB;AACA,YAAI,SAAS,MAAM;AACjB,mBAAS,KAAK,OAAO,SAAS;QAChC,OAAO;AACL,kBAAQ,SAAS;QACnB;MACF;IACF;EACF;AACF;AAeA,IAAM,gBAAgB;EACpB,SAAS;EAAC;EACV,KAAK,MAAM,CAAC;AACd;AAEO,SAAS,mBAAmB,OAAY,WAA0B;AACvE,MAAI;AACJ,MAAI,YAAgC;AAGpC,MAAI,sBAAsB;AAG1B,MAAI,iBAAiB;AAErB,WAAS,aAAa,UAAsB;AAC1C,iBAAa;AAEb,UAAM,kBAAkB,UAAU,UAAU,QAAQ;AAGpD,QAAI,UAAU;AACd,WAAO,MAAM;AACX,UAAI,CAAC,SAAS;AACZ,kBAAU;AACV,wBAAgB;AAChB,uBAAe;MACjB;IACF;EACF;AAEA,WAAS,mBAAmB;AAC1B,cAAU,OAAO;EACnB;AAEA,WAAS,sBAAsB;AAC7B,QAAI,aAAa,eAAe;AAC9B,mBAAa,cAAc;IAC7B;EACF;AAEA,WAAS,eAAe;AACtB,WAAO;EACT;AAEA,WAAS,eAAe;AACtB;AACA,QAAI,CAAC,aAAa;AAChB,oBAAc,YACV,UAAU,aAAa,mBAAmB,IAC1C,MAAM,UAAU,mBAAmB;AAEvC,kBAAY,yBAAyB;IACvC;EACF;AAEA,WAAS,iBAAiB;AACxB;AACA,QAAI,eAAe,wBAAwB,GAAG;AAC5C,kBAAY;AACZ,oBAAc;AACd,gBAAU,MAAM;AAChB,kBAAY;IACd;EACF;AAEA,WAAS,mBAAmB;AAC1B,QAAI,CAAC,gBAAgB;AACnB,uBAAiB;AACjB,mBAAa;IACf;EACF;AAEA,WAAS,qBAAqB;AAC5B,QAAI,gBAAgB;AAClB,uBAAiB;AACjB,qBAAe;IACjB;EACF;AAEA,QAAM,eAA6B;IACjC;IACA;IACA;IACA;IACA,cAAc;IACd,gBAAgB;IAChB,cAAc,MAAM;EACtB;AAEA,SAAO;AACT;AC1KO,IAAM,YAAY,CAAC,EACxB,OAAO,WAAW,eAClB,OAAO,OAAO,aAAa,eAC3B,OAAO,OAAO,SAAS,kBAAkB;AAGpC,IAAM,4BAA4B,YACrC,MAAM,kBACN,MAAM;ACpBV,SAAS,GAAG,GAAY,GAAY;AAClC,MAAI,MAAM,GAAG;AACX,WAAO,MAAM,KAAK,MAAM,KAAK,IAAI,MAAM,IAAI;EAC7C,OAAO;AACL,WAAO,MAAM,KAAK,MAAM;EAC1B;AACF;AAEe,SAAR,aAA8B,MAAW,MAAW;AACzD,MAAI,GAAG,MAAM,IAAI;AAAG,WAAO;AAE3B,MACE,OAAO,SAAS,YAChB,SAAS,QACT,OAAO,SAAS,YAChB,SAAS,MACT;AACA,WAAO;EACT;AAEA,QAAM,QAAQ,OAAO,KAAK,IAAI;AAC9B,QAAM,QAAQ,OAAO,KAAK,IAAI;AAE9B,MAAI,MAAM,WAAW,MAAM;AAAQ,WAAO;AAE1C,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,QACE,CAAC,OAAO,UAAU,eAAe,KAAK,MAAM,MAAM,CAAC,CAAC,KACpD,CAAC,GAAG,KAAK,MAAM,CAAC,CAAC,GAAG,KAAK,MAAM,CAAC,CAAC,CAAC,GAClC;AACA,aAAO;IACT;EACF;AAEA,SAAO;AACT;ACxBA,IAAM,gBAAgB;EACpB,mBAAmB;EACnB,aAAa;EACb,cAAc;EACd,cAAc;EACd,aAAa;EACb,iBAAiB;EACjB,0BAA0B;EAC1B,0BAA0B;EAC1B,QAAQ;EACR,WAAW;EACX,MAAM;AACR;AAEA,IAAM,gBAAgB;EACpB,MAAM;EACN,QAAQ;EACR,WAAW;EACX,QAAQ;EACR,QAAQ;EACR,WAAW;EACX,OAAO;AACT;AAEA,IAAM,sBAAsB;EAC1B,UAAU;EACV,QAAQ;EACR,cAAc;EACd,aAAa;EACb,WAAW;AACb;AAEA,IAAM,eAAe;EACnB,UAAU;EACV,SAAS;EACT,cAAc;EACd,aAAa;EACb,WAAW;EACX,MAAM;AACR;AAEA,IAAM,eAAe;EACnB,CAAC,UAAU,GAAG;EACd,CAAC,IAAI,GAAG;AACV;AAEA,SAAS,WAAW,WAAgB;AAElC,MAAI,OAAO,SAAS,GAAG;AACrB,WAAO;EACT;AAGA,SAAO,aAAa,UAAU,UAAU,CAAC,KAAK;AAChD;AAkBA,IAAM,iBAAiB,OAAO;AAC9B,IAAM,sBAAsB,OAAO;AACnC,IAAM,wBAAwB,OAAO;AACrC,IAAM,2BAA2B,OAAO;AACxC,IAAM,iBAAiB,OAAO;AAC9B,IAAM,kBAAkB,OAAO;AAEhB,SAAR,qBAML,iBAAoB,iBAA+C;AACnE,MAAI,OAAO,oBAAoB,UAAU;AAGvC,QAAI,iBAAiB;AACnB,YAAM,qBAAqB,eAAe,eAAe;AACzD,UAAI,sBAAsB,uBAAuB,iBAAiB;AAChE,6BAAqB,iBAAiB,kBAAkB;MAC1D;IACF;AAEA,QAAI,OAA4B,oBAAoB,eAAe;AAEnE,QAAI,uBAAuB;AACzB,aAAO,KAAK,OAAO,sBAAsB,eAAe,CAAC;IAC3D;AAEA,UAAM,gBAAgB,WAAW,eAAe;AAChD,UAAM,gBAAgB,WAAW,eAAe;AAEhD,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AACpC,YAAM,MAAM,KAAK,CAAC;AAClB,UACE,CAAC,cAAc,GAAiC,KAChD,EAAE,iBAAiB,cAAc,GAAiC,MAClE,EAAE,iBAAiB,cAAc,GAAiC,IAClE;AACA,cAAM,aAAa,yBAAyB,iBAAiB,GAAG;AAChE,YAAI;AAEF,yBAAe,iBAAiB,KAAK,UAAW;QAClD,SAAS,GAAP;QAEF;MACF;IACF;EACF;AAEA,SAAO;AACT;AC3FA,IAAIE,wBAAuB;AACpB,IAAM,oBAAoB,CAAC,OAAa;AAC7C,EAAAA,wBAAuB;AACzB;AAIA,IAAM,wBAAwB,CAAC,MAAM,IAAI;AAIzC,IAAM,qBAAqB,CAAC,SAAkB;AAC5C,MAAI;AACF,WAAO,KAAK,UAAU,IAAI;EAC5B,SAAS,KAAP;AACA,WAAO,OAAO,IAAI;EACpB;AACF;AAQA,SAAS,kCACP,YACA,YACA,cACA;AACA,4BAA0B,MAAM,WAAW,GAAG,UAAU,GAAG,YAAY;AACzE;AAGA,SAAS,oBACP,kBACA,gBACA,mBACA,cAEA,2BACA,kBACA;AAEA,mBAAiB,UAAU;AAC3B,oBAAkB,UAAU;AAG5B,MAAI,0BAA0B,SAAS;AACrC,8BAA0B,UAAU;AACpC,qBAAiB;EACnB;AACF;AAIA,SAAS,iBACP,0BACA,OACA,cACA,oBACA,kBACA,gBACA,mBACA,WACA,2BACA,kBAEA,6BACA;AAEA,MAAI,CAAC;AAA0B,WAAO,MAAM;IAAC;AAG7C,MAAI,iBAAiB;AACrB,MAAI,kBAAgC;AAGpC,QAAM,kBAAkB,MAAM;AAC5B,QAAI,kBAAkB,CAAC,UAAU,SAAS;AAGxC;IACF;AAGA,UAAM,mBAAmB,MAAM,SAAS;AAExC,QAAI,eAAe;AACnB,QAAI;AAGF,sBAAgB;QACd;QACA,iBAAiB;MACnB;IACF,SAAS,GAAP;AACA,cAAQ;AACR,wBAAkB;IACpB;AAEA,QAAI,CAAC,OAAO;AACV,wBAAkB;IACpB;AAGA,QAAI,kBAAkB,eAAe,SAAS;AAC5C,UAAI,CAAC,kBAAkB,SAAS;AAC9B,yBAAiB;MACnB;IACF,OAAO;AAKL,qBAAe,UAAU;AACzB,gCAA0B,UAAU;AACpC,wBAAkB,UAAU;AAI5B,kCAA4B;IAC9B;EACF;AAGA,eAAa,gBAAgB;AAC7B,eAAa,aAAa;AAI1B,kBAAgB;AAEhB,QAAM,qBAAqB,MAAM;AAC/B,qBAAiB;AACjB,iBAAa,eAAe;AAC5B,iBAAa,gBAAgB;AAE7B,QAAI,iBAAiB;AAMnB,YAAM;IACR;EACF;AAEA,SAAO;AACT;AAgBA,SAAS,YAAY,GAAY,GAAY;AAC3C,SAAO,MAAM;AACf;AAmNA,IAAI,qCAAqC;AAsBzC,SAAS,QAOP,iBACA,oBACA,YACA;;;EAGE;EACA,iBAAiB;EACjB,mBAAmB;EACnB,qBAAqB;EACrB,sBAAsB;;EAGtB,aAAa;;EAGb,UAAU;AACZ,IAAwD,CAAC,GAChD;AACT,MAAI,QAAQ,IAAI,aAAa,cAAc;AACzC,QAAI,SAAS,UAAa,CAAC,oCAAoC;AAC7D,2CAAqC;AACrC;QACE;MACF;IACF;EACF;AAEA,QAAM,UAAU;AAEhB,QAAM,sBAAsB,uBAAuB,eAAe;AAClE,QAAM,yBAAyB,0BAA0B,kBAAkB;AAC3E,QAAM,iBAAiB,kBAAkB,UAAU;AAEnD,QAAM,2BAA2B,QAAQ,eAAe;AAExD,QAAM,kBAAkB,CACtB,qBACG;AAIH,QAAI,QAAQ,IAAI,aAAa,cAAc;AACzC,YAAM,UAAwB,mBAAmB,gBAAgB;AACjE,UAAI,CAAC;AACH,cAAM,IAAI;UACR,mFAAmF;YACjF;UACF,CAAA;QACF;IACJ;AAEA,UAAM,uBACJ,iBAAiB,eAAe,iBAAiB,QAAQ;AAE3D,UAAM,cAAc,WAAW,oBAAA;AAE/B,UAAM,yBAMF;MACF;MACA;MACA;MACA;;MAEA;;MAEA;MACA;MACA;MACA;MACA;MACA;IACF;AAEA,aAAS,gBACP,OACA;AACA,YAAM,CAAC,cAAc,wBAAwB,YAAY,IACvD,MAAM,QAAQ,MAAM;AAIlB,cAAM,EAAE,wBAAAC,yBAAwB,GAAGC,cAAa,IAAI;AACpD,eAAO,CAAC,MAAM,SAASD,yBAAwBC,aAAY;MAC7D,GAAG,CAAC,KAAK,CAAC;AAEZ,YAAM,eAA0C,MAAM,QAAQ,MAAM;AAGlE,YAAI,gBAAgB;AACpB,YAAI,6CAAc,UAAU;AAC1B,cAAI,QAAQ,IAAI,aAAa,cAAc;AACzC,kBAAM,UAAwB;;cAE5B,MAAA,cAAC,aAAa,UAAb,IAAsB;YACzB;AACA,gBAAI,CAAC,SAAS;AACZ,oBAAM,IAAI;gBACR;cACF;YACF;AACA,4BAAgB;UAClB;QACF;AACA,eAAO;MACT,GAAG,CAAC,cAAc,OAAO,CAAC;AAG1B,YAAM,eAAe,MAAM,WAAW,YAAY;AAKlD,YAAM,wBACJ,QAAQ,MAAM,KAAK,KACnB,QAAQ,MAAM,MAAO,QAAQ,KAC7B,QAAQ,MAAM,MAAO,QAAQ;AAC/B,YAAM,0BACJ,QAAQ,YAAY,KAAK,QAAQ,aAAc,KAAK;AAEtD,UACE,QAAQ,IAAI,aAAa,gBACzB,CAAC,yBACD,CAAC,yBACD;AACA,cAAM,IAAI;UACR,6CACM,WAAA,4JAEyB,WAAA;QACjC;MACF;AAGA,YAAM,QAAe,wBACjB,MAAM,QACN,aAAc;AAElB,YAAM,iBAAiB,0BACnB,aAAc,iBACd,MAAM;AAEV,YAAM,qBAAqB,MAAM,QAAQ,MAAM;AAG7C,eAAO,0BAAuB,MAAM,UAAU,sBAAsB;MACtE,GAAG,CAAC,KAAK,CAAC;AAEV,YAAM,CAAC,cAAc,gBAAgB,IAAI,MAAM,QAAQ,MAAM;AAC3D,YAAI,CAAC;AAA0B,iBAAO;AAItC,cAAMC,gBAAe;UACnB;UACA,wBAAwB,SAAY,aAAc;QACpD;AAMA,cAAMC,oBACJD,cAAa,iBAAiB,KAAKA,aAAY;AAEjD,eAAO,CAACA,eAAcC,iBAAgB;MACxC,GAAG,CAAC,OAAO,uBAAuB,YAAY,CAAC;AAI/C,YAAM,yBAAyB,MAAM,QAAQ,MAAM;AACjD,YAAI,uBAAuB;AAIzB,iBAAO;QACT;AAIA,eAAO;UACL,GAAG;UACH;QACF;MACF,GAAG,CAAC,uBAAuB,cAAc,YAAY,CAAC;AAGtD,YAAM,iBAAiB,MAAM,OAAgB;AAC7C,YAAM,mBAAmB,MAAM,OAAO,YAAY;AAClD,YAAM,4BAA4B,MAAM,OAAgB;AACxD,YAAM,oBAAoB,MAAM,OAAO,KAAK;AAC5C,YAAM,uBAAuB,MAAM,OAAO,KAAK;AAC/C,YAAM,YAAY,MAAM,OAAO,KAAK;AAEpC,YAAM,kCAAkC,MAAM,OAAc;AAE5D,gCAA0B,MAAM;AAC9B,kBAAU,UAAU;AACpB,eAAO,MAAM;AACX,oBAAU,UAAU;QACtB;MACF,GAAG,CAAC,CAAC;AAEL,YAAM,2BAA2B,MAAM,QAAQ,MAAM;AACnD,cAAM,WAAW,MAAM;AAOrB,cACE,0BAA0B,WAC1B,iBAAiB,iBAAiB,SAClC;AACA,mBAAO,0BAA0B;UACnC;AAMA,iBAAO,mBAAmB,MAAM,SAAS,GAAG,YAAY;QAC1D;AACA,eAAO;MACT,GAAG,CAAC,OAAO,YAAY,CAAC;AAMxB,YAAM,oBAAoB,MAAM,QAAQ,MAAM;AAC5C,cAAM,YAAY,CAAC,kBAA8B;AAC/C,cAAI,CAAC,cAAc;AACjB,mBAAO,MAAM;YAAC;UAChB;AAEA,iBAAO;YACL;YACA;YACA;;YAEA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACF;QACF;AAEA,eAAO;MACT,GAAG,CAAC,YAAY,CAAC;AAEjB,wCAAkC,qBAAqB;QACrD;QACA;QACA;QACA;QACA;QACA;MACF,CAAC;AAED,UAAI;AAEJ,UAAI;AACF,2BAAmBJ;;UAEjB;;;UAGA;UACA,iBACI,MAAM,mBAAmB,eAAe,GAAG,YAAY,IACvD;QACN;MACF,SAAS,KAAP;AACA,YAAI,gCAAgC,SAAS;AAC3C;AACE,cACA,WAAW;;EAA4D,gCAAgC,QAAQ,KAAA;;;QACnH;AAEA,cAAM;MACR;AAEA,gCAA0B,MAAM;AAC9B,wCAAgC,UAAU;AAC1C,kCAA0B,UAAU;AACpC,uBAAe,UAAU;MAC3B,CAAC;AAID,YAAM,2BAA2B,MAAM,QAAQ,MAAM;AACnD;;UAEE,MAAA;YAAC;YAAA;cACE,GAAG;cACJ,KAAK;YAAA;UACP;;MAEJ,GAAG,CAAC,wBAAwB,kBAAkB,gBAAgB,CAAC;AAI/D,YAAM,gBAAgB,MAAM,QAAQ,MAAM;AACxC,YAAI,0BAA0B;AAI5B,iBACE,MAAA,cAAC,aAAa,UAAb,EAAsB,OAAO,uBAAA,GAC3B,wBACH;QAEJ;AAEA,eAAO;MACT,GAAG,CAAC,cAAc,0BAA0B,sBAAsB,CAAC;AAEnE,aAAO;IACT;AAEA,UAAM,WAAW,MAAM,KAAK,eAAe;AAO3C,UAAM,UAAU;AAIhB,YAAQ,mBAAmB;AAC3B,YAAQ,cAAc,gBAAgB,cAAc;AAEpD,QAAI,YAAY;AACd,YAAM,aAAa,MAAM,WAAW,SAAS,kBAC3C,OACA,KACA;AAEA,eAAO,MAAA,cAAC,SAAA,EAAS,GAAG,OAAO,wBAAwB,IAAA,CAAK;MAC1D,CAAC;AAED,YAAM,YAAY;AAClB,gBAAU,cAAc;AACxB,gBAAU,mBAAmB;AAC7B,aAAqB,qBAAa,WAAW,gBAAgB;IAC/D;AAEA,WAAqB,qBAAa,SAAS,gBAAgB;EAC7D;AAEA,SAAO;AACT;AAEA,IAAO,kBAAQ;ACzvBf,SAAS,SAAgE;EACvE;EACA;EACA;EACA;EACA,iBAAiB;EACjB,wBAAwB;AAC1B,GAAwB;AACtB,QAAM,eAAe,MAAM,QAAQ,MAAM;AACvC,UAAM,eAAe,mBAAmB,KAAK;AAC7C,WAAO;MACL;MACA;MACA,gBAAgB,cAAc,MAAM,cAAc;MAClD;MACA;IACF;EACF,GAAG,CAAC,OAAO,aAAa,gBAAgB,qBAAqB,CAAC;AAE9D,QAAM,gBAAgB,MAAM,QAAQ,MAAM,MAAM,SAAS,GAAG,CAAC,KAAK,CAAC;AAEnE,4BAA0B,MAAM;AAC9B,UAAM,EAAE,aAAa,IAAI;AACzB,iBAAa,gBAAgB,aAAa;AAC1C,iBAAa,aAAa;AAE1B,QAAI,kBAAkB,MAAM,SAAS,GAAG;AACtC,mBAAa,iBAAiB;IAChC;AACA,WAAO,MAAM;AACX,mBAAa,eAAe;AAC5B,mBAAa,gBAAgB;IAC/B;EACF,GAAG,CAAC,cAAc,aAAa,CAAC;AAEhC,QAAM,UAAU,WAAW;AAG3B,SAAO,MAAA,cAAC,QAAQ,UAAR,EAAiB,OAAO,aAAA,GAAe,QAAS;AAC1D;AAEA,IAAO,mBAAQ;AClFR,SAAS,gBAId,UAAyD,mBAAmB;AAC5E,QAAMF;;IAEJ,YAAY,oBACR;;MAEA,uBAAuB,OAAO;;;AACpC,SAAO,SAASO,YAIZ;AACF,UAAM,EAAE,MAAM,IAAIP,iBAAgB;AAElC,WAAO;EACT;AACF;AAiBO,IAAM,WAAyB,gBAAgB;ACvC/C,SAAS,mBAId,UAAyD,mBAAmB;AAC5E,QAAMO;;IAEJ,YAAY,oBAAoB,WAAkB,gBAAgB,OAAO;;AAE3E,SAAO,SAASC,eAEC;AACf,UAAM,QAAQD,UAAS;AAEvB,WAAO,MAAM;EACf;AACF;AAuBO,IAAM,cAA4B,mBAAmB;ACK5D,IAAM,QAAQ;A3B9Cd,sBAAsBE,qBAAAA,gCAAgC;AACtD,kBAAwB,2BAAoB;", "names": ["import_dist", "React", "is", "useSyncExternalStore", "import_dist", "is", "React", "useSyncExternalStore", "import_dist", "useReduxContext", "useSelector", "useSyncExternalStore", "reactReduxForwardedRef", "wrapperProps", "subscription", "notifyNestedSubs", "useStore", "useDispatch", "useSyncExternalStoreWithSelector"]}