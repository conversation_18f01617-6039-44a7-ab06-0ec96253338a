{"version": 3, "sources": ["../../../src/query/index.ts", "../../../src/query/core/apiState.ts", "../../../src/query/utils/isAbsoluteUrl.ts", "../../../src/query/utils/joinUrls.ts", "../../../src/query/utils/flatten.ts", "../../../src/query/utils/isOnline.ts", "../../../src/query/utils/isDocumentVisible.ts", "../../../src/query/core/rtkImports.ts", "../../../src/query/utils/copyWithStructuralSharing.ts", "../../../src/query/fetchBaseQuery.ts", "../../../src/query/HandledError.ts", "../../../src/query/retry.ts", "../../../src/query/core/setupListeners.ts", "../../../src/query/endpointDefinitions.ts", "../../../src/query/core/buildInitiate.ts", "../../../src/query/utils/isNotNullish.ts", "../../../src/query/utils/countObjectKeys.ts", "../../../src/query/core/buildThunks.ts", "../../../src/query/core/buildSlice.ts", "../../../src/query/core/buildSelectors.ts", "../../../src/query/defaultSerializeQueryArgs.ts", "../../../src/query/createApi.ts", "../../../src/query/fakeBaseQuery.ts", "../../../src/query/core/buildMiddleware/cacheCollection.ts", "../../../src/query/core/buildMiddleware/invalidationByTags.ts", "../../../src/query/core/buildMiddleware/polling.ts", "../../../src/query/core/buildMiddleware/windowEventHandling.ts", "../../../src/query/core/buildMiddleware/cacheLifecycle.ts", "../../../src/query/core/buildMiddleware/queryLifecycle.ts", "../../../src/query/core/buildMiddleware/devMiddleware.ts", "../../../src/query/core/buildMiddleware/batchActions.ts", "../../../src/query/core/buildMiddleware/index.ts", "../../../src/query/tsHelpers.ts", "../../../src/query/core/module.ts", "../../../src/query/core/index.ts"], "sourcesContent": ["// This must remain here so that the `mangleErrors.cjs` build script\n// does not have to import this into each source file it rewrites.\nimport { formatProdErrorMessage } from '@reduxjs/toolkit';\nexport type { CombinedState, QueryCacheKey, QueryKeys, QuerySubState, RootState, SubscriptionOptions } from './core/apiState';\nexport { QueryStatus } from './core/apiState';\nexport type { Api, ApiContext, ApiModules, Module } from './apiTypes';\nexport type { BaseQueryApi, BaseQueryEnhancer, BaseQueryFn } from './baseQueryTypes';\nexport type { EndpointDefinitions, EndpointDefinition, EndpointBuilder, QueryDefinition, MutationDefinition, TagDescription, QueryArgFrom, ResultTypeFrom, DefinitionType } from './endpointDefinitions';\nexport { fetchBaseQuery } from './fetchBaseQuery';\nexport type { FetchBaseQueryError, FetchBaseQueryMeta, FetchArgs } from './fetchBaseQuery';\nexport { retry } from './retry';\nexport { setupListeners } from './core/setupListeners';\nexport { skipToken } from './core/buildSelectors';\nexport type { QueryResultSelectorResult, MutationResultSelectorResult, SkipToken } from './core/buildSelectors';\nexport type { QueryActionCreatorResult, MutationActionCreatorResult } from './core/buildInitiate';\nexport type { CreateApi, CreateApiOptions } from './createApi';\nexport { buildCreateApi } from './createApi';\nexport { fakeBaseQuery } from './fakeBaseQuery';\nexport { copyWithStructuralSharing } from './utils/copyWithStructuralSharing';\nexport { createApi, coreModule, coreModuleName } from './core';\nexport type { ApiEndpointMutation, ApiEndpointQuery, CoreModule, PrefetchOptions } from './core/module';\nexport { defaultSerializeQueryArgs } from './defaultSerializeQueryArgs';\nexport type { SerializeQueryArgs } from './defaultSerializeQueryArgs';\nexport type { Id as TSHelpersId, NoInfer as TSHelpersNoInfer, Override as TSHelpersOverride } from './tsHelpers';", "import type { SerializedError } from '@reduxjs/toolkit';\nimport type { BaseQueryError } from '../baseQueryTypes';\nimport type { QueryDefinition, MutationDefinition, EndpointDefinitions, BaseEndpointDefinition, ResultTypeFrom, QueryArgFrom } from '../endpointDefinitions';\nimport type { Id, WithRequiredProp } from '../tsHelpers';\nexport type QueryCacheKey = string & {\n  _type: 'queryCacheKey';\n};\nexport type QuerySubstateIdentifier = {\n  queryCacheKey: QueryCacheKey;\n};\nexport type MutationSubstateIdentifier = {\n  requestId: string;\n  fixedCacheKey?: string;\n} | {\n  requestId?: string;\n  fixedCacheKey: string;\n};\nexport type RefetchConfigOptions = {\n  refetchOnMountOrArgChange: boolean | number;\n  refetchOnReconnect: boolean;\n  refetchOnFocus: boolean;\n};\n/**\r\n * Strings describing the query state at any given time.\r\n */\n\nexport enum QueryStatus {\n  uninitialized = 'uninitialized',\n  pending = 'pending',\n  fulfilled = 'fulfilled',\n  rejected = 'rejected',\n}\nexport type RequestStatusFlags = {\n  status: QueryStatus.uninitialized;\n  isUninitialized: true;\n  isLoading: false;\n  isSuccess: false;\n  isError: false;\n} | {\n  status: QueryStatus.pending;\n  isUninitialized: false;\n  isLoading: true;\n  isSuccess: false;\n  isError: false;\n} | {\n  status: QueryStatus.fulfilled;\n  isUninitialized: false;\n  isLoading: false;\n  isSuccess: true;\n  isError: false;\n} | {\n  status: QueryStatus.rejected;\n  isUninitialized: false;\n  isLoading: false;\n  isSuccess: false;\n  isError: true;\n};\nexport function getRequestStatusFlags(status: QueryStatus): RequestStatusFlags {\n  return ({\n    status,\n    isUninitialized: status === QueryStatus.uninitialized,\n    isLoading: status === QueryStatus.pending,\n    isSuccess: status === QueryStatus.fulfilled,\n    isError: status === QueryStatus.rejected\n  } as any);\n}\nexport type SubscriptionOptions = {\n  /**\r\n   * How frequently to automatically re-fetch data (in milliseconds). Defaults to `0` (off).\r\n   */\n  pollingInterval?: number;\n  /**\r\n   * Defaults to `false`. This setting allows you to control whether RTK Query will try to refetch all subscribed queries after regaining a network connection.\r\n   *\r\n   * If you specify this option alongside `skip: true`, this **will not be evaluated** until `skip` is false.\r\n   *\r\n   * Note: requires [`setupListeners`](./setupListeners) to have been called.\r\n   */\n\n  refetchOnReconnect?: boolean;\n  /**\r\n   * Defaults to `false`. This setting allows you to control whether RTK Query will try to refetch all subscribed queries after the application window regains focus.\r\n   *\r\n   * If you specify this option alongside `skip: true`, this **will not be evaluated** until `skip` is false.\r\n   *\r\n   * Note: requires [`setupListeners`](./setupListeners) to have been called.\r\n   */\n\n  refetchOnFocus?: boolean;\n};\nexport type Subscribers = {\n  [requestId: string]: SubscriptionOptions;\n};\nexport type QueryKeys<Definitions extends EndpointDefinitions> = { [K in keyof Definitions]: Definitions[K] extends QueryDefinition<any, any, any, any> ? K : never }[keyof Definitions];\nexport type MutationKeys<Definitions extends EndpointDefinitions> = { [K in keyof Definitions]: Definitions[K] extends MutationDefinition<any, any, any, any> ? K : never }[keyof Definitions];\ntype BaseQuerySubState<D extends BaseEndpointDefinition<any, any, any>> = {\n  /**\r\n   * The argument originally passed into the hook or `initiate` action call\r\n   */\n  originalArgs: QueryArgFrom<D>;\n  /**\r\n   * A unique ID associated with the request\r\n   */\n\n  requestId: string;\n  /**\r\n   * The received data from the query\r\n   */\n\n  data?: ResultTypeFrom<D>;\n  /**\r\n   * The received error if applicable\r\n   */\n\n  error?: SerializedError | (D extends QueryDefinition<any, infer BaseQuery, any, any> ? BaseQueryError<BaseQuery> : never);\n  /**\r\n   * The name of the endpoint associated with the query\r\n   */\n\n  endpointName: string;\n  /**\r\n   * Time that the latest query started\r\n   */\n\n  startedTimeStamp: number;\n  /**\r\n   * Time that the latest query was fulfilled\r\n   */\n\n  fulfilledTimeStamp?: number;\n};\nexport type QuerySubState<D extends BaseEndpointDefinition<any, any, any>> = Id<({\n  status: QueryStatus.fulfilled;\n} & WithRequiredProp<BaseQuerySubState<D>, 'data' | 'fulfilledTimeStamp'> & {\n  error: undefined;\n}) | ({\n  status: QueryStatus.pending;\n} & BaseQuerySubState<D>) | ({\n  status: QueryStatus.rejected;\n} & WithRequiredProp<BaseQuerySubState<D>, 'error'>) | {\n  status: QueryStatus.uninitialized;\n  originalArgs?: undefined;\n  data?: undefined;\n  error?: undefined;\n  requestId?: undefined;\n  endpointName?: string;\n  startedTimeStamp?: undefined;\n  fulfilledTimeStamp?: undefined;\n}>;\ntype BaseMutationSubState<D extends BaseEndpointDefinition<any, any, any>> = {\n  requestId: string;\n  data?: ResultTypeFrom<D>;\n  error?: SerializedError | (D extends MutationDefinition<any, infer BaseQuery, any, any> ? BaseQueryError<BaseQuery> : never);\n  endpointName: string;\n  startedTimeStamp: number;\n  fulfilledTimeStamp?: number;\n};\nexport type MutationSubState<D extends BaseEndpointDefinition<any, any, any>> = (({\n  status: QueryStatus.fulfilled;\n} & WithRequiredProp<BaseMutationSubState<D>, 'data' | 'fulfilledTimeStamp'>) & {\n  error: undefined;\n}) | (({\n  status: QueryStatus.pending;\n} & BaseMutationSubState<D>) & {\n  data?: undefined;\n}) | ({\n  status: QueryStatus.rejected;\n} & WithRequiredProp<BaseMutationSubState<D>, 'error'>) | {\n  requestId?: undefined;\n  status: QueryStatus.uninitialized;\n  data?: undefined;\n  error?: undefined;\n  endpointName?: string;\n  startedTimeStamp?: undefined;\n  fulfilledTimeStamp?: undefined;\n};\nexport type CombinedState<D extends EndpointDefinitions, E extends string, ReducerPath extends string> = {\n  queries: QueryState<D>;\n  mutations: MutationState<D>;\n  provided: InvalidationState<E>;\n  subscriptions: SubscriptionState;\n  config: ConfigState<ReducerPath>;\n};\nexport type InvalidationState<TagTypes extends string> = { [_ in TagTypes]: {\n  [id: string]: Array<QueryCacheKey>;\n  [id: number]: Array<QueryCacheKey>;\n} };\nexport type QueryState<D extends EndpointDefinitions> = {\n  [queryCacheKey: string]: QuerySubState<D[string]> | undefined;\n};\nexport type SubscriptionState = {\n  [queryCacheKey: string]: Subscribers | undefined;\n};\nexport type ConfigState<ReducerPath> = RefetchConfigOptions & {\n  reducerPath: ReducerPath;\n  online: boolean;\n  focused: boolean;\n  middlewareRegistered: boolean | 'conflict';\n} & ModifiableConfigState;\nexport type ModifiableConfigState = {\n  keepUnusedDataFor: number;\n  invalidationBehavior: 'delayed' | 'immediately';\n} & RefetchConfigOptions;\nexport type MutationState<D extends EndpointDefinitions> = {\n  [requestId: string]: MutationSubState<D[string]> | undefined;\n};\nexport type RootState<Definitions extends EndpointDefinitions, TagTypes extends string, ReducerPath extends string> = { [P in ReducerPath]: CombinedState<Definitions, TagTypes, P> };", "/**\r\n * If either :// or // is present consider it to be an absolute url\r\n *\r\n * @param url string\r\n */\nexport function isAbsoluteUrl(url: string) {\n  return new RegExp(`(^|:)//`).test(url);\n}", "import { isAbsoluteUrl } from './isAbsoluteUrl';\n\nconst withoutTrailingSlash = (url: string) => url.replace(/\\/$/, '');\n\nconst withoutLeadingSlash = (url: string) => url.replace(/^\\//, '');\n\nexport function joinUrls(base: string | undefined, url: string | undefined): string {\n  if (!base) {\n    return url!;\n  }\n\n  if (!url) {\n    return base;\n  }\n\n  if (isAbsoluteUrl(url)) {\n    return url;\n  }\n\n  const delimiter = base.endsWith('/') || !url.startsWith('?') ? '/' : '';\n  base = withoutTrailingSlash(base);\n  url = withoutLeadingSlash(url);\n  return `${base}${delimiter}${url}`;\n}", "/**\r\n * Alternative to `Array.flat(1)`\r\n * @param arr An array like [1,2,3,[1,2]]\r\n * @link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/flat\r\n */\nexport const flatten = (arr: readonly any[]) => [].concat(...arr);", "/**\r\n * Assumes a browser is online if `undefined`, otherwise makes a best effort\r\n * @link https://developer.mozilla.org/en-US/docs/Web/API/NavigatorOnLine/onLine\r\n */\nexport function isOnline() {\n  // We set the default config value in the store, so we'd need to check for this in a SSR env\n  return typeof navigator === 'undefined' ? true : navigator.onLine === undefined ? true : navigator.onLine;\n}", "/**\r\n * Assumes true for a non-browser env, otherwise makes a best effort\r\n * @link https://developer.mozilla.org/en-US/docs/Web/API/Document/visibilityState\r\n */\nexport function isDocumentVisible(): boolean {\n  // `document` may not exist in non-browser envs (like RN)\n  if (typeof document === 'undefined') {\n    return true;\n  } // Match true for visible, prerender, undefined\n\n\n  return document.visibilityState !== 'hidden';\n}", "// This file exists to consolidate all of the imports from the `@reduxjs/toolkit` package.\n// ESBuild does not de-duplicate imports, so this file is used to ensure that each method\n// imported is only listed once, and there's only one mention of the `@reduxjs/toolkit` package.\nexport { createAction, createSlice, createSelector, createAsyncThunk, combineReducers, createNextState, isAnyOf, isAllOf, isAction, isPending, isRejected, isFulfilled, isRejectedWithValue, isAsyncThunkAction, prepareAutoBatched, SHOULD_AUTOBATCH, isPlainObject, nanoid } from '@reduxjs/toolkit';", "import { isPlainObject as _iPO } from '../core/rtkImports'; // remove type guard\n\nconst isPlainObject: (_: any) => boolean = _iPO;\nexport function copyWithStructuralSharing<T>(oldObj: any, newObj: T): T;\nexport function copyWithStructuralSharing(oldObj: any, newObj: any): any {\n  if (oldObj === newObj || !(isPlainObject(oldObj) && isPlainObject(newObj) || Array.isArray(oldObj) && Array.isArray(newObj))) {\n    return newObj;\n  }\n\n  const newKeys = Object.keys(newObj);\n  const oldKeys = Object.keys(oldObj);\n  let isSameObject = newKeys.length === oldKeys.length;\n  const mergeObj: any = Array.isArray(newObj) ? [] : {};\n\n  for (const key of newKeys) {\n    mergeObj[key] = copyWithStructuralSharing(oldObj[key], newObj[key]);\n    if (isSameObject) isSameObject = oldObj[key] === mergeObj[key];\n  }\n\n  return isSameObject ? oldObj : mergeObj;\n}", "import { joinUrls } from './utils';\nimport { isPlainObject } from './core/rtkImports';\nimport type { BaseQueryApi, BaseQueryFn } from './baseQueryTypes';\nimport type { MaybePromise, Override } from './tsHelpers';\nexport type ResponseHandler = 'content-type' | 'json' | 'text' | ((response: Response) => Promise<any>);\ntype CustomRequestInit = Override<RequestInit, {\n  headers?: Headers | string[][] | Record<string, string | undefined> | undefined;\n}>;\nexport interface FetchArgs extends CustomRequestInit {\n  url: string;\n  params?: Record<string, any>;\n  body?: any;\n  responseHandler?: ResponseHandler;\n  validateStatus?: (response: Response, body: any) => boolean;\n  /**\r\n   * A number in milliseconds that represents that maximum time a request can take before timing out.\r\n   */\n\n  timeout?: number;\n}\n/**\r\n * A mini-wrapper that passes arguments straight through to\r\n * {@link [fetch](https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API)}.\r\n * Avoids storing `fetch` in a closure, in order to permit mocking/monkey-patching.\r\n */\n\nconst defaultFetchFn: typeof fetch = (...args) => fetch(...args);\n\nconst defaultValidateStatus = (response: Response) => response.status >= 200 && response.status <= 299;\n\nconst defaultIsJsonContentType = (headers: Headers) =>\n/*applicat*/\n/ion\\/(vnd\\.api\\+)?json/.test(headers.get('content-type') || '');\n\nexport type FetchBaseQueryError = {\n  /**\r\n   * * `number`:\r\n   *   HTTP status code\r\n   */\n  status: number;\n  data: unknown;\n} | {\n  /**\r\n   * * `\"FETCH_ERROR\"`:\r\n   *   An error that occurred during execution of `fetch` or the `fetchFn` callback option\r\n   **/\n  status: 'FETCH_ERROR';\n  data?: undefined;\n  error: string;\n} | {\n  /**\r\n   * * `\"PARSING_ERROR\"`:\r\n   *   An error happened during parsing.\r\n   *   Most likely a non-JSON-response was returned with the default `responseHandler` \"JSON\",\r\n   *   or an error occurred while executing a custom `responseHandler`.\r\n   **/\n  status: 'PARSING_ERROR';\n  originalStatus: number;\n  data: string;\n  error: string;\n} | {\n  /**\r\n   * * `\"TIMEOUT_ERROR\"`:\r\n   *   Request timed out\r\n   **/\n  status: 'TIMEOUT_ERROR';\n  data?: undefined;\n  error: string;\n} | {\n  /**\r\n   * * `\"CUSTOM_ERROR\"`:\r\n   *   A custom error type that you can return from your `queryFn` where another error might not make sense.\r\n   **/\n  status: 'CUSTOM_ERROR';\n  data?: unknown;\n  error: string;\n};\n\nfunction stripUndefined(obj: any) {\n  if (!isPlainObject(obj)) {\n    return obj;\n  }\n\n  const copy: Record<string, any> = { ...obj\n  };\n\n  for (const [k, v] of Object.entries(copy)) {\n    if (v === undefined) delete copy[k];\n  }\n\n  return copy;\n}\n\nexport type FetchBaseQueryArgs = {\n  baseUrl?: string;\n  prepareHeaders?: (headers: Headers, api: Pick<BaseQueryApi, 'getState' | 'extra' | 'endpoint' | 'type' | 'forced'>) => MaybePromise<Headers | void>;\n  fetchFn?: (input: RequestInfo, init?: RequestInit | undefined) => Promise<Response>;\n  paramsSerializer?: (params: Record<string, any>) => string;\n  /**\r\n   * By default, we only check for 'application/json' and 'application/vnd.api+json' as the content-types for json. If you need to support another format, you can pass\r\n   * in a predicate function for your given api to get the same automatic stringifying behavior\r\n   * @example\r\n   * ```ts\r\n   * const isJsonContentType = (headers: Headers) => [\"application/vnd.api+json\", \"application/json\", \"application/vnd.hal+json\"].includes(headers.get(\"content-type\")?.trim());\r\n   * ```\r\n   */\n\n  isJsonContentType?: (headers: Headers) => boolean;\n  /**\r\n   * Defaults to `application/json`;\r\n   */\n\n  jsonContentType?: string;\n  /**\r\n   * Custom replacer function used when calling `JSON.stringify()`;\r\n   */\n\n  jsonReplacer?: (this: any, key: string, value: any) => any;\n} & RequestInit & Pick<FetchArgs, 'responseHandler' | 'validateStatus' | 'timeout'>;\nexport type FetchBaseQueryMeta = {\n  request: Request;\n  response?: Response;\n};\n/**\r\n * This is a very small wrapper around fetch that aims to simplify requests.\r\n *\r\n * @example\r\n * ```ts\r\n * const baseQuery = fetchBaseQuery({\r\n *   baseUrl: 'https://api.your-really-great-app.com/v1/',\r\n *   prepareHeaders: (headers, { getState }) => {\r\n *     const token = (getState() as RootState).auth.token;\r\n *     // If we have a token set in state, let's assume that we should be passing it.\r\n *     if (token) {\r\n *       headers.set('authorization', `Bearer ${token}`);\r\n *     }\r\n *     return headers;\r\n *   },\r\n * })\r\n * ```\r\n *\r\n * @param {string} baseUrl\r\n * The base URL for an API service.\r\n * Typically in the format of https://example.com/\r\n *\r\n * @param {(headers: Headers, api: { getState: () => unknown; extra: unknown; endpoint: string; type: 'query' | 'mutation'; forced: boolean; }) => Headers} prepareHeaders\r\n * An optional function that can be used to inject headers on requests.\r\n * Provides a Headers object, as well as most of the `BaseQueryApi` (`dispatch` is not available).\r\n * Useful for setting authentication or headers that need to be set conditionally.\r\n *\r\n * @link https://developer.mozilla.org/en-US/docs/Web/API/Headers\r\n *\r\n * @param {(input: RequestInfo, init?: RequestInit | undefined) => Promise<Response>} fetchFn\r\n * Accepts a custom `fetch` function if you do not want to use the default on the window.\r\n * Useful in SSR environments if you need to use a library such as `isomorphic-fetch` or `cross-fetch`\r\n *\r\n * @param {(params: Record<string, unknown>) => string} paramsSerializer\r\n * An optional function that can be used to stringify querystring parameters.\r\n *\r\n * @param {(headers: Headers) => boolean} isJsonContentType\r\n * An optional predicate function to determine if `JSON.stringify()` should be called on the `body` arg of `FetchArgs`\r\n *\r\n * @param {string} jsonContentType Used when automatically setting the content-type header for a request with a jsonifiable body that does not have an explicit content-type header. Defaults to `application/json`.\r\n *\r\n * @param {(this: any, key: string, value: any) => any} jsonReplacer Custom replacer function used when calling `JSON.stringify()`.\r\n *\r\n * @param {number} timeout\r\n * A number in milliseconds that represents the maximum time a request can take before timing out.\r\n */\n\nexport function fetchBaseQuery({\n  baseUrl,\n  prepareHeaders = x => x,\n  fetchFn = defaultFetchFn,\n  paramsSerializer,\n  isJsonContentType = defaultIsJsonContentType,\n  jsonContentType = 'application/json',\n  jsonReplacer,\n  timeout: defaultTimeout,\n  responseHandler: globalResponseHandler,\n  validateStatus: globalValidateStatus,\n  ...baseFetchOptions\n}: FetchBaseQueryArgs = {}): BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError, {}, FetchBaseQueryMeta> {\n  if (typeof fetch === 'undefined' && fetchFn === defaultFetchFn) {\n    console.warn('Warning: `fetch` is not available. Please supply a custom `fetchFn` property to use `fetchBaseQuery` on SSR environments.');\n  }\n\n  return async (arg, api) => {\n    const {\n      signal,\n      getState,\n      extra,\n      endpoint,\n      forced,\n      type\n    } = api;\n    let meta: FetchBaseQueryMeta | undefined;\n    let {\n      url,\n      headers = new Headers(baseFetchOptions.headers),\n      params = undefined,\n      responseHandler = globalResponseHandler ?? ('json' as const),\n      validateStatus = globalValidateStatus ?? defaultValidateStatus,\n      timeout = defaultTimeout,\n      ...rest\n    } = typeof arg == 'string' ? {\n      url: arg\n    } : arg;\n    let config: RequestInit = { ...baseFetchOptions,\n      signal,\n      ...rest\n    };\n    headers = new Headers(stripUndefined(headers));\n    config.headers = (await prepareHeaders(headers, {\n      getState,\n      extra,\n      endpoint,\n      forced,\n      type\n    })) || headers; // Only set the content-type to json if appropriate. Will not be true for FormData, ArrayBuffer, Blob, etc.\n\n    const isJsonifiable = (body: any) => typeof body === 'object' && (isPlainObject(body) || Array.isArray(body) || typeof body.toJSON === 'function');\n\n    if (!config.headers.has('content-type') && isJsonifiable(config.body)) {\n      config.headers.set('content-type', jsonContentType);\n    }\n\n    if (isJsonifiable(config.body) && isJsonContentType(config.headers)) {\n      config.body = JSON.stringify(config.body, jsonReplacer);\n    }\n\n    if (params) {\n      const divider = ~url.indexOf('?') ? '&' : '?';\n      const query = paramsSerializer ? paramsSerializer(params) : new URLSearchParams(stripUndefined(params));\n      url += divider + query;\n    }\n\n    url = joinUrls(baseUrl, url);\n    const request = new Request(url, config);\n    const requestClone = new Request(url, config);\n    meta = {\n      request: requestClone\n    };\n    let response,\n        timedOut = false,\n        timeoutId = timeout && setTimeout(() => {\n      timedOut = true;\n      api.abort();\n    }, timeout);\n\n    try {\n      response = await fetchFn(request);\n    } catch (e) {\n      return {\n        error: {\n          status: timedOut ? 'TIMEOUT_ERROR' : 'FETCH_ERROR',\n          error: String(e)\n        },\n        meta\n      };\n    } finally {\n      if (timeoutId) clearTimeout(timeoutId);\n    }\n\n    const responseClone = response.clone();\n    meta.response = responseClone;\n    let resultData: any;\n    let responseText: string = '';\n\n    try {\n      let handleResponseError;\n      await Promise.all([handleResponse(response, responseHandler).then(r => resultData = r, e => handleResponseError = e), // see https://github.com/node-fetch/node-fetch/issues/665#issuecomment-538995182\n      // we *have* to \"use up\" both streams at the same time or they will stop running in node-fetch scenarios\n      responseClone.text().then(r => responseText = r, () => {})]);\n      if (handleResponseError) throw handleResponseError;\n    } catch (e) {\n      return {\n        error: {\n          status: 'PARSING_ERROR',\n          originalStatus: response.status,\n          data: responseText,\n          error: String(e)\n        },\n        meta\n      };\n    }\n\n    return validateStatus(response, resultData) ? {\n      data: resultData,\n      meta\n    } : {\n      error: {\n        status: response.status,\n        data: resultData\n      },\n      meta\n    };\n  };\n\n  async function handleResponse(response: Response, responseHandler: ResponseHandler) {\n    if (typeof responseHandler === 'function') {\n      return responseHandler(response);\n    }\n\n    if (responseHandler === 'content-type') {\n      responseHandler = isJsonContentType(response.headers) ? 'json' : 'text';\n    }\n\n    if (responseHandler === 'json') {\n      const text = await response.text();\n      return text.length ? JSON.parse(text) : null;\n    }\n\n    return response.text();\n  }\n}", "export class HandledError {\n  constructor(public readonly value: any, public readonly meta: any = undefined) {}\n\n}", "import type { BaseQueryApi, BaseQueryArg, BaseQueryEnhancer, BaseQueryExtraOptions, BaseQueryFn } from './baseQueryTypes';\nimport type { FetchBaseQueryError } from './fetchBaseQuery';\nimport { HandledError } from './HandledError';\n/**\r\n * Exponential backoff based on the attempt number.\r\n *\r\n * @remarks\r\n * 1. 600ms * random(0.4, 1.4)\r\n * 2. 1200ms * random(0.4, 1.4)\r\n * 3. 2400ms * random(0.4, 1.4)\r\n * 4. 4800ms * random(0.4, 1.4)\r\n * 5. 9600ms * random(0.4, 1.4)\r\n *\r\n * @param attempt - Current attempt\r\n * @param maxRetries - Maximum number of retries\r\n */\n\nasync function defaultBackoff(attempt: number = 0, maxRetries: number = 5) {\n  const attempts = Math.min(attempt, maxRetries);\n  const timeout = ~~((Math.random() + 0.4) * (300 << attempts)); // Force a positive int in the case we make this an option\n\n  await new Promise(resolve => setTimeout((res: any) => resolve(res), timeout));\n}\n\ntype RetryConditionFunction = (error: FetchBaseQueryError, args: BaseQueryArg<BaseQueryFn>, extraArgs: {\n  attempt: number;\n  baseQueryApi: BaseQueryApi;\n  extraOptions: BaseQueryExtraOptions<BaseQueryFn> & RetryOptions;\n}) => boolean;\nexport type RetryOptions = {\n  /**\r\n   * Function used to determine delay between retries\r\n   */\n  backoff?: (attempt: number, maxRetries: number) => Promise<void>;\n} & ({\n  /**\r\n   * How many times the query will be retried (default: 5)\r\n   */\n  maxRetries?: number;\n  retryCondition?: undefined;\n} | {\n  /**\r\n   * Callback to determine if a retry should be attempted.\r\n   * Return `true` for another retry and `false` to quit trying prematurely.\r\n   */\n  retryCondition?: RetryConditionFunction;\n  maxRetries?: undefined;\n});\n\nfunction fail(e: any): never {\n  throw Object.assign(new HandledError({\n    error: e\n  }), {\n    throwImmediately: true\n  });\n}\n\nconst EMPTY_OPTIONS = {};\n\nconst retryWithBackoff: BaseQueryEnhancer<unknown, RetryOptions, RetryOptions | void> = (baseQuery, defaultOptions) => async (args, api, extraOptions) => {\n  // We need to figure out `maxRetries` before we define `defaultRetryCondition.\n  // This is probably goofy, but ought to work.\n  // Put our defaults in one array, filter out undefineds, grab the last value.\n  const possibleMaxRetries: number[] = [5, ((defaultOptions as any) || EMPTY_OPTIONS).maxRetries, ((extraOptions as any) || EMPTY_OPTIONS).maxRetries].filter(x => x !== undefined);\n  const [maxRetries] = possibleMaxRetries.slice(-1);\n\n  const defaultRetryCondition: RetryConditionFunction = (_, __, {\n    attempt\n  }) => attempt <= maxRetries;\n\n  const options: {\n    maxRetries: number;\n    backoff: typeof defaultBackoff;\n    retryCondition: typeof defaultRetryCondition;\n  } = {\n    maxRetries,\n    backoff: defaultBackoff,\n    retryCondition: defaultRetryCondition,\n    ...defaultOptions,\n    ...extraOptions\n  };\n  let retry = 0;\n\n  while (true) {\n    try {\n      const result = await baseQuery(args, api, extraOptions); // baseQueries _should_ return an error property, so we should check for that and throw it to continue retrying\n\n      if (result.error) {\n        throw new HandledError(result);\n      }\n\n      return result;\n    } catch (e: any) {\n      retry++;\n\n      if (e.throwImmediately) {\n        if (e instanceof HandledError) {\n          return e.value;\n        } // We don't know what this is, so we have to rethrow it\n\n\n        throw e;\n      }\n\n      if (e instanceof HandledError && !options.retryCondition((e.value.error as FetchBaseQueryError), args, {\n        attempt: retry,\n        baseQueryApi: api,\n        extraOptions\n      })) {\n        return e.value;\n      }\n\n      await options.backoff(retry, options.maxRetries);\n    }\n  }\n};\n/**\r\n * A utility that can wrap `baseQuery` in the API definition to provide retries with a basic exponential backoff.\r\n *\r\n * @example\r\n *\r\n * ```ts\r\n * // codeblock-meta title=\"Retry every request 5 times by default\"\r\n * import { createApi, fetchBaseQuery, retry } from '@reduxjs/toolkit/query/react'\r\n * interface Post {\r\n *   id: number\r\n *   name: string\r\n * }\r\n * type PostsResponse = Post[]\r\n *\r\n * // maxRetries: 5 is the default, and can be omitted. Shown for documentation purposes.\r\n * const staggeredBaseQuery = retry(fetchBaseQuery({ baseUrl: '/' }), { maxRetries: 5 });\r\n * export const api = createApi({\r\n *   baseQuery: staggeredBaseQuery,\r\n *   endpoints: (build) => ({\r\n *     getPosts: build.query<PostsResponse, void>({\r\n *       query: () => ({ url: 'posts' }),\r\n *     }),\r\n *     getPost: build.query<PostsResponse, string>({\r\n *       query: (id) => ({ url: `post/${id}` }),\r\n *       extraOptions: { maxRetries: 8 }, // You can override the retry behavior on each endpoint\r\n *     }),\r\n *   }),\r\n * });\r\n *\r\n * export const { useGetPostsQuery, useGetPostQuery } = api;\r\n * ```\r\n */\n\n\nexport const retry = /* @__PURE__ */Object.assign(retryWithBackoff, {\n  fail\n});", "import type { ThunkDispatch, ActionCreatorWithoutPayload // Workaround for API-Extractor\n} from '@reduxjs/toolkit';\nimport { createAction } from './rtkImports';\nexport const onFocus = /* @__PURE__ */createAction('__rtkq/focused');\nexport const onFocusLost = /* @__PURE__ */createAction('__rtkq/unfocused');\nexport const onOnline = /* @__PURE__ */createAction('__rtkq/online');\nexport const onOffline = /* @__PURE__ */createAction('__rtkq/offline');\nlet initialized = false;\n/**\r\n * A utility used to enable `refetchOnMount` and `refetchOnReconnect` behaviors.\r\n * It requires the dispatch method from your store.\r\n * Calling `setupListeners(store.dispatch)` will configure listeners with the recommended defaults,\r\n * but you have the option of providing a callback for more granular control.\r\n *\r\n * @example\r\n * ```ts\r\n * setupListeners(store.dispatch)\r\n * ```\r\n *\r\n * @param dispatch - The dispatch method from your store\r\n * @param customHandler - An optional callback for more granular control over listener behavior\r\n * @returns Return value of the handler.\r\n * The default handler returns an `unsubscribe` method that can be called to remove the listeners.\r\n */\n\nexport function setupListeners(dispatch: ThunkDispatch<any, any, any>, customHandler?: (dispatch: ThunkDispatch<any, any, any>, actions: {\n  onFocus: typeof onFocus;\n  onFocusLost: typeof onFocusLost;\n  onOnline: typeof onOnline;\n  onOffline: typeof onOffline;\n}) => () => void) {\n  function defaultHandler() {\n    const handleFocus = () => dispatch(onFocus());\n\n    const handleFocusLost = () => dispatch(onFocusLost());\n\n    const handleOnline = () => dispatch(onOnline());\n\n    const handleOffline = () => dispatch(onOffline());\n\n    const handleVisibilityChange = () => {\n      if (window.document.visibilityState === 'visible') {\n        handleFocus();\n      } else {\n        handleFocusLost();\n      }\n    };\n\n    if (!initialized) {\n      if (typeof window !== 'undefined' && window.addEventListener) {\n        // Handle focus events\n        window.addEventListener('visibilitychange', handleVisibilityChange, false);\n        window.addEventListener('focus', handleFocus, false); // Handle connection events\n\n        window.addEventListener('online', handleOnline, false);\n        window.addEventListener('offline', handleOffline, false);\n        initialized = true;\n      }\n    }\n\n    const unsubscribe = () => {\n      window.removeEventListener('focus', handleFocus);\n      window.removeEventListener('visibilitychange', handleVisibilityChange);\n      window.removeEventListener('online', handleOnline);\n      window.removeEventListener('offline', handleOffline);\n      initialized = false;\n    };\n\n    return unsubscribe;\n  }\n\n  return customHandler ? customHandler(dispatch, {\n    onFocus,\n    onFocusLost,\n    onOffline,\n    onOnline\n  }) : defaultHandler();\n}", "import type { SerializeQueryArgs } from './defaultSerializeQueryArgs';\nimport type { QuerySubState, RootState } from './core/apiState';\nimport type { BaseQueryExtraOptions, BaseQueryFn, BaseQueryResult, BaseQueryArg, BaseQueryApi, QueryReturnValue, BaseQueryError, BaseQueryMeta } from './baseQueryTypes';\nimport type { HasRequiredProps, MaybePromise, OmitFromUnion, CastAny, NonUndefined, UnwrapPromise } from './tsHelpers';\nimport type { NEVER } from './fakeBaseQuery';\nimport type { Api } from '@reduxjs/toolkit/query';\nconst resultType = /* @__PURE__ */Symbol();\nconst baseQuery = /* @__PURE__ */Symbol();\ninterface EndpointDefinitionWithQuery<QueryArg, BaseQuery extends BaseQueryFn, ResultType> {\n  /**\r\n   * `query` can be a function that returns either a `string` or an `object` which is passed to your `baseQuery`. If you are using [fetchBaseQuery](./fetchBaseQuery), this can return either a `string` or an `object` of properties in `FetchArgs`. If you use your own custom [`baseQuery`](../../rtk-query/usage/customizing-queries), you can customize this behavior to your liking.\r\n   *\r\n   * @example\r\n   *\r\n   * ```ts\r\n   * // codeblock-meta title=\"query example\"\r\n   *\r\n   * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'\r\n   * interface Post {\r\n   *   id: number\r\n   *   name: string\r\n   * }\r\n   * type PostsResponse = Post[]\r\n   *\r\n   * const api = createApi({\r\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\r\n   *   tagTypes: ['Post'],\r\n   *   endpoints: (build) => ({\r\n   *     getPosts: build.query<PostsResponse, void>({\r\n   *       // highlight-start\r\n   *       query: () => 'posts',\r\n   *       // highlight-end\r\n   *     }),\r\n   *     addPost: build.mutation<Post, Partial<Post>>({\r\n   *      // highlight-start\r\n   *      query: (body) => ({\r\n   *        url: `posts`,\r\n   *        method: 'POST',\r\n   *        body,\r\n   *      }),\r\n   *      // highlight-end\r\n   *      invalidatesTags: [{ type: 'Post', id: 'LIST' }],\r\n   *    }),\r\n   *   })\r\n   * })\r\n   * ```\r\n   */\n  query(arg: QueryArg): BaseQueryArg<BaseQuery>;\n  queryFn?: never;\n  /**\r\n   * A function to manipulate the data returned by a query or mutation.\r\n   */\n\n  transformResponse?(baseQueryReturnValue: BaseQueryResult<BaseQuery>, meta: BaseQueryMeta<BaseQuery>, arg: QueryArg): ResultType | Promise<ResultType>;\n  /**\r\n   * A function to manipulate the data returned by a failed query or mutation.\r\n   */\n\n  transformErrorResponse?(baseQueryReturnValue: BaseQueryError<BaseQuery>, meta: BaseQueryMeta<BaseQuery>, arg: QueryArg): unknown;\n  /**\r\n   * Defaults to `true`.\r\n   *\r\n   * Most apps should leave this setting on. The only time it can be a performance issue\r\n   * is if an API returns extremely large amounts of data (e.g. 10,000 rows per request) and\r\n   * you're unable to paginate it.\r\n   *\r\n   * For details of how this works, please see the below. When it is set to `false`,\r\n   * every request will cause subscribed components to rerender, even when the data has not changed.\r\n   *\r\n   * @see https://redux-toolkit.js.org/api/other-exports#copywithstructuralsharing\r\n   */\n\n  structuralSharing?: boolean;\n}\ninterface EndpointDefinitionWithQueryFn<QueryArg, BaseQuery extends BaseQueryFn, ResultType> {\n  /**\r\n   * Can be used in place of `query` as an inline function that bypasses `baseQuery` completely for the endpoint.\r\n   *\r\n   * @example\r\n   * ```ts\r\n   * // codeblock-meta title=\"Basic queryFn example\"\r\n   *\r\n   * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'\r\n   * interface Post {\r\n   *   id: number\r\n   *   name: string\r\n   * }\r\n   * type PostsResponse = Post[]\r\n   *\r\n   * const api = createApi({\r\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\r\n   *   endpoints: (build) => ({\r\n   *     getPosts: build.query<PostsResponse, void>({\r\n   *       query: () => 'posts',\r\n   *     }),\r\n   *     flipCoin: build.query<'heads' | 'tails', void>({\r\n   *       // highlight-start\r\n   *       queryFn(arg, queryApi, extraOptions, baseQuery) {\r\n   *         const randomVal = Math.random()\r\n   *         if (randomVal < 0.45) {\r\n   *           return { data: 'heads' }\r\n   *         }\r\n   *         if (randomVal < 0.9) {\r\n   *           return { data: 'tails' }\r\n   *         }\r\n   *         return { error: { status: 500, statusText: 'Internal Server Error', data: \"Coin landed on it's edge!\" } }\r\n   *       }\r\n   *       // highlight-end\r\n   *     })\r\n   *   })\r\n   * })\r\n   * ```\r\n   */\n  queryFn(arg: QueryArg, api: BaseQueryApi, extraOptions: BaseQueryExtraOptions<BaseQuery>, baseQuery: (arg: Parameters<BaseQuery>[0]) => ReturnType<BaseQuery>): MaybePromise<QueryReturnValue<ResultType, BaseQueryError<BaseQuery>>>;\n  query?: never;\n  transformResponse?: never;\n  transformErrorResponse?: never;\n  /**\r\n   * Defaults to `true`.\r\n   *\r\n   * Most apps should leave this setting on. The only time it can be a performance issue\r\n   * is if an API returns extremely large amounts of data (e.g. 10,000 rows per request) and\r\n   * you're unable to paginate it.\r\n   *\r\n   * For details of how this works, please see the below. When it is set to `false`,\r\n   * every request will cause subscribed components to rerender, even when the data has not changed.\r\n   *\r\n   * @see https://redux-toolkit.js.org/api/other-exports#copywithstructuralsharing\r\n   */\n\n  structuralSharing?: boolean;\n}\nexport interface BaseEndpointTypes<QueryArg, BaseQuery extends BaseQueryFn, ResultType> {\n  QueryArg: QueryArg;\n  BaseQuery: BaseQuery;\n  ResultType: ResultType;\n}\nexport type BaseEndpointDefinition<QueryArg, BaseQuery extends BaseQueryFn, ResultType> = (([CastAny<BaseQueryResult<BaseQuery>, {}>] extends [NEVER] ? never : EndpointDefinitionWithQuery<QueryArg, BaseQuery, ResultType>) | EndpointDefinitionWithQueryFn<QueryArg, BaseQuery, ResultType>) & {\n  /* phantom type */\n  [resultType]?: ResultType;\n  /* phantom type */\n\n  [baseQuery]?: BaseQuery;\n} & HasRequiredProps<BaseQueryExtraOptions<BaseQuery>, {\n  extraOptions: BaseQueryExtraOptions<BaseQuery>;\n}, {\n  extraOptions?: BaseQueryExtraOptions<BaseQuery>;\n}>;\nexport enum DefinitionType {\n  query = 'query',\n  mutation = 'mutation',\n}\nexport type GetResultDescriptionFn<TagTypes extends string, ResultType, QueryArg, ErrorType, MetaType> = (result: ResultType | undefined, error: ErrorType | undefined, arg: QueryArg, meta: MetaType) => ReadonlyArray<TagDescription<TagTypes>>;\nexport type FullTagDescription<TagType> = {\n  type: TagType;\n  id?: number | string;\n};\nexport type TagDescription<TagType> = TagType | FullTagDescription<TagType>;\nexport type ResultDescription<TagTypes extends string, ResultType, QueryArg, ErrorType, MetaType> = ReadonlyArray<TagDescription<TagTypes>> | GetResultDescriptionFn<TagTypes, ResultType, QueryArg, ErrorType, MetaType>;\nexport interface QueryTypes<QueryArg, BaseQuery extends BaseQueryFn, TagTypes extends string, ResultType, ReducerPath extends string = string> extends BaseEndpointTypes<QueryArg, BaseQuery, ResultType> {\n  /**\r\n   * The endpoint definition type. To be used with some internal generic types.\r\n   * @example\r\n   * ```ts\r\n   * const useMyWrappedHook: UseQuery<typeof api.endpoints.query.Types.QueryDefinition> = ...\r\n   * ```\r\n   */\n  QueryDefinition: QueryDefinition<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>;\n  TagTypes: TagTypes;\n  ReducerPath: ReducerPath;\n}\nexport interface QueryExtraOptions<TagTypes extends string, ResultType, QueryArg, BaseQuery extends BaseQueryFn, ReducerPath extends string = string> {\n  type: DefinitionType.query;\n  /**\r\n   * Used by `query` endpoints. Determines which 'tag' is attached to the cached data returned by the query.\r\n   * Expects an array of tag type strings, an array of objects of tag types with ids, or a function that returns such an array.\r\n   * 1.  `['Post']` - equivalent to `2`\r\n   * 2.  `[{ type: 'Post' }]` - equivalent to `1`\r\n   * 3.  `[{ type: 'Post', id: 1 }]`\r\n   * 4.  `(result, error, arg) => ['Post']` - equivalent to `5`\r\n   * 5.  `(result, error, arg) => [{ type: 'Post' }]` - equivalent to `4`\r\n   * 6.  `(result, error, arg) => [{ type: 'Post', id: 1 }]`\r\n   *\r\n   * @example\r\n   *\r\n   * ```ts\r\n   * // codeblock-meta title=\"providesTags example\"\r\n   *\r\n   * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'\r\n   * interface Post {\r\n   *   id: number\r\n   *   name: string\r\n   * }\r\n   * type PostsResponse = Post[]\r\n   *\r\n   * const api = createApi({\r\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\r\n   *   tagTypes: ['Posts'],\r\n   *   endpoints: (build) => ({\r\n   *     getPosts: build.query<PostsResponse, void>({\r\n   *       query: () => 'posts',\r\n   *       // highlight-start\r\n   *       providesTags: (result) =>\r\n   *         result\r\n   *           ? [\r\n   *               ...result.map(({ id }) => ({ type: 'Posts' as const, id })),\r\n   *               { type: 'Posts', id: 'LIST' },\r\n   *             ]\r\n   *           : [{ type: 'Posts', id: 'LIST' }],\r\n   *       // highlight-end\r\n   *     })\r\n   *   })\r\n   * })\r\n   * ```\r\n   */\n\n  providesTags?: ResultDescription<TagTypes, ResultType, QueryArg, BaseQueryError<BaseQuery>, BaseQueryMeta<BaseQuery>>;\n  /**\r\n   * Not to be used. A query should not invalidate tags in the cache.\r\n   */\n\n  invalidatesTags?: never;\n  /**\r\n   * Can be provided to return a custom cache key value based on the query arguments.\r\n   *\r\n   * This is primarily intended for cases where a non-serializable value is passed as part of the query arg object and should be excluded from the cache key.  It may also be used for cases where an endpoint should only have a single cache entry, such as an infinite loading / pagination implementation.\r\n   *\r\n   * Unlike the `createApi` version which can _only_ return a string, this per-endpoint option can also return an an object, number, or boolean.  If it returns a string, that value will be used as the cache key directly.  If it returns an object / number / boolean, that value will be passed to the built-in `defaultSerializeQueryArgs`.  This simplifies the use case of stripping out args you don't want included in the cache key.\r\n   *\r\n   *\r\n   * @example\r\n   *\r\n   * ```ts\r\n   * // codeblock-meta title=\"serializeQueryArgs : exclude value\"\r\n   *\r\n   * import { createApi, fetchBaseQuery, defaultSerializeQueryArgs } from '@reduxjs/toolkit/query/react'\r\n   * interface Post {\r\n   *   id: number\r\n   *   name: string\r\n   * }\r\n   *\r\n   * interface MyApiClient {\r\n   *   fetchPost: (id: string) => Promise<Post>\r\n   * }\r\n   *\r\n   * createApi({\r\n   *  baseQuery: fetchBaseQuery({ baseUrl: '/' }),\r\n   *  endpoints: (build) => ({\r\n   *    // Example: an endpoint with an API client passed in as an argument,\r\n   *    // but only the item ID should be used as the cache key\r\n   *    getPost: build.query<Post, { id: string; client: MyApiClient }>({\r\n   *      queryFn: async ({ id, client }) => {\r\n   *        const post = await client.fetchPost(id)\r\n   *        return { data: post }\r\n   *      },\r\n   *      // highlight-start\r\n   *      serializeQueryArgs: ({ queryArgs, endpointDefinition, endpointName }) => {\r\n   *        const { id } = queryArgs\r\n   *        // This can return a string, an object, a number, or a boolean.\r\n   *        // If it returns an object, number or boolean, that value\r\n   *        // will be serialized automatically via `defaultSerializeQueryArgs`\r\n   *        return { id } // omit `client` from the cache key\r\n   *\r\n   *        // Alternately, you can use `defaultSerializeQueryArgs` yourself:\r\n   *        // return defaultSerializeQueryArgs({\r\n   *        //   endpointName,\r\n   *        //   queryArgs: { id },\r\n   *        //   endpointDefinition\r\n   *        // })\r\n   *        // Or  create and return a string yourself:\r\n   *        // return `getPost(${id})`\r\n   *      },\r\n   *      // highlight-end\r\n   *    }),\r\n   *  }),\r\n   *})\r\n   * ```\r\n   */\n\n  serializeQueryArgs?: SerializeQueryArgs<QueryArg, string | number | boolean | Record<any, any>>;\n  /**\r\n   * Can be provided to merge an incoming response value into the current cache data.\r\n   * If supplied, no automatic structural sharing will be applied - it's up to\r\n   * you to update the cache appropriately.\r\n   *\r\n   * Since RTKQ normally replaces cache entries with the new response, you will usually\r\n   * need to use this with the `serializeQueryArgs` or `forceRefetch` options to keep\r\n   * an existing cache entry so that it can be updated.\r\n   *\r\n   * Since this is wrapped with Immer, you may either mutate the `currentCacheValue` directly,\r\n   * or return a new value, but _not_ both at once.\r\n   *\r\n   * Will only be called if the existing `currentCacheData` is _not_ `undefined` - on first response,\r\n   * the cache entry will just save the response data directly.\r\n   *\r\n   * Useful if you don't want a new request to completely override the current cache value,\r\n   * maybe because you have manually updated it from another source and don't want those\r\n   * updates to get lost.\r\n   *\r\n   *\r\n   * @example\r\n   *\r\n   * ```ts\r\n   * // codeblock-meta title=\"merge: pagination\"\r\n   *\r\n   * import { createApi, fetchBaseQuery, defaultSerializeQueryArgs } from '@reduxjs/toolkit/query/react'\r\n   * interface Post {\r\n   *   id: number\r\n   *   name: string\r\n   * }\r\n   *\r\n   * createApi({\r\n   *  baseQuery: fetchBaseQuery({ baseUrl: '/' }),\r\n   *  endpoints: (build) => ({\r\n   *    listItems: build.query<string[], number>({\r\n   *      query: (pageNumber) => `/listItems?page=${pageNumber}`,\r\n   *     // Only have one cache entry because the arg always maps to one string\r\n   *     serializeQueryArgs: ({ endpointName }) => {\r\n   *       return endpointName\r\n   *      },\r\n   *      // Always merge incoming data to the cache entry\r\n   *      merge: (currentCache, newItems) => {\r\n   *        currentCache.push(...newItems)\r\n   *      },\r\n   *      // Refetch when the page arg changes\r\n   *      forceRefetch({ currentArg, previousArg }) {\r\n   *        return currentArg !== previousArg\r\n   *      },\r\n   *    }),\r\n   *  }),\r\n   *})\r\n   * ```\r\n   */\n\n  merge?(currentCacheData: ResultType, responseData: ResultType, otherArgs: {\n    arg: QueryArg;\n    baseQueryMeta: BaseQueryMeta<BaseQuery>;\n    requestId: string;\n    fulfilledTimeStamp: number;\n  }): ResultType | void;\n  /**\r\n   * Check to see if the endpoint should force a refetch in cases where it normally wouldn't.\r\n   * This is primarily useful for \"infinite scroll\" / pagination use cases where\r\n   * RTKQ is keeping a single cache entry that is added to over time, in combination\r\n   * with `serializeQueryArgs` returning a fixed cache key and a `merge` callback\r\n   * set to add incoming data to the cache entry each time.\r\n   *\r\n   * @example\r\n   *\r\n   * ```ts\r\n   * // codeblock-meta title=\"forceRefresh: pagination\"\r\n   *\r\n   * import { createApi, fetchBaseQuery, defaultSerializeQueryArgs } from '@reduxjs/toolkit/query/react'\r\n   * interface Post {\r\n   *   id: number\r\n   *   name: string\r\n   * }\r\n   *\r\n   * createApi({\r\n   *  baseQuery: fetchBaseQuery({ baseUrl: '/' }),\r\n   *  endpoints: (build) => ({\r\n   *    listItems: build.query<string[], number>({\r\n   *      query: (pageNumber) => `/listItems?page=${pageNumber}`,\r\n   *     // Only have one cache entry because the arg always maps to one string\r\n   *     serializeQueryArgs: ({ endpointName }) => {\r\n   *       return endpointName\r\n   *      },\r\n   *      // Always merge incoming data to the cache entry\r\n   *      merge: (currentCache, newItems) => {\r\n   *        currentCache.push(...newItems)\r\n   *      },\r\n   *      // Refetch when the page arg changes\r\n   *      forceRefetch({ currentArg, previousArg }) {\r\n   *        return currentArg !== previousArg\r\n   *      },\r\n   *    }),\r\n   *  }),\r\n   *})\r\n   * ```\r\n   */\n\n  forceRefetch?(params: {\n    currentArg: QueryArg | undefined;\n    previousArg: QueryArg | undefined;\n    state: RootState<any, any, string>;\n    endpointState?: QuerySubState<any>;\n  }): boolean;\n  /**\r\n   * All of these are `undefined` at runtime, purely to be used in TypeScript declarations!\r\n   */\n\n  Types?: QueryTypes<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>;\n}\nexport type QueryDefinition<QueryArg, BaseQuery extends BaseQueryFn, TagTypes extends string, ResultType, ReducerPath extends string = string> = BaseEndpointDefinition<QueryArg, BaseQuery, ResultType> & QueryExtraOptions<TagTypes, ResultType, QueryArg, BaseQuery, ReducerPath>;\nexport interface MutationTypes<QueryArg, BaseQuery extends BaseQueryFn, TagTypes extends string, ResultType, ReducerPath extends string = string> extends BaseEndpointTypes<QueryArg, BaseQuery, ResultType> {\n  /**\r\n   * The endpoint definition type. To be used with some internal generic types.\r\n   * @example\r\n   * ```ts\r\n   * const useMyWrappedHook: UseMutation<typeof api.endpoints.query.Types.MutationDefinition> = ...\r\n   * ```\r\n   */\n  MutationDefinition: MutationDefinition<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>;\n  TagTypes: TagTypes;\n  ReducerPath: ReducerPath;\n}\nexport interface MutationExtraOptions<TagTypes extends string, ResultType, QueryArg, BaseQuery extends BaseQueryFn, ReducerPath extends string = string> {\n  type: DefinitionType.mutation;\n  /**\r\n   * Used by `mutation` endpoints. Determines which cached data should be either re-fetched or removed from the cache.\r\n   * Expects the same shapes as `providesTags`.\r\n   *\r\n   * @example\r\n   *\r\n   * ```ts\r\n   * // codeblock-meta title=\"invalidatesTags example\"\r\n   * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'\r\n   * interface Post {\r\n   *   id: number\r\n   *   name: string\r\n   * }\r\n   * type PostsResponse = Post[]\r\n   *\r\n   * const api = createApi({\r\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\r\n   *   tagTypes: ['Posts'],\r\n   *   endpoints: (build) => ({\r\n   *     getPosts: build.query<PostsResponse, void>({\r\n   *       query: () => 'posts',\r\n   *       providesTags: (result) =>\r\n   *         result\r\n   *           ? [\r\n   *               ...result.map(({ id }) => ({ type: 'Posts' as const, id })),\r\n   *               { type: 'Posts', id: 'LIST' },\r\n   *             ]\r\n   *           : [{ type: 'Posts', id: 'LIST' }],\r\n   *     }),\r\n   *     addPost: build.mutation<Post, Partial<Post>>({\r\n   *       query(body) {\r\n   *         return {\r\n   *           url: `posts`,\r\n   *           method: 'POST',\r\n   *           body,\r\n   *         }\r\n   *       },\r\n   *       // highlight-start\r\n   *       invalidatesTags: [{ type: 'Posts', id: 'LIST' }],\r\n   *       // highlight-end\r\n   *     }),\r\n   *   })\r\n   * })\r\n   * ```\r\n   */\n\n  invalidatesTags?: ResultDescription<TagTypes, ResultType, QueryArg, BaseQueryError<BaseQuery>, BaseQueryMeta<BaseQuery>>;\n  /**\r\n   * Not to be used. A mutation should not provide tags to the cache.\r\n   */\n\n  providesTags?: never;\n  /**\r\n   * All of these are `undefined` at runtime, purely to be used in TypeScript declarations!\r\n   */\n\n  Types?: MutationTypes<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>;\n}\nexport type MutationDefinition<QueryArg, BaseQuery extends BaseQueryFn, TagTypes extends string, ResultType, ReducerPath extends string = string> = BaseEndpointDefinition<QueryArg, BaseQuery, ResultType> & MutationExtraOptions<TagTypes, ResultType, QueryArg, BaseQuery, ReducerPath>;\nexport type EndpointDefinition<QueryArg, BaseQuery extends BaseQueryFn, TagTypes extends string, ResultType, ReducerPath extends string = string> = QueryDefinition<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath> | MutationDefinition<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>;\nexport type EndpointDefinitions = Record<string, EndpointDefinition<any, any, any, any>>;\nexport function isQueryDefinition(e: EndpointDefinition<any, any, any, any>): e is QueryDefinition<any, any, any, any> {\n  return e.type === DefinitionType.query;\n}\nexport function isMutationDefinition(e: EndpointDefinition<any, any, any, any>): e is MutationDefinition<any, any, any, any> {\n  return e.type === DefinitionType.mutation;\n}\nexport type EndpointBuilder<BaseQuery extends BaseQueryFn, TagTypes extends string, ReducerPath extends string> = {\n  /**\r\n   * An endpoint definition that retrieves data, and may provide tags to the cache.\r\n   *\r\n   * @example\r\n   * ```js\r\n   * // codeblock-meta title=\"Example of all query endpoint options\"\r\n   * const api = createApi({\r\n   *  baseQuery,\r\n   *  endpoints: (build) => ({\r\n   *    getPost: build.query({\r\n   *      query: (id) => ({ url: `post/${id}` }),\r\n   *      // Pick out data and prevent nested properties in a hook or selector\r\n   *      transformResponse: (response) => response.data,\r\n   *      // Pick out error and prevent nested properties in a hook or selector\r\n   *      transformErrorResponse: (response) => response.error,\r\n   *      // `result` is the server response\r\n   *      providesTags: (result, error, id) => [{ type: 'Post', id }],\r\n   *      // trigger side effects or optimistic updates\r\n   *      onQueryStarted(id, { dispatch, getState, extra, requestId, queryFulfilled, getCacheEntry, updateCachedData }) {},\r\n   *      // handle subscriptions etc\r\n   *      onCacheEntryAdded(id, { dispatch, getState, extra, requestId, cacheEntryRemoved, cacheDataLoaded, getCacheEntry, updateCachedData }) {},\r\n   *    }),\r\n   *  }),\r\n   *});\r\n   *```\r\n   */\n  query<ResultType, QueryArg>(definition: OmitFromUnion<QueryDefinition<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>, 'type'>): QueryDefinition<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>;\n  /**\r\n   * An endpoint definition that alters data on the server or will possibly invalidate the cache.\r\n   *\r\n   * @example\r\n   * ```js\r\n   * // codeblock-meta title=\"Example of all mutation endpoint options\"\r\n   * const api = createApi({\r\n   *   baseQuery,\r\n   *   endpoints: (build) => ({\r\n   *     updatePost: build.mutation({\r\n   *       query: ({ id, ...patch }) => ({ url: `post/${id}`, method: 'PATCH', body: patch }),\r\n   *       // Pick out data and prevent nested properties in a hook or selector\r\n   *       transformResponse: (response) => response.data,\r\n   *       // Pick out error and prevent nested properties in a hook or selector\r\n   *       transformErrorResponse: (response) => response.error,\r\n   *       // `result` is the server response\r\n   *       invalidatesTags: (result, error, id) => [{ type: 'Post', id }],\r\n   *      // trigger side effects or optimistic updates\r\n   *      onQueryStarted(id, { dispatch, getState, extra, requestId, queryFulfilled, getCacheEntry }) {},\r\n   *      // handle subscriptions etc\r\n   *      onCacheEntryAdded(id, { dispatch, getState, extra, requestId, cacheEntryRemoved, cacheDataLoaded, getCacheEntry }) {},\r\n   *     }),\r\n   *   }),\r\n   * });\r\n   * ```\r\n   */\n\n  mutation<ResultType, QueryArg>(definition: OmitFromUnion<MutationDefinition<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>, 'type'>): MutationDefinition<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>;\n};\nexport type AssertTagTypes = <T extends FullTagDescription<string>>(t: T) => T;\nexport function calculateProvidedBy<ResultType, QueryArg, ErrorType, MetaType>(description: ResultDescription<string, ResultType, QueryArg, ErrorType, MetaType> | undefined, result: ResultType | undefined, error: ErrorType | undefined, queryArg: QueryArg, meta: MetaType | undefined, assertTagTypes: AssertTagTypes): readonly FullTagDescription<string>[] {\n  if (isFunction(description)) {\n    return description((result as ResultType), (error as undefined), queryArg, (meta as MetaType)).map(expandTagDescription).map(assertTagTypes);\n  }\n\n  if (Array.isArray(description)) {\n    return description.map(expandTagDescription).map(assertTagTypes);\n  }\n\n  return [];\n}\n\nfunction isFunction<T>(t: T): t is Extract<T, Function> {\n  return typeof t === 'function';\n}\n\nexport function expandTagDescription(description: TagDescription<string>): FullTagDescription<string> {\n  return typeof description === 'string' ? {\n    type: description\n  } : description;\n}\nexport type QueryArgFrom<D extends BaseEndpointDefinition<any, any, any>> = D extends BaseEndpointDefinition<infer QA, any, any> ? QA : unknown;\nexport type ResultTypeFrom<D extends BaseEndpointDefinition<any, any, any>> = D extends BaseEndpointDefinition<any, any, infer RT> ? RT : unknown;\nexport type ReducerPathFrom<D extends EndpointDefinition<any, any, any, any, any>> = D extends EndpointDefinition<any, any, any, any, infer RP> ? RP : unknown;\nexport type TagTypesFrom<D extends EndpointDefinition<any, any, any, any>> = D extends EndpointDefinition<any, any, infer RP, any> ? RP : unknown;\nexport type TagTypesFromApi<T> = T extends Api<any, any, any, infer TagTypes> ? TagTypes : never;\nexport type DefinitionsFromApi<T> = T extends Api<any, infer Definitions, any, any> ? Definitions : never;\nexport type TransformedResponse<NewDefinitions extends EndpointDefinitions, K, ResultType> = K extends keyof NewDefinitions ? NewDefinitions[K]['transformResponse'] extends undefined ? ResultType : UnwrapPromise<ReturnType<NonUndefined<NewDefinitions[K]['transformResponse']>>> : ResultType;\nexport type OverrideResultType<Definition, NewResultType> = Definition extends QueryDefinition<infer QueryArg, infer BaseQuery, infer TagTypes, any, infer ReducerPath> ? QueryDefinition<QueryArg, BaseQuery, TagTypes, NewResultType, ReducerPath> : Definition extends MutationDefinition<infer QueryArg, infer BaseQuery, infer TagTypes, any, infer ReducerPath> ? MutationDefinition<QueryArg, BaseQuery, TagTypes, NewResultType, ReducerPath> : never;\nexport type UpdateDefinitions<Definitions extends EndpointDefinitions, NewTagTypes extends string, NewDefinitions extends EndpointDefinitions> = { [K in keyof Definitions]: Definitions[K] extends QueryDefinition<infer QueryArg, infer BaseQuery, any, infer ResultType, infer ReducerPath> ? QueryDefinition<QueryArg, BaseQuery, NewTagTypes, TransformedResponse<NewDefinitions, K, ResultType>, ReducerPath> : Definitions[K] extends MutationDefinition<infer QueryArg, infer BaseQuery, any, infer ResultType, infer ReducerPath> ? MutationDefinition<QueryArg, BaseQuery, NewTagTypes, TransformedResponse<NewDefinitions, K, ResultType>, ReducerPath> : never };", "import { formatProdErrorMessage as _formatProdErrorMessage } from \"@reduxjs/toolkit\";\nimport type { EndpointDefinitions, QueryDefinition, MutationDefinition, QueryArgFrom, ResultTypeFrom } from '../endpointDefinitions';\nimport { DefinitionType, isQueryDefinition } from '../endpointDefinitions';\nimport type { QueryThunk, MutationThunk, QueryThunkArg } from './buildThunks';\nimport type { UnknownAction, ThunkAction, SerializedError } from '@reduxjs/toolkit';\nimport type { SubscriptionOptions, RootState } from './apiState';\nimport type { InternalSerializeQueryArgs } from '../defaultSerializeQueryArgs';\nimport type { Api, ApiContext } from '../apiTypes';\nimport type { ApiEndpointQuery } from './module';\nimport type { BaseQueryError, QueryReturnValue } from '../baseQueryTypes';\nimport type { QueryResultSelectorResult } from './buildSelectors';\nimport type { Dispatch } from 'redux';\nimport { isNotNullish } from '../utils/isNotNullish';\nimport { countObjectKeys } from '../utils/countObjectKeys';\ndeclare module './module' {\n  export interface ApiEndpointQuery<Definition extends QueryDefinition<any, any, any, any, any>, // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  Definitions extends EndpointDefinitions> {\n    initiate: StartQueryActionCreator<Definition>;\n  }\n  export interface ApiEndpointMutation<Definition extends MutationDefinition<any, any, any, any, any>, // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  Definitions extends EndpointDefinitions> {\n    initiate: StartMutationActionCreator<Definition>;\n  }\n}\nexport const forceQueryFnSymbol = Symbol('forceQueryFn');\nexport const isUpsertQuery = (arg: QueryThunkArg) => typeof arg[forceQueryFnSymbol] === 'function';\nexport interface StartQueryActionCreatorOptions {\n  subscribe?: boolean;\n  forceRefetch?: boolean | number;\n  subscriptionOptions?: SubscriptionOptions;\n  [forceQueryFnSymbol]?: () => QueryReturnValue;\n}\ntype StartQueryActionCreator<D extends QueryDefinition<any, any, any, any, any>> = (arg: QueryArgFrom<D>, options?: StartQueryActionCreatorOptions) => ThunkAction<QueryActionCreatorResult<D>, any, any, UnknownAction>;\nexport type QueryActionCreatorResult<D extends QueryDefinition<any, any, any, any>> = Promise<QueryResultSelectorResult<D>> & {\n  arg: QueryArgFrom<D>;\n  requestId: string;\n  subscriptionOptions: SubscriptionOptions | undefined;\n  abort(): void;\n  unwrap(): Promise<ResultTypeFrom<D>>;\n  unsubscribe(): void;\n  refetch(): QueryActionCreatorResult<D>;\n  updateSubscriptionOptions(options: SubscriptionOptions): void;\n  queryCacheKey: string;\n};\ntype StartMutationActionCreator<D extends MutationDefinition<any, any, any, any>> = (arg: QueryArgFrom<D>, options?: {\n  /**\r\n   * If this mutation should be tracked in the store.\r\n   * If you just want to manually trigger this mutation using `dispatch` and don't care about the\r\n   * result, state & potential errors being held in store, you can set this to false.\r\n   * (defaults to `true`)\r\n   */\n  track?: boolean;\n  fixedCacheKey?: string;\n}) => ThunkAction<MutationActionCreatorResult<D>, any, any, UnknownAction>;\nexport type MutationActionCreatorResult<D extends MutationDefinition<any, any, any, any>> = Promise<{\n  data: ResultTypeFrom<D>;\n} | {\n  error: Exclude<BaseQueryError<D extends MutationDefinition<any, infer BaseQuery, any, any> ? BaseQuery : never>, undefined> | SerializedError;\n}> & {\n  /** @internal */\n  arg: {\n    /**\r\n     * The name of the given endpoint for the mutation\r\n     */\n    endpointName: string;\n    /**\r\n     * The original arguments supplied to the mutation call\r\n     */\n\n    originalArgs: QueryArgFrom<D>;\n    /**\r\n     * Whether the mutation is being tracked in the store.\r\n     */\n\n    track?: boolean;\n    fixedCacheKey?: string;\n  };\n  /**\r\n   * A unique string generated for the request sequence\r\n   */\n\n  requestId: string;\n  /**\r\n   * A method to cancel the mutation promise. Note that this is not intended to prevent the mutation\r\n   * that was fired off from reaching the server, but only to assist in handling the response.\r\n   *\r\n   * Calling `abort()` prior to the promise resolving will force it to reach the error state with\r\n   * the serialized error:\r\n   * `{ name: 'AbortError', message: 'Aborted' }`\r\n   *\r\n   * @example\r\n   * ```ts\r\n   * const [updateUser] = useUpdateUserMutation();\r\n   *\r\n   * useEffect(() => {\r\n   *   const promise = updateUser(id);\r\n   *   promise\r\n   *     .unwrap()\r\n   *     .catch((err) => {\r\n   *       if (err.name === 'AbortError') return;\r\n   *       // else handle the unexpected error\r\n   *     })\r\n   *\r\n   *   return () => {\r\n   *     promise.abort();\r\n   *   }\r\n   * }, [id, updateUser])\r\n   * ```\r\n   */\n\n  abort(): void;\n  /**\r\n   * Unwraps a mutation call to provide the raw response/error.\r\n   *\r\n   * @remarks\r\n   * If you need to access the error or success payload immediately after a mutation, you can chain .unwrap().\r\n   *\r\n   * @example\r\n   * ```ts\r\n   * // codeblock-meta title=\"Using .unwrap\"\r\n   * addPost({ id: 1, name: 'Example' })\r\n   *   .unwrap()\r\n   *   .then((payload) => console.log('fulfilled', payload))\r\n   *   .catch((error) => console.error('rejected', error));\r\n   * ```\r\n   *\r\n   * @example\r\n   * ```ts\r\n   * // codeblock-meta title=\"Using .unwrap with async await\"\r\n   * try {\r\n   *   const payload = await addPost({ id: 1, name: 'Example' }).unwrap();\r\n   *   console.log('fulfilled', payload)\r\n   * } catch (error) {\r\n   *   console.error('rejected', error);\r\n   * }\r\n   * ```\r\n   */\n\n  unwrap(): Promise<ResultTypeFrom<D>>;\n  /**\r\n   * A method to manually unsubscribe from the mutation call, meaning it will be removed from cache after the usual caching grace period.\r\n   The value returned by the hook will reset to `isUninitialized` afterwards.\r\n   */\n\n  reset(): void;\n};\nexport function buildInitiate({\n  serializeQueryArgs,\n  queryThunk,\n  mutationThunk,\n  api,\n  context\n}: {\n  serializeQueryArgs: InternalSerializeQueryArgs;\n  queryThunk: QueryThunk;\n  mutationThunk: MutationThunk;\n  api: Api<any, EndpointDefinitions, any, any>;\n  context: ApiContext<EndpointDefinitions>;\n}) {\n  const runningQueries: Map<Dispatch, Record<string, QueryActionCreatorResult<any> | undefined>> = new Map();\n  const runningMutations: Map<Dispatch, Record<string, MutationActionCreatorResult<any> | undefined>> = new Map();\n  const {\n    unsubscribeQueryResult,\n    removeMutationResult,\n    updateSubscriptionOptions\n  } = api.internalActions;\n  return {\n    buildInitiateQuery,\n    buildInitiateMutation,\n    getRunningQueryThunk,\n    getRunningMutationThunk,\n    getRunningQueriesThunk,\n    getRunningMutationsThunk\n  };\n\n  function getRunningQueryThunk(endpointName: string, queryArgs: any) {\n    return (dispatch: Dispatch) => {\n      const endpointDefinition = context.endpointDefinitions[endpointName];\n      const queryCacheKey = serializeQueryArgs({\n        queryArgs,\n        endpointDefinition,\n        endpointName\n      });\n      return (runningQueries.get(dispatch)?.[queryCacheKey] as QueryActionCreatorResult<never> | undefined);\n    };\n  }\n\n  function getRunningMutationThunk(\n  /**\r\n   * this is only here to allow TS to infer the result type by input value\r\n   * we could use it to validate the result, but it's probably not necessary\r\n   */\n  _endpointName: string, fixedCacheKeyOrRequestId: string) {\n    return (dispatch: Dispatch) => {\n      return (runningMutations.get(dispatch)?.[fixedCacheKeyOrRequestId] as MutationActionCreatorResult<never> | undefined);\n    };\n  }\n\n  function getRunningQueriesThunk() {\n    return (dispatch: Dispatch) => Object.values(runningQueries.get(dispatch) || {}).filter(isNotNullish);\n  }\n\n  function getRunningMutationsThunk() {\n    return (dispatch: Dispatch) => Object.values(runningMutations.get(dispatch) || {}).filter(isNotNullish);\n  }\n\n  function middlewareWarning(dispatch: Dispatch) {\n    if (process.env.NODE_ENV !== 'production') {\n      if ((middlewareWarning as any).triggered) return;\n      const returnedValue = dispatch(api.internalActions.internal_getRTKQSubscriptions());\n      (middlewareWarning as any).triggered = true; // The RTKQ middleware should return the internal state object,\n      // but it should _not_ be the action object.\n\n      if (typeof returnedValue !== 'object' || typeof returnedValue?.type === 'string') {\n        // Otherwise, must not have been added\n        throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(34) : `Warning: Middleware for RTK-Query API at reducerPath \"${api.reducerPath}\" has not been added to the store.\nYou must add the middleware for RTK-Query to function correctly!`);\n      }\n    }\n  }\n\n  function buildInitiateQuery(endpointName: string, endpointDefinition: QueryDefinition<any, any, any, any>) {\n    const queryAction: StartQueryActionCreator<any> = (arg, {\n      subscribe = true,\n      forceRefetch,\n      subscriptionOptions,\n      [forceQueryFnSymbol]: forceQueryFn\n    } = {}) => (dispatch, getState) => {\n      const queryCacheKey = serializeQueryArgs({\n        queryArgs: arg,\n        endpointDefinition,\n        endpointName\n      });\n      const thunk = queryThunk({\n        type: 'query',\n        subscribe,\n        forceRefetch: forceRefetch,\n        subscriptionOptions,\n        endpointName,\n        originalArgs: arg,\n        queryCacheKey,\n        [forceQueryFnSymbol]: forceQueryFn\n      });\n      const selector = (api.endpoints[endpointName] as ApiEndpointQuery<any, any>).select(arg);\n      const thunkResult = dispatch(thunk);\n      const stateAfter = selector(getState());\n      middlewareWarning(dispatch);\n      const {\n        requestId,\n        abort\n      } = thunkResult;\n      const skippedSynchronously = stateAfter.requestId !== requestId;\n      const runningQuery = runningQueries.get(dispatch)?.[queryCacheKey];\n\n      const selectFromState = () => selector(getState());\n\n      const statePromise: QueryActionCreatorResult<any> = Object.assign(forceQueryFn ? // a query has been forced (upsertQueryData)\n      // -> we want to resolve it once data has been written with the data that will be written\n      thunkResult.then(selectFromState) : skippedSynchronously && !runningQuery ? // a query has been skipped due to a condition and we do not have any currently running query\n      // -> we want to resolve it immediately with the current data\n      Promise.resolve(stateAfter) : // query just started or one is already in flight\n      // -> wait for the running query, then resolve with data from after that\n      Promise.all([runningQuery, thunkResult]).then(selectFromState), {\n        arg,\n        requestId,\n        subscriptionOptions,\n        queryCacheKey,\n        abort,\n\n        async unwrap() {\n          const result = await statePromise;\n\n          if (result.isError) {\n            throw result.error;\n          }\n\n          return result.data;\n        },\n\n        refetch: () => dispatch(queryAction(arg, {\n          subscribe: false,\n          forceRefetch: true\n        })),\n\n        unsubscribe() {\n          if (subscribe) dispatch(unsubscribeQueryResult({\n            queryCacheKey,\n            requestId\n          }));\n        },\n\n        updateSubscriptionOptions(options: SubscriptionOptions) {\n          statePromise.subscriptionOptions = options;\n          dispatch(updateSubscriptionOptions({\n            endpointName,\n            requestId,\n            queryCacheKey,\n            options\n          }));\n        }\n\n      });\n\n      if (!runningQuery && !skippedSynchronously && !forceQueryFn) {\n        const running = runningQueries.get(dispatch) || {};\n        running[queryCacheKey] = statePromise;\n        runningQueries.set(dispatch, running);\n        statePromise.then(() => {\n          delete running[queryCacheKey];\n\n          if (!countObjectKeys(running)) {\n            runningQueries.delete(dispatch);\n          }\n        });\n      }\n\n      return statePromise;\n    };\n\n    return queryAction;\n  }\n\n  function buildInitiateMutation(endpointName: string): StartMutationActionCreator<any> {\n    return (arg, {\n      track = true,\n      fixedCacheKey\n    } = {}) => (dispatch, getState) => {\n      const thunk = mutationThunk({\n        type: 'mutation',\n        endpointName,\n        originalArgs: arg,\n        track,\n        fixedCacheKey\n      });\n      const thunkResult = dispatch(thunk);\n      middlewareWarning(dispatch);\n      const {\n        requestId,\n        abort,\n        unwrap\n      } = thunkResult;\n      const returnValuePromise = thunkResult.unwrap().then(data => ({\n        data\n      })).catch(error => ({\n        error\n      }));\n\n      const reset = () => {\n        dispatch(removeMutationResult({\n          requestId,\n          fixedCacheKey\n        }));\n      };\n\n      const ret = Object.assign(returnValuePromise, {\n        arg: thunkResult.arg,\n        requestId,\n        abort,\n        unwrap,\n        reset\n      });\n      const running = runningMutations.get(dispatch) || {};\n      runningMutations.set(dispatch, running);\n      running[requestId] = ret;\n      ret.then(() => {\n        delete running[requestId];\n\n        if (!countObjectKeys(running)) {\n          runningMutations.delete(dispatch);\n        }\n      });\n\n      if (fixedCacheKey) {\n        running[fixedCacheKey] = ret;\n        ret.then(() => {\n          if (running[fixedCacheKey] === ret) {\n            delete running[fixedCacheKey];\n\n            if (!countObjectKeys(running)) {\n              runningMutations.delete(dispatch);\n            }\n          }\n        });\n      }\n\n      return ret;\n    };\n  }\n}", "export function isNotNullish<T>(v: T | null | undefined): v is T {\n  return v != null;\n}", "// Fast method for counting an object's keys\n// without resorting to `Object.keys(obj).length\n// Will this make a big difference in perf? Probably not\n// But we can save a few allocations.\nexport function countObjectKeys(obj: Record<any, any>) {\n  let count = 0;\n\n  for (const _key in obj) {\n    count++;\n  }\n\n  return count;\n}", "import type { InternalSerializeQueryArgs } from '../defaultSerializeQueryArgs';\nimport type { Api, ApiContext } from '../apiTypes';\nimport type { BaseQueryFn, BaseQueryError, QueryReturnValue } from '../baseQueryTypes';\nimport type { RootState, QueryKeys, QuerySubstateIdentifier } from './apiState';\nimport { QueryStatus } from './apiState';\nimport type { StartQueryActionCreatorOptions, QueryActionCreatorResult } from './buildInitiate';\nimport { forceQueryFnSymbol, isUpsertQuery } from './buildInitiate';\nimport type { AssertTagTypes, EndpointDefinition, EndpointDefinitions, MutationDefinition, QueryArgFrom, QueryDefinition, ResultTypeFrom, FullTagDescription } from '../endpointDefinitions';\nimport { isQueryDefinition } from '../endpointDefinitions';\nimport { calculateProvidedBy } from '../endpointDefinitions';\nimport type { AsyncThunkPayloadCreator, Draft, UnknownAction } from '@reduxjs/toolkit';\nimport { isAllOf, isFulfilled, isPending, isRejected, isRejectedWithValue, createAsyncThunk, SHOULD_AUTOBATCH } from './rtkImports';\nimport type { Patch } from 'immer';\nimport { isDraftable, produceWithPatches } from 'immer';\nimport type { ThunkAction, ThunkDispatch, AsyncThunk } from '@reduxjs/toolkit';\nimport { HandledError } from '../HandledError';\nimport type { ApiEndpointQuery, PrefetchOptions } from './module';\nimport type { UnwrapPromise } from '../tsHelpers';\ndeclare module './module' {\n  export interface ApiEndpointQuery<Definition extends QueryDefinition<any, any, any, any, any>, // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  Definitions extends EndpointDefinitions> extends Matchers<QueryThunk, Definition> {}\n  export interface ApiEndpointMutation<Definition extends MutationDefinition<any, any, any, any, any>, // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  Definitions extends EndpointDefinitions> extends Matchers<MutationThunk, Definition> {}\n}\ntype EndpointThunk<Thunk extends QueryThunk | MutationThunk, Definition extends EndpointDefinition<any, any, any, any>> = Definition extends EndpointDefinition<infer QueryArg, infer BaseQueryFn, any, infer ResultType> ? Thunk extends AsyncThunk<unknown, infer ATArg, infer ATConfig> ? AsyncThunk<ResultType, ATArg & {\n  originalArgs: QueryArg;\n}, ATConfig & {\n  rejectValue: BaseQueryError<BaseQueryFn>;\n}> : never : never;\nexport type PendingAction<Thunk extends QueryThunk | MutationThunk, Definition extends EndpointDefinition<any, any, any, any>> = ReturnType<EndpointThunk<Thunk, Definition>['pending']>;\nexport type FulfilledAction<Thunk extends QueryThunk | MutationThunk, Definition extends EndpointDefinition<any, any, any, any>> = ReturnType<EndpointThunk<Thunk, Definition>['fulfilled']>;\nexport type RejectedAction<Thunk extends QueryThunk | MutationThunk, Definition extends EndpointDefinition<any, any, any, any>> = ReturnType<EndpointThunk<Thunk, Definition>['rejected']>;\nexport type Matcher<M> = (value: any) => value is M;\nexport interface Matchers<Thunk extends QueryThunk | MutationThunk, Definition extends EndpointDefinition<any, any, any, any>> {\n  matchPending: Matcher<PendingAction<Thunk, Definition>>;\n  matchFulfilled: Matcher<FulfilledAction<Thunk, Definition>>;\n  matchRejected: Matcher<RejectedAction<Thunk, Definition>>;\n}\nexport interface QueryThunkArg extends QuerySubstateIdentifier, StartQueryActionCreatorOptions {\n  type: 'query';\n  originalArgs: unknown;\n  endpointName: string;\n}\nexport interface MutationThunkArg {\n  type: 'mutation';\n  originalArgs: unknown;\n  endpointName: string;\n  track?: boolean;\n  fixedCacheKey?: string;\n}\nexport type ThunkResult = unknown;\nexport type ThunkApiMetaConfig = {\n  pendingMeta: {\n    startedTimeStamp: number;\n    [SHOULD_AUTOBATCH]: true;\n  };\n  fulfilledMeta: {\n    fulfilledTimeStamp: number;\n    baseQueryMeta: unknown;\n    [SHOULD_AUTOBATCH]: true;\n  };\n  rejectedMeta: {\n    baseQueryMeta: unknown;\n    [SHOULD_AUTOBATCH]: true;\n  };\n};\nexport type QueryThunk = AsyncThunk<ThunkResult, QueryThunkArg, ThunkApiMetaConfig>;\nexport type MutationThunk = AsyncThunk<ThunkResult, MutationThunkArg, ThunkApiMetaConfig>;\n\nfunction defaultTransformResponse(baseQueryReturnValue: unknown) {\n  return baseQueryReturnValue;\n}\n\nexport type MaybeDrafted<T> = T | Draft<T>;\nexport type Recipe<T> = (data: MaybeDrafted<T>) => void | MaybeDrafted<T>;\nexport type UpsertRecipe<T> = (data: MaybeDrafted<T> | undefined) => void | MaybeDrafted<T>;\nexport type PatchQueryDataThunk<Definitions extends EndpointDefinitions, PartialState> = <EndpointName extends QueryKeys<Definitions>>(endpointName: EndpointName, args: QueryArgFrom<Definitions[EndpointName]>, patches: readonly Patch[], updateProvided?: boolean) => ThunkAction<void, PartialState, any, UnknownAction>;\nexport type UpdateQueryDataThunk<Definitions extends EndpointDefinitions, PartialState> = <EndpointName extends QueryKeys<Definitions>>(endpointName: EndpointName, args: QueryArgFrom<Definitions[EndpointName]>, updateRecipe: Recipe<ResultTypeFrom<Definitions[EndpointName]>>, updateProvided?: boolean) => ThunkAction<PatchCollection, PartialState, any, UnknownAction>;\nexport type UpsertQueryDataThunk<Definitions extends EndpointDefinitions, PartialState> = <EndpointName extends QueryKeys<Definitions>>(endpointName: EndpointName, args: QueryArgFrom<Definitions[EndpointName]>, value: ResultTypeFrom<Definitions[EndpointName]>) => ThunkAction<QueryActionCreatorResult<Definitions[EndpointName] extends QueryDefinition<any, any, any, any> ? Definitions[EndpointName] : never>, PartialState, any, UnknownAction>;\n/**\r\n * An object returned from dispatching a `api.util.updateQueryData` call.\r\n */\n\nexport type PatchCollection = {\n  /**\r\n   * An `immer` Patch describing the cache update.\r\n   */\n  patches: Patch[];\n  /**\r\n   * An `immer` Patch to revert the cache update.\r\n   */\n\n  inversePatches: Patch[];\n  /**\r\n   * A function that will undo the cache update.\r\n   */\n\n  undo: () => void;\n};\nexport function buildThunks<BaseQuery extends BaseQueryFn, ReducerPath extends string, Definitions extends EndpointDefinitions>({\n  reducerPath,\n  baseQuery,\n  context: {\n    endpointDefinitions\n  },\n  serializeQueryArgs,\n  api,\n  assertTagType\n}: {\n  baseQuery: BaseQuery;\n  reducerPath: ReducerPath;\n  context: ApiContext<Definitions>;\n  serializeQueryArgs: InternalSerializeQueryArgs;\n  api: Api<BaseQuery, Definitions, ReducerPath, any>;\n  assertTagType: AssertTagTypes;\n}) {\n  type State = RootState<any, string, ReducerPath>;\n\n  const patchQueryData: PatchQueryDataThunk<EndpointDefinitions, State> = (endpointName, args, patches, updateProvided) => (dispatch, getState) => {\n    const endpointDefinition = endpointDefinitions[endpointName];\n    const queryCacheKey = serializeQueryArgs({\n      queryArgs: args,\n      endpointDefinition,\n      endpointName\n    });\n    dispatch(api.internalActions.queryResultPatched({\n      queryCacheKey,\n      patches\n    }));\n\n    if (!updateProvided) {\n      return;\n    }\n\n    const newValue = api.endpoints[endpointName].select(args)(( // Work around TS 4.1 mismatch\n    getState() as RootState<any, any, any>));\n    const providedTags = calculateProvidedBy(endpointDefinition.providesTags, newValue.data, undefined, args, {}, assertTagType);\n    dispatch(api.internalActions.updateProvidedBy({\n      queryCacheKey,\n      providedTags\n    }));\n  };\n\n  const updateQueryData: UpdateQueryDataThunk<EndpointDefinitions, State> = (endpointName, args, updateRecipe, updateProvided = true) => (dispatch, getState) => {\n    const endpointDefinition = api.endpoints[endpointName];\n    const currentState = endpointDefinition.select(args)(( // Work around TS 4.1 mismatch\n    getState() as RootState<any, any, any>));\n    let ret: PatchCollection = {\n      patches: [],\n      inversePatches: [],\n      undo: () => dispatch(api.util.patchQueryData(endpointName, args, ret.inversePatches, updateProvided))\n    };\n\n    if (currentState.status === QueryStatus.uninitialized) {\n      return ret;\n    }\n\n    let newValue;\n\n    if ('data' in currentState) {\n      if (isDraftable(currentState.data)) {\n        const [value, patches, inversePatches] = produceWithPatches(currentState.data, updateRecipe);\n        ret.patches.push(...patches);\n        ret.inversePatches.push(...inversePatches);\n        newValue = value;\n      } else {\n        newValue = updateRecipe(currentState.data);\n        ret.patches.push({\n          op: 'replace',\n          path: [],\n          value: newValue\n        });\n        ret.inversePatches.push({\n          op: 'replace',\n          path: [],\n          value: currentState.data\n        });\n      }\n    }\n\n    dispatch(api.util.patchQueryData(endpointName, args, ret.patches, updateProvided));\n    return ret;\n  };\n\n  const upsertQueryData: UpsertQueryDataThunk<Definitions, State> = (endpointName, args, value) => dispatch => {\n    return dispatch((api.endpoints[endpointName] as ApiEndpointQuery<QueryDefinition<any, any, any, any, any>, Definitions>).initiate(args, {\n      subscribe: false,\n      forceRefetch: true,\n      [forceQueryFnSymbol]: () => ({\n        data: value\n      })\n    }));\n  };\n\n  const executeEndpoint: AsyncThunkPayloadCreator<ThunkResult, QueryThunkArg | MutationThunkArg, ThunkApiMetaConfig & {\n    state: RootState<any, string, ReducerPath>;\n  }> = async (arg, {\n    signal,\n    abort,\n    rejectWithValue,\n    fulfillWithValue,\n    dispatch,\n    getState,\n    extra\n  }) => {\n    const endpointDefinition = endpointDefinitions[arg.endpointName];\n\n    try {\n      let transformResponse: (baseQueryReturnValue: any, meta: any, arg: any) => any = defaultTransformResponse;\n      let result: QueryReturnValue;\n      const baseQueryApi = {\n        signal,\n        abort,\n        dispatch,\n        getState,\n        extra,\n        endpoint: arg.endpointName,\n        type: arg.type,\n        forced: arg.type === 'query' ? isForcedQuery(arg, getState()) : undefined\n      };\n      const forceQueryFn = arg.type === 'query' ? arg[forceQueryFnSymbol] : undefined;\n\n      if (forceQueryFn) {\n        result = forceQueryFn();\n      } else if (endpointDefinition.query) {\n        result = await baseQuery(endpointDefinition.query(arg.originalArgs), baseQueryApi, (endpointDefinition.extraOptions as any));\n\n        if (endpointDefinition.transformResponse) {\n          transformResponse = endpointDefinition.transformResponse;\n        }\n      } else {\n        result = await endpointDefinition.queryFn(arg.originalArgs, baseQueryApi, (endpointDefinition.extraOptions as any), arg => baseQuery(arg, baseQueryApi, (endpointDefinition.extraOptions as any)));\n      }\n\n      if (typeof process !== 'undefined' && process.env.NODE_ENV === 'development') {\n        const what = endpointDefinition.query ? '`baseQuery`' : '`queryFn`';\n        let err: undefined | string;\n\n        if (!result) {\n          err = `${what} did not return anything.`;\n        } else if (typeof result !== 'object') {\n          err = `${what} did not return an object.`;\n        } else if (result.error && result.data) {\n          err = `${what} returned an object containing both \\`error\\` and \\`result\\`.`;\n        } else if (result.error === undefined && result.data === undefined) {\n          err = `${what} returned an object containing neither a valid \\`error\\` and \\`result\\`. At least one of them should not be \\`undefined\\``;\n        } else {\n          for (const key of Object.keys(result)) {\n            if (key !== 'error' && key !== 'data' && key !== 'meta') {\n              err = `The object returned by ${what} has the unknown property ${key}.`;\n              break;\n            }\n          }\n        }\n\n        if (err) {\n          console.error(`Error encountered handling the endpoint ${arg.endpointName}.\n              ${err}\n              It needs to return an object with either the shape \\`{ data: <value> }\\` or \\`{ error: <value> }\\` that may contain an optional \\`meta\\` property.\n              Object returned was:`, result);\n        }\n      }\n\n      if (result.error) throw new HandledError(result.error, result.meta);\n      return fulfillWithValue(await transformResponse(result.data, result.meta, arg.originalArgs), {\n        fulfilledTimeStamp: Date.now(),\n        baseQueryMeta: result.meta,\n        [SHOULD_AUTOBATCH]: true\n      });\n    } catch (error) {\n      let catchedError = error;\n\n      if (catchedError instanceof HandledError) {\n        let transformErrorResponse: (baseQueryReturnValue: any, meta: any, arg: any) => any = defaultTransformResponse;\n\n        if (endpointDefinition.query && endpointDefinition.transformErrorResponse) {\n          transformErrorResponse = endpointDefinition.transformErrorResponse;\n        }\n\n        try {\n          return rejectWithValue(await transformErrorResponse(catchedError.value, catchedError.meta, arg.originalArgs), {\n            baseQueryMeta: catchedError.meta,\n            [SHOULD_AUTOBATCH]: true\n          });\n        } catch (e) {\n          catchedError = e;\n        }\n      }\n\n      if (typeof process !== 'undefined' && process.env.NODE_ENV !== 'production') {\n        console.error(`An unhandled error occurred processing a request for the endpoint \"${arg.endpointName}\".\nIn the case of an unhandled error, no tags will be \"provided\" or \"invalidated\".`, catchedError);\n      } else {\n        console.error(catchedError);\n      }\n\n      throw catchedError;\n    }\n  };\n\n  function isForcedQuery(arg: QueryThunkArg, state: RootState<any, string, ReducerPath>) {\n    const requestState = state[reducerPath]?.queries?.[arg.queryCacheKey];\n    const baseFetchOnMountOrArgChange = state[reducerPath]?.config.refetchOnMountOrArgChange;\n    const fulfilledVal = requestState?.fulfilledTimeStamp;\n    const refetchVal = arg.forceRefetch ?? (arg.subscribe && baseFetchOnMountOrArgChange);\n\n    if (refetchVal) {\n      // Return if its true or compare the dates because it must be a number\n      return refetchVal === true || (Number(new Date()) - Number(fulfilledVal)) / 1000 >= refetchVal;\n    }\n\n    return false;\n  }\n\n  const queryThunk = createAsyncThunk<ThunkResult, QueryThunkArg, ThunkApiMetaConfig & {\n    state: RootState<any, string, ReducerPath>;\n  }>(`${reducerPath}/executeQuery`, executeEndpoint, {\n    getPendingMeta() {\n      return {\n        startedTimeStamp: Date.now(),\n        [SHOULD_AUTOBATCH]: true\n      };\n    },\n\n    condition(queryThunkArgs, {\n      getState\n    }) {\n      const state = getState();\n      const requestState = state[reducerPath]?.queries?.[queryThunkArgs.queryCacheKey];\n      const fulfilledVal = requestState?.fulfilledTimeStamp;\n      const currentArg = queryThunkArgs.originalArgs;\n      const previousArg = requestState?.originalArgs;\n      const endpointDefinition = endpointDefinitions[queryThunkArgs.endpointName]; // Order of these checks matters.\n      // In order for `upsertQueryData` to successfully run while an existing request is in flight,\n      /// we have to check for that first, otherwise `queryThunk` will bail out and not run at all.\n\n      if (isUpsertQuery(queryThunkArgs)) {\n        return true;\n      } // Don't retry a request that's currently in-flight\n\n\n      if (requestState?.status === 'pending') {\n        return false;\n      } // if this is forced, continue\n\n\n      if (isForcedQuery(queryThunkArgs, state)) {\n        return true;\n      }\n\n      if (isQueryDefinition(endpointDefinition) && endpointDefinition?.forceRefetch?.({\n        currentArg,\n        previousArg,\n        endpointState: requestState,\n        state\n      })) {\n        return true;\n      } // Pull from the cache unless we explicitly force refetch or qualify based on time\n\n\n      if (fulfilledVal) {\n        // Value is cached and we didn't specify to refresh, skip it.\n        return false;\n      }\n\n      return true;\n    },\n\n    dispatchConditionRejection: true\n  });\n  const mutationThunk = createAsyncThunk<ThunkResult, MutationThunkArg, ThunkApiMetaConfig & {\n    state: RootState<any, string, ReducerPath>;\n  }>(`${reducerPath}/executeMutation`, executeEndpoint, {\n    getPendingMeta() {\n      return {\n        startedTimeStamp: Date.now(),\n        [SHOULD_AUTOBATCH]: true\n      };\n    }\n\n  });\n\n  const hasTheForce = (options: any): options is {\n    force: boolean;\n  } => 'force' in options;\n\n  const hasMaxAge = (options: any): options is {\n    ifOlderThan: false | number;\n  } => 'ifOlderThan' in options;\n\n  const prefetch = <EndpointName extends QueryKeys<Definitions>,>(endpointName: EndpointName, arg: any, options: PrefetchOptions): ThunkAction<void, any, any, UnknownAction> => (dispatch: ThunkDispatch<any, any, any>, getState: () => any) => {\n    const force = hasTheForce(options) && options.force;\n    const maxAge = hasMaxAge(options) && options.ifOlderThan;\n\n    const queryAction = (force: boolean = true) => (api.endpoints[endpointName] as ApiEndpointQuery<any, any>).initiate(arg, {\n      forceRefetch: force\n    });\n\n    const latestStateValue = (api.endpoints[endpointName] as ApiEndpointQuery<any, any>).select(arg)(getState());\n\n    if (force) {\n      dispatch(queryAction());\n    } else if (maxAge) {\n      const lastFulfilledTs = latestStateValue?.fulfilledTimeStamp;\n\n      if (!lastFulfilledTs) {\n        dispatch(queryAction());\n        return;\n      }\n\n      const shouldRetrigger = (Number(new Date()) - Number(new Date(lastFulfilledTs))) / 1000 >= maxAge;\n\n      if (shouldRetrigger) {\n        dispatch(queryAction());\n      }\n    } else {\n      // If prefetching with no options, just let it try\n      dispatch(queryAction(false));\n    }\n  };\n\n  function matchesEndpoint(endpointName: string) {\n    return (action: any): action is UnknownAction => action?.meta?.arg?.endpointName === endpointName;\n  }\n\n  function buildMatchThunkActions<Thunk extends AsyncThunk<any, QueryThunkArg, ThunkApiMetaConfig> | AsyncThunk<any, MutationThunkArg, ThunkApiMetaConfig>>(thunk: Thunk, endpointName: string) {\n    return ({\n      matchPending: isAllOf(isPending(thunk), matchesEndpoint(endpointName)),\n      matchFulfilled: isAllOf(isFulfilled(thunk), matchesEndpoint(endpointName)),\n      matchRejected: isAllOf(isRejected(thunk), matchesEndpoint(endpointName))\n    } as Matchers<Thunk, any>);\n  }\n\n  return {\n    queryThunk,\n    mutationThunk,\n    prefetch,\n    updateQueryData,\n    upsertQueryData,\n    patchQueryData,\n    buildMatchThunkActions\n  };\n}\nexport function calculateProvidedByThunk(action: UnwrapPromise<ReturnType<ReturnType<QueryThunk>> | ReturnType<ReturnType<MutationThunk>>>, type: 'providesTags' | 'invalidatesTags', endpointDefinitions: EndpointDefinitions, assertTagType: AssertTagTypes) {\n  return calculateProvidedBy(endpointDefinitions[action.meta.arg.endpointName][type], isFulfilled(action) ? action.payload : undefined, isRejectedWithValue(action) ? action.payload : undefined, action.meta.arg.originalArgs, 'baseQueryMeta' in action.meta ? action.meta.baseQueryMeta : undefined, assertTagType);\n}", "import type { Action, PayloadAction, UnknownAction } from '@reduxjs/toolkit';\nimport { combineReducers, createAction, createSlice, isAnyOf, isFulfilled, isRejectedWithValue, createNextState, prepareAutoBatched } from './rtkImports';\nimport type { QuerySubstateIdentifier, QuerySubState, MutationSubstateIdentifier, MutationSubState, MutationState, QueryState, InvalidationState, Subscribers, QueryCacheKey, SubscriptionState, ConfigState } from './apiState';\nimport { QueryStatus } from './apiState';\nimport type { MutationThunk, QueryThunk, RejectedAction } from './buildThunks';\nimport { calculateProvidedByThunk } from './buildThunks';\nimport type { AssertTagTypes, EndpointDefinitions, FullTagDescription, QueryDefinition } from '../endpointDefinitions';\nimport type { Patch } from 'immer';\nimport { isDraft } from 'immer';\nimport { applyPatches, original } from 'immer';\nimport { onFocus, onFocusLost, onOffline, onOnline } from './setupListeners';\nimport { isDocumentVisible, isOnline, copyWithStructuralSharing } from '../utils';\nimport type { ApiContext } from '../apiTypes';\nimport { isUpsertQuery } from './buildInitiate';\n\nfunction updateQuerySubstateIfExists(state: QueryState<any>, queryCacheKey: QueryCacheKey, update: (substate: QuerySubState<any>) => void) {\n  const substate = state[queryCacheKey];\n\n  if (substate) {\n    update(substate);\n  }\n}\n\nexport function getMutationCacheKey(id: MutationSubstateIdentifier | {\n  requestId: string;\n  arg: {\n    fixedCacheKey?: string | undefined;\n  };\n}): string;\nexport function getMutationCacheKey(id: {\n  fixedCacheKey?: string;\n  requestId?: string;\n}): string | undefined;\nexport function getMutationCacheKey(id: {\n  fixedCacheKey?: string;\n  requestId?: string;\n} | MutationSubstateIdentifier | {\n  requestId: string;\n  arg: {\n    fixedCacheKey?: string | undefined;\n  };\n}): string | undefined {\n  return ('arg' in id ? id.arg.fixedCacheKey : id.fixedCacheKey) ?? id.requestId;\n}\n\nfunction updateMutationSubstateIfExists(state: MutationState<any>, id: MutationSubstateIdentifier | {\n  requestId: string;\n  arg: {\n    fixedCacheKey?: string | undefined;\n  };\n}, update: (substate: MutationSubState<any>) => void) {\n  const substate = state[getMutationCacheKey(id)];\n\n  if (substate) {\n    update(substate);\n  }\n}\n\nconst initialState = ({} as any);\nexport function buildSlice({\n  reducerPath,\n  queryThunk,\n  mutationThunk,\n  context: {\n    endpointDefinitions: definitions,\n    apiUid,\n    extractRehydrationInfo,\n    hasRehydrationInfo\n  },\n  assertTagType,\n  config\n}: {\n  reducerPath: string;\n  queryThunk: QueryThunk;\n  mutationThunk: MutationThunk;\n  context: ApiContext<EndpointDefinitions>;\n  assertTagType: AssertTagTypes;\n  config: Omit<ConfigState<string>, 'online' | 'focused' | 'middlewareRegistered'>;\n}) {\n  const resetApiState = createAction(`${reducerPath}/resetApiState`);\n  const querySlice = createSlice({\n    name: `${reducerPath}/queries`,\n    initialState: (initialState as QueryState<any>),\n    reducers: {\n      removeQueryResult: {\n        reducer(draft, {\n          payload: {\n            queryCacheKey\n          }\n        }: PayloadAction<QuerySubstateIdentifier>) {\n          delete draft[queryCacheKey];\n        },\n\n        prepare: prepareAutoBatched<QuerySubstateIdentifier>()\n      },\n      queryResultPatched: {\n        reducer(draft, {\n          payload: {\n            queryCacheKey,\n            patches\n          }\n        }: PayloadAction<QuerySubstateIdentifier & {\n          patches: readonly Patch[];\n        }>) {\n          updateQuerySubstateIfExists(draft, queryCacheKey, substate => {\n            substate.data = applyPatches((substate.data as any), patches.concat());\n          });\n        },\n\n        prepare: prepareAutoBatched<QuerySubstateIdentifier & {\n          patches: readonly Patch[];\n        }>()\n      }\n    },\n\n    extraReducers(builder) {\n      builder.addCase(queryThunk.pending, (draft, {\n        meta,\n        meta: {\n          arg\n        }\n      }) => {\n        const upserting = isUpsertQuery(arg);\n        draft[arg.queryCacheKey] ??= {\n          status: QueryStatus.uninitialized,\n          endpointName: arg.endpointName\n        };\n        updateQuerySubstateIfExists(draft, arg.queryCacheKey, substate => {\n          substate.status = QueryStatus.pending;\n          substate.requestId = upserting && substate.requestId ? // for `upsertQuery` **updates**, keep the current `requestId`\n          substate.requestId : // for normal queries or `upsertQuery` **inserts** always update the `requestId`\n          meta.requestId;\n\n          if (arg.originalArgs !== undefined) {\n            substate.originalArgs = arg.originalArgs;\n          }\n\n          substate.startedTimeStamp = meta.startedTimeStamp;\n        });\n      }).addCase(queryThunk.fulfilled, (draft, {\n        meta,\n        payload\n      }) => {\n        updateQuerySubstateIfExists(draft, meta.arg.queryCacheKey, substate => {\n          if (substate.requestId !== meta.requestId && !isUpsertQuery(meta.arg)) return;\n          const {\n            merge\n          } = (definitions[meta.arg.endpointName] as QueryDefinition<any, any, any, any>);\n          substate.status = QueryStatus.fulfilled;\n\n          if (merge) {\n            if (substate.data !== undefined) {\n              const {\n                fulfilledTimeStamp,\n                arg,\n                baseQueryMeta,\n                requestId\n              } = meta; // There's existing cache data. Let the user merge it in themselves.\n              // We're already inside an Immer-powered reducer, and the user could just mutate `substate.data`\n              // themselves inside of `merge()`. But, they might also want to return a new value.\n              // Try to let Immer figure that part out, save the result, and assign it to `substate.data`.\n\n              let newData = createNextState(substate.data, draftSubstateData => {\n                // As usual with Immer, you can mutate _or_ return inside here, but not both\n                return merge(draftSubstateData, payload, {\n                  arg: arg.originalArgs,\n                  baseQueryMeta,\n                  fulfilledTimeStamp,\n                  requestId\n                });\n              });\n              substate.data = newData;\n            } else {\n              // Presumably a fresh request. Just cache the response data.\n              substate.data = payload;\n            }\n          } else {\n            // Assign or safely update the cache data.\n            substate.data = definitions[meta.arg.endpointName].structuralSharing ?? true ? copyWithStructuralSharing(isDraft(substate.data) ? original(substate.data) : substate.data, payload) : payload;\n          }\n\n          delete substate.error;\n          substate.fulfilledTimeStamp = meta.fulfilledTimeStamp;\n        });\n      }).addCase(queryThunk.rejected, (draft, {\n        meta: {\n          condition,\n          arg,\n          requestId\n        },\n        error,\n        payload\n      }) => {\n        updateQuerySubstateIfExists(draft, arg.queryCacheKey, substate => {\n          if (condition) {// request was aborted due to condition (another query already running)\n          } else {\n            // request failed\n            if (substate.requestId !== requestId) return;\n            substate.status = QueryStatus.rejected;\n            substate.error = (payload ?? error as any);\n          }\n        });\n      }).addMatcher(hasRehydrationInfo, (draft, action) => {\n        const {\n          queries\n        } = extractRehydrationInfo(action)!;\n\n        for (const [key, entry] of Object.entries(queries)) {\n          if ( // do not rehydrate entries that were currently in flight.\n          entry?.status === QueryStatus.fulfilled || entry?.status === QueryStatus.rejected) {\n            draft[key] = entry;\n          }\n        }\n      });\n    }\n\n  });\n  const mutationSlice = createSlice({\n    name: `${reducerPath}/mutations`,\n    initialState: (initialState as MutationState<any>),\n    reducers: {\n      removeMutationResult: {\n        reducer(draft, {\n          payload\n        }: PayloadAction<MutationSubstateIdentifier>) {\n          const cacheKey = getMutationCacheKey(payload);\n\n          if (cacheKey in draft) {\n            delete draft[cacheKey];\n          }\n        },\n\n        prepare: prepareAutoBatched<MutationSubstateIdentifier>()\n      }\n    },\n\n    extraReducers(builder) {\n      builder.addCase(mutationThunk.pending, (draft, {\n        meta,\n        meta: {\n          requestId,\n          arg,\n          startedTimeStamp\n        }\n      }) => {\n        if (!arg.track) return;\n        draft[getMutationCacheKey(meta)] = {\n          requestId,\n          status: QueryStatus.pending,\n          endpointName: arg.endpointName,\n          startedTimeStamp\n        };\n      }).addCase(mutationThunk.fulfilled, (draft, {\n        payload,\n        meta\n      }) => {\n        if (!meta.arg.track) return;\n        updateMutationSubstateIfExists(draft, meta, substate => {\n          if (substate.requestId !== meta.requestId) return;\n          substate.status = QueryStatus.fulfilled;\n          substate.data = payload;\n          substate.fulfilledTimeStamp = meta.fulfilledTimeStamp;\n        });\n      }).addCase(mutationThunk.rejected, (draft, {\n        payload,\n        error,\n        meta\n      }) => {\n        if (!meta.arg.track) return;\n        updateMutationSubstateIfExists(draft, meta, substate => {\n          if (substate.requestId !== meta.requestId) return;\n          substate.status = QueryStatus.rejected;\n          substate.error = (payload ?? error as any);\n        });\n      }).addMatcher(hasRehydrationInfo, (draft, action) => {\n        const {\n          mutations\n        } = extractRehydrationInfo(action)!;\n\n        for (const [key, entry] of Object.entries(mutations)) {\n          if ( // do not rehydrate entries that were currently in flight.\n          (entry?.status === QueryStatus.fulfilled || entry?.status === QueryStatus.rejected) && // only rehydrate endpoints that were persisted using a `fixedCacheKey`\n          key !== entry?.requestId) {\n            draft[key] = entry;\n          }\n        }\n      });\n    }\n\n  });\n  const invalidationSlice = createSlice({\n    name: `${reducerPath}/invalidation`,\n    initialState: (initialState as InvalidationState<string>),\n    reducers: {\n      updateProvidedBy: {\n        reducer(draft, action: PayloadAction<{\n          queryCacheKey: QueryCacheKey;\n          providedTags: readonly FullTagDescription<string>[];\n        }>) {\n          const {\n            queryCacheKey,\n            providedTags\n          } = action.payload;\n\n          for (const tagTypeSubscriptions of Object.values(draft)) {\n            for (const idSubscriptions of Object.values(tagTypeSubscriptions)) {\n              const foundAt = idSubscriptions.indexOf(queryCacheKey);\n\n              if (foundAt !== -1) {\n                idSubscriptions.splice(foundAt, 1);\n              }\n            }\n          }\n\n          for (const {\n            type,\n            id\n          } of providedTags) {\n            const subscribedQueries = (draft[type] ??= {})[id || '__internal_without_id'] ??= [];\n            const alreadySubscribed = subscribedQueries.includes(queryCacheKey);\n\n            if (!alreadySubscribed) {\n              subscribedQueries.push(queryCacheKey);\n            }\n          }\n        },\n\n        prepare: prepareAutoBatched<{\n          queryCacheKey: QueryCacheKey;\n          providedTags: readonly FullTagDescription<string>[];\n        }>()\n      }\n    },\n\n    extraReducers(builder) {\n      builder.addCase(querySlice.actions.removeQueryResult, (draft, {\n        payload: {\n          queryCacheKey\n        }\n      }) => {\n        for (const tagTypeSubscriptions of Object.values(draft)) {\n          for (const idSubscriptions of Object.values(tagTypeSubscriptions)) {\n            const foundAt = idSubscriptions.indexOf(queryCacheKey);\n\n            if (foundAt !== -1) {\n              idSubscriptions.splice(foundAt, 1);\n            }\n          }\n        }\n      }).addMatcher(hasRehydrationInfo, (draft, action) => {\n        const {\n          provided\n        } = extractRehydrationInfo(action)!;\n\n        for (const [type, incomingTags] of Object.entries(provided)) {\n          for (const [id, cacheKeys] of Object.entries(incomingTags)) {\n            const subscribedQueries = (draft[type] ??= {})[id || '__internal_without_id'] ??= [];\n\n            for (const queryCacheKey of cacheKeys) {\n              const alreadySubscribed = subscribedQueries.includes(queryCacheKey);\n\n              if (!alreadySubscribed) {\n                subscribedQueries.push(queryCacheKey);\n              }\n            }\n          }\n        }\n      }).addMatcher(isAnyOf(isFulfilled(queryThunk), isRejectedWithValue(queryThunk)), (draft, action) => {\n        const providedTags = calculateProvidedByThunk(action, 'providesTags', definitions, assertTagType);\n        const {\n          queryCacheKey\n        } = action.meta.arg;\n        invalidationSlice.caseReducers.updateProvidedBy(draft, invalidationSlice.actions.updateProvidedBy({\n          queryCacheKey,\n          providedTags\n        }));\n      });\n    }\n\n  }); // Dummy slice to generate actions\n\n  const subscriptionSlice = createSlice({\n    name: `${reducerPath}/subscriptions`,\n    initialState: (initialState as SubscriptionState),\n    reducers: {\n      updateSubscriptionOptions(d, a: PayloadAction<{\n        endpointName: string;\n        requestId: string;\n        options: Subscribers[number];\n      } & QuerySubstateIdentifier>) {// Dummy\n      },\n\n      unsubscribeQueryResult(d, a: PayloadAction<{\n        requestId: string;\n      } & QuerySubstateIdentifier>) {// Dummy\n      },\n\n      internal_getRTKQSubscriptions() {}\n\n    }\n  });\n  const internalSubscriptionsSlice = createSlice({\n    name: `${reducerPath}/internalSubscriptions`,\n    initialState: (initialState as SubscriptionState),\n    reducers: {\n      subscriptionsUpdated: {\n        reducer(state, action: PayloadAction<Patch[]>) {\n          return applyPatches(state, action.payload);\n        },\n\n        prepare: prepareAutoBatched<Patch[]>()\n      }\n    }\n  });\n  const configSlice = createSlice({\n    name: `${reducerPath}/config`,\n    initialState: ({\n      online: isOnline(),\n      focused: isDocumentVisible(),\n      middlewareRegistered: false,\n      ...config\n    } as ConfigState<string>),\n    reducers: {\n      middlewareRegistered(state, {\n        payload\n      }: PayloadAction<string>) {\n        state.middlewareRegistered = state.middlewareRegistered === 'conflict' || apiUid !== payload ? 'conflict' : true;\n      }\n\n    },\n    extraReducers: builder => {\n      builder.addCase(onOnline, state => {\n        state.online = true;\n      }).addCase(onOffline, state => {\n        state.online = false;\n      }).addCase(onFocus, state => {\n        state.focused = true;\n      }).addCase(onFocusLost, state => {\n        state.focused = false;\n      }) // update the state to be a new object to be picked up as a \"state change\"\n      // by redux-persist's `autoMergeLevel2`\n      .addMatcher(hasRehydrationInfo, draft => ({ ...draft\n      }));\n    }\n  });\n  const combinedReducer = combineReducers({\n    queries: querySlice.reducer,\n    mutations: mutationSlice.reducer,\n    provided: invalidationSlice.reducer,\n    subscriptions: internalSubscriptionsSlice.reducer,\n    config: configSlice.reducer\n  });\n\n  const reducer: typeof combinedReducer = (state, action) => combinedReducer(resetApiState.match(action) ? undefined : state, action);\n\n  const actions = { ...configSlice.actions,\n    ...querySlice.actions,\n    ...subscriptionSlice.actions,\n    ...internalSubscriptionsSlice.actions,\n    ...mutationSlice.actions,\n    ...invalidationSlice.actions,\n    resetApiState\n  };\n  return {\n    reducer,\n    actions\n  };\n}\nexport type SliceActions = ReturnType<typeof buildSlice>['actions'];", "import { createNextState, createSelector } from './rtkImports';\nimport type { MutationSubState, QuerySubState, RootState as _RootState, RequestStatusFlags, QueryCacheKey, QueryKeys, QueryState } from './apiState';\nimport { QueryStatus, getRequestStatusFlags } from './apiState';\nimport type { EndpointDefinitions, QueryDefinition, MutationDefinition, QueryArgFrom, TagTypesFrom, ReducerPathFrom, TagDescription } from '../endpointDefinitions';\nimport { expandTagDescription } from '../endpointDefinitions';\nimport type { InternalSerializeQueryArgs } from '../defaultSerializeQueryArgs';\nimport { getMutationCacheKey } from './buildSlice';\nimport { flatten } from '../utils';\nexport type SkipToken = typeof skipToken;\n/**\r\n * Can be passed into `useQuery`, `useQueryState` or `useQuerySubscription`\r\n * instead of the query argument to get the same effect as if setting\r\n * `skip: true` in the query options.\r\n *\r\n * Useful for scenarios where a query should be skipped when `arg` is `undefined`\r\n * and <PERSON><PERSON> complains about it because `arg` is not allowed to be passed\r\n * in as `undefined`, such as\r\n *\r\n * ```ts\r\n * // codeblock-meta title=\"will error if the query argument is not allowed to be undefined\" no-transpile\r\n * useSomeQuery(arg, { skip: !!arg })\r\n * ```\r\n *\r\n * ```ts\r\n * // codeblock-meta title=\"using skipToken instead\" no-transpile\r\n * useSomeQuery(arg ?? skipToken)\r\n * ```\r\n *\r\n * If passed directly into a query or mutation selector, that selector will always\r\n * return an uninitialized state.\r\n */\n\nexport const skipToken = /* @__PURE__ */Symbol.for('RTKQ/skipToken');\ndeclare module './module' {\n  export interface ApiEndpointQuery<Definition extends QueryDefinition<any, any, any, any, any>, Definitions extends EndpointDefinitions> {\n    select: QueryResultSelectorFactory<Definition, _RootState<Definitions, TagTypesFrom<Definition>, ReducerPathFrom<Definition>>>;\n  }\n  export interface ApiEndpointMutation<Definition extends MutationDefinition<any, any, any, any, any>, Definitions extends EndpointDefinitions> {\n    select: MutationResultSelectorFactory<Definition, _RootState<Definitions, TagTypesFrom<Definition>, ReducerPathFrom<Definition>>>;\n  }\n}\ntype QueryResultSelectorFactory<Definition extends QueryDefinition<any, any, any, any>, RootState> = (queryArg: QueryArgFrom<Definition> | SkipToken) => (state: RootState) => QueryResultSelectorResult<Definition>;\nexport type QueryResultSelectorResult<Definition extends QueryDefinition<any, any, any, any>> = QuerySubState<Definition> & RequestStatusFlags;\ntype MutationResultSelectorFactory<Definition extends MutationDefinition<any, any, any, any>, RootState> = (requestId: string | {\n  requestId: string | undefined;\n  fixedCacheKey: string | undefined;\n} | SkipToken) => (state: RootState) => MutationResultSelectorResult<Definition>;\nexport type MutationResultSelectorResult<Definition extends MutationDefinition<any, any, any, any>> = MutationSubState<Definition> & RequestStatusFlags;\nconst initialSubState: QuerySubState<any> = {\n  status: (QueryStatus.uninitialized as const)\n}; // abuse immer to freeze default states\n\nconst defaultQuerySubState = /* @__PURE__ */createNextState(initialSubState, () => {});\nconst defaultMutationSubState = /* @__PURE__ */createNextState((initialSubState as MutationSubState<any>), () => {});\nexport function buildSelectors<Definitions extends EndpointDefinitions, ReducerPath extends string>({\n  serializeQueryArgs,\n  reducerPath\n}: {\n  serializeQueryArgs: InternalSerializeQueryArgs;\n  reducerPath: ReducerPath;\n}) {\n  type RootState = _RootState<Definitions, string, string>;\n\n  const selectSkippedQuery = (state: RootState) => defaultQuerySubState;\n\n  const selectSkippedMutation = (state: RootState) => defaultMutationSubState;\n\n  return {\n    buildQuerySelector,\n    buildMutationSelector,\n    selectInvalidatedBy,\n    selectCachedArgsForQuery\n  };\n\n  function withRequestFlags<T extends {\n    status: QueryStatus;\n  }>(substate: T): T & RequestStatusFlags {\n    return { ...substate,\n      ...getRequestStatusFlags(substate.status)\n    };\n  }\n\n  function selectInternalState(rootState: RootState) {\n    const state = rootState[reducerPath];\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (!state) {\n        if ((selectInternalState as any).triggered) return state;\n        (selectInternalState as any).triggered = true;\n        console.error(`Error: No data found at \\`state.${reducerPath}\\`. Did you forget to add the reducer to the store?`);\n      }\n    }\n\n    return state;\n  }\n\n  function buildQuerySelector(endpointName: string, endpointDefinition: QueryDefinition<any, any, any, any>) {\n    return (((queryArgs: any) => {\n      const serializedArgs = serializeQueryArgs({\n        queryArgs,\n        endpointDefinition,\n        endpointName\n      });\n\n      const selectQuerySubstate = (state: RootState) => selectInternalState(state)?.queries?.[serializedArgs] ?? defaultQuerySubState;\n\n      const finalSelectQuerySubState = queryArgs === skipToken ? selectSkippedQuery : selectQuerySubstate;\n      return createSelector(finalSelectQuerySubState, withRequestFlags);\n    }) as QueryResultSelectorFactory<any, RootState>);\n  }\n\n  function buildMutationSelector() {\n    return ((id => {\n      let mutationId: string | typeof skipToken;\n\n      if (typeof id === 'object') {\n        mutationId = getMutationCacheKey(id) ?? skipToken;\n      } else {\n        mutationId = id;\n      }\n\n      const selectMutationSubstate = (state: RootState) => selectInternalState(state)?.mutations?.[(mutationId as string)] ?? defaultMutationSubState;\n\n      const finalSelectMutationSubstate = mutationId === skipToken ? selectSkippedMutation : selectMutationSubstate;\n      return createSelector(finalSelectMutationSubstate, withRequestFlags);\n    }) as MutationResultSelectorFactory<any, RootState>);\n  }\n\n  function selectInvalidatedBy(state: RootState, tags: ReadonlyArray<TagDescription<string>>): Array<{\n    endpointName: string;\n    originalArgs: any;\n    queryCacheKey: QueryCacheKey;\n  }> {\n    const apiState = state[reducerPath];\n    const toInvalidate = new Set<QueryCacheKey>();\n\n    for (const tag of tags.map(expandTagDescription)) {\n      const provided = apiState.provided[tag.type];\n\n      if (!provided) {\n        continue;\n      }\n\n      let invalidateSubscriptions = (tag.id !== undefined ? // id given: invalidate all queries that provide this type & id\n      provided[tag.id] : // no id: invalidate all queries that provide this type\n      flatten(Object.values(provided))) ?? [];\n\n      for (const invalidate of invalidateSubscriptions) {\n        toInvalidate.add(invalidate);\n      }\n    }\n\n    return flatten(Array.from(toInvalidate.values()).map(queryCacheKey => {\n      const querySubState = apiState.queries[queryCacheKey];\n      return querySubState ? [{\n        queryCacheKey,\n        endpointName: querySubState.endpointName!,\n        originalArgs: querySubState.originalArgs\n      }] : [];\n    }));\n  }\n\n  function selectCachedArgsForQuery<QueryName extends QueryKeys<Definitions>>(state: RootState, queryName: QueryName): Array<QueryArgFrom<Definitions[QueryName]>> {\n    return Object.values((state[reducerPath].queries as QueryState<any>)).filter((entry): entry is Exclude<QuerySubState<Definitions[QueryName]>, {\n      status: QueryStatus.uninitialized;\n    }> => entry?.endpointName === queryName && entry.status !== QueryStatus.uninitialized).map(entry => entry.originalArgs);\n  }\n}", "import type { QueryCacheKey } from './core/apiState';\nimport type { EndpointDefinition } from './endpointDefinitions';\nimport { isPlainObject } from './core/rtkImports';\nconst cache: WeakMap<any, string> | undefined = WeakMap ? new WeakMap() : undefined;\nexport const defaultSerializeQueryArgs: SerializeQueryArgs<any> = ({\n  endpointName,\n  queryArgs\n}) => {\n  let serialized = '';\n  const cached = cache?.get(queryArgs);\n\n  if (typeof cached === 'string') {\n    serialized = cached;\n  } else {\n    const stringified = JSON.stringify(queryArgs, (key, value) => isPlainObject(value) ? Object.keys(value).sort().reduce<any>((acc, key) => {\n      acc[key] = (value as any)[key];\n      return acc;\n    }, {}) : value);\n\n    if (isPlainObject(queryArgs)) {\n      cache?.set(queryArgs, stringified);\n    }\n\n    serialized = stringified;\n  } // Sort the object keys before stringifying, to prevent useQuery({ a: 1, b: 2 }) having a different cache key than useQuery({ b: 2, a: 1 })\n\n\n  return `${endpointName}(${serialized})`;\n};\nexport type SerializeQueryArgs<QueryArgs, ReturnType = string> = (_: {\n  queryArgs: QueryArgs;\n  endpointDefinition: EndpointDefinition<any, any, any, any>;\n  endpointName: string;\n}) => ReturnType;\nexport type InternalSerializeQueryArgs = (_: {\n  queryArgs: any;\n  endpointDefinition: EndpointDefinition<any, any, any, any>;\n  endpointName: string;\n}) => QueryCacheKey;", "import type { <PERSON><PERSON>, <PERSON>pi<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>leName } from './apiTypes';\nimport type { CombinedState } from './core/apiState';\nimport type { BaseQueryArg, BaseQueryFn } from './baseQueryTypes';\nimport type { SerializeQueryArgs } from './defaultSerializeQueryArgs';\nimport { defaultSerializeQueryArgs } from './defaultSerializeQueryArgs';\nimport type { EndpointBuilder, EndpointDefinitions } from './endpointDefinitions';\nimport { DefinitionType, isQueryDefinition } from './endpointDefinitions';\nimport { nanoid } from './core/rtkImports';\nimport type { UnknownAction } from '@reduxjs/toolkit';\nimport type { NoInfer } from './tsHelpers';\nimport { weakMapMemoize } from 'reselect';\nexport interface CreateApiOptions<BaseQuery extends BaseQueryFn, Definitions extends EndpointDefinitions, ReducerPath extends string = 'api', TagTypes extends string = never> {\n  /**\r\n   * The base query used by each endpoint if no `queryFn` option is specified. RTK Query exports a utility called [fetchBaseQuery](./fetchBaseQuery) as a lightweight wrapper around `fetch` for common use-cases. See [Customizing Queries](../../rtk-query/usage/customizing-queries) if `fetchBaseQuery` does not handle your requirements.\r\n   *\r\n   * @example\r\n   *\r\n   * ```ts\r\n   * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query'\r\n   *\r\n   * const api = createApi({\r\n   *   // highlight-start\r\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\r\n   *   // highlight-end\r\n   *   endpoints: (build) => ({\r\n   *     // ...endpoints\r\n   *   }),\r\n   * })\r\n   * ```\r\n   */\n  baseQuery: BaseQuery;\n  /**\r\n   * An array of string tag type names. Specifying tag types is optional, but you should define them so that they can be used for caching and invalidation. When defining a tag type, you will be able to [provide](../../rtk-query/usage/automated-refetching#providing-tags) them with `providesTags` and [invalidate](../../rtk-query/usage/automated-refetching#invalidating-tags) them with `invalidatesTags` when configuring [endpoints](#endpoints).\r\n   *\r\n   * @example\r\n   *\r\n   * ```ts\r\n   * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query'\r\n   *\r\n   * const api = createApi({\r\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\r\n   *   // highlight-start\r\n   *   tagTypes: ['Post', 'User'],\r\n   *   // highlight-end\r\n   *   endpoints: (build) => ({\r\n   *     // ...endpoints\r\n   *   }),\r\n   * })\r\n   * ```\r\n   */\n\n  tagTypes?: readonly TagTypes[];\n  /**\r\n   * The `reducerPath` is a _unique_ key that your service will be mounted to in your store. If you call `createApi` more than once in your application, you will need to provide a unique value each time. Defaults to `'api'`.\r\n   *\r\n   * @example\r\n   *\r\n   * ```ts\r\n   * // codeblock-meta title=\"apis.js\"\r\n   * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query';\r\n   *\r\n   * const apiOne = createApi({\r\n   *   // highlight-start\r\n   *   reducerPath: 'apiOne',\r\n   *   // highlight-end\r\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\r\n   *   endpoints: (builder) => ({\r\n   *     // ...endpoints\r\n   *   }),\r\n   * });\r\n   *\r\n   * const apiTwo = createApi({\r\n   *   // highlight-start\r\n   *   reducerPath: 'apiTwo',\r\n   *   // highlight-end\r\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\r\n   *   endpoints: (builder) => ({\r\n   *     // ...endpoints\r\n   *   }),\r\n   * });\r\n   * ```\r\n   */\n\n  reducerPath?: ReducerPath;\n  /**\r\n   * Accepts a custom function if you have a need to change the creation of cache keys for any reason.\r\n   */\n\n  serializeQueryArgs?: SerializeQueryArgs<BaseQueryArg<BaseQuery>>;\n  /**\r\n   * Endpoints are just a set of operations that you want to perform against your server. You define them as an object using the builder syntax. There are two basic endpoint types: [`query`](../../rtk-query/usage/queries) and [`mutation`](../../rtk-query/usage/mutations).\r\n   */\n\n  endpoints(build: EndpointBuilder<BaseQuery, TagTypes, ReducerPath>): Definitions;\n  /**\r\n   * Defaults to `60` _(this value is in seconds)_. This is how long RTK Query will keep your data cached for **after** the last component unsubscribes. For example, if you query an endpoint, then unmount the component, then mount another component that makes the same request within the given time frame, the most recent value will be served from the cache.\r\n   *\r\n   * ```ts\r\n   * // codeblock-meta title=\"keepUnusedDataFor example\"\r\n   *\r\n   * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'\r\n   * interface Post {\r\n   *   id: number\r\n   *   name: string\r\n   * }\r\n   * type PostsResponse = Post[]\r\n   *\r\n   * const api = createApi({\r\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\r\n   *   endpoints: (build) => ({\r\n   *     getPosts: build.query<PostsResponse, void>({\r\n   *       query: () => 'posts',\r\n   *       // highlight-start\r\n   *       keepUnusedDataFor: 5\r\n   *       // highlight-end\r\n   *     })\r\n   *   })\r\n   * })\r\n   * ```\r\n   */\n\n  keepUnusedDataFor?: number;\n  /**\r\n   * Defaults to `false`. This setting allows you to control whether if a cached result is already available RTK Query will only serve a cached result, or if it should `refetch` when set to `true` or if an adequate amount of time has passed since the last successful query result.\r\n   * - `false` - Will not cause a query to be performed _unless_ it does not exist yet.\r\n   * - `true` - Will always refetch when a new subscriber to a query is added. Behaves the same as calling the `refetch` callback or passing `forceRefetch: true` in the action creator.\r\n   * - `number` - **Value is in seconds**. If a number is provided and there is an existing query in the cache, it will compare the current time vs the last fulfilled timestamp, and only refetch if enough time has elapsed.\r\n   *\r\n   * If you specify this option alongside `skip: true`, this **will not be evaluated** until `skip` is false.\r\n   */\n\n  refetchOnMountOrArgChange?: boolean | number;\n  /**\r\n   * Defaults to `false`. This setting allows you to control whether RTK Query will try to refetch all subscribed queries after the application window regains focus.\r\n   *\r\n   * If you specify this option alongside `skip: true`, this **will not be evaluated** until `skip` is false.\r\n   *\r\n   * Note: requires [`setupListeners`](./setupListeners) to have been called.\r\n   */\n\n  refetchOnFocus?: boolean;\n  /**\r\n   * Defaults to `false`. This setting allows you to control whether RTK Query will try to refetch all subscribed queries after regaining a network connection.\r\n   *\r\n   * If you specify this option alongside `skip: true`, this **will not be evaluated** until `skip` is false.\r\n   *\r\n   * Note: requires [`setupListeners`](./setupListeners) to have been called.\r\n   */\n\n  refetchOnReconnect?: boolean;\n  /**\r\n   * Defaults to `'immediately'`. This setting allows you to control when tags are invalidated after a mutation.\r\n   *\r\n   * - `'immediately'`: Queries are invalidated instantly after the mutation finished, even if they are running.\r\n   *   If the query provides tags that were invalidated while it ran, it won't be re-fetched.\r\n   * - `'delayed'`: Invalidation only happens after all queries and mutations are settled.\r\n   *   This ensures that queries are always invalidated correctly and automatically \"batches\" invalidations of concurrent mutations.\r\n   *   Note that if you constantly have some queries (or mutations) running, this can delay tag invalidations indefinitely.\r\n   */\n\n  invalidationBehavior?: 'delayed' | 'immediately';\n  /**\r\n   * A function that is passed every dispatched action. If this returns something other than `undefined`,\r\n   * that return value will be used to rehydrate fulfilled & errored queries.\r\n   *\r\n   * @example\r\n   *\r\n   * ```ts\r\n   * // codeblock-meta title=\"next-redux-wrapper rehydration example\"\r\n   * import type { Action, PayloadAction } from '@reduxjs/toolkit'\r\n   * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'\r\n   * import { HYDRATE } from 'next-redux-wrapper'\r\n   *\r\n   * type RootState = any; // normally inferred from state\r\n   *\r\n   * function isHydrateAction(action: Action): action is PayloadAction<RootState> {\r\n   *   return action.type === HYDRATE\r\n   * }\r\n   *\r\n   * export const api = createApi({\r\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\r\n   *   // highlight-start\r\n   *   extractRehydrationInfo(action, { reducerPath }) {\r\n   *     if (isHydrateAction(action)) {\r\n   *       return action.payload[reducerPath]\r\n   *     }\r\n   *   },\r\n   *   // highlight-end\r\n   *   endpoints: (build) => ({\r\n   *     // omitted\r\n   *   }),\r\n   * })\r\n   * ```\r\n   */\n\n  extractRehydrationInfo?: (action: UnknownAction, {\n    reducerPath\n  }: {\n    reducerPath: ReducerPath;\n  }) => undefined | CombinedState<NoInfer<Definitions>, NoInfer<TagTypes>, NoInfer<ReducerPath>>;\n}\nexport type CreateApi<Modules extends ModuleName> = {\n  /**\r\n   * Creates a service to use in your application. Contains only the basic redux logic (the core module).\r\n   *\r\n   * @link https://rtk-query-docs.netlify.app/api/createApi\r\n   */\n  <BaseQuery extends BaseQueryFn, Definitions extends EndpointDefinitions, ReducerPath extends string = 'api', TagTypes extends string = never>(options: CreateApiOptions<BaseQuery, Definitions, ReducerPath, TagTypes>): Api<BaseQuery, Definitions, ReducerPath, TagTypes, Modules>;\n};\n/**\r\n * Builds a `createApi` method based on the provided `modules`.\r\n *\r\n * @link https://rtk-query-docs.netlify.app/concepts/customizing-create-api\r\n *\r\n * @example\r\n * ```ts\r\n * const MyContext = React.createContext<ReactReduxContextValue>(null as any);\r\n * const customCreateApi = buildCreateApi(\r\n *   coreModule(),\r\n *   reactHooksModule({\r\n *     hooks: {\r\n *       useDispatch: createDispatchHook(MyContext),\r\n *       useSelector: createSelectorHook(MyContext),\r\n *       useStore: createStoreHook(MyContext)\r\n *     }\r\n *   })\r\n * );\r\n * ```\r\n *\r\n * @param modules - A variable number of modules that customize how the `createApi` method handles endpoints\r\n * @returns A `createApi` method using the provided `modules`.\r\n */\n\nexport function buildCreateApi<Modules extends [Module<any>, ...Module<any>[]]>(...modules: Modules): CreateApi<Modules[number]['name']> {\n  return function baseCreateApi(options) {\n    const extractRehydrationInfo = weakMapMemoize((action: UnknownAction) => options.extractRehydrationInfo?.(action, {\n      reducerPath: (options.reducerPath ?? 'api' as any)\n    }));\n    const optionsWithDefaults: CreateApiOptions<any, any, any, any> = {\n      reducerPath: 'api',\n      keepUnusedDataFor: 60,\n      refetchOnMountOrArgChange: false,\n      refetchOnFocus: false,\n      refetchOnReconnect: false,\n      invalidationBehavior: 'delayed',\n      ...options,\n      extractRehydrationInfo,\n\n      serializeQueryArgs(queryArgsApi) {\n        let finalSerializeQueryArgs = defaultSerializeQueryArgs;\n\n        if ('serializeQueryArgs' in queryArgsApi.endpointDefinition) {\n          const endpointSQA = queryArgsApi.endpointDefinition.serializeQueryArgs!;\n\n          finalSerializeQueryArgs = queryArgsApi => {\n            const initialResult = endpointSQA(queryArgsApi);\n\n            if (typeof initialResult === 'string') {\n              // If the user function returned a string, use it as-is\n              return initialResult;\n            } else {\n              // Assume they returned an object (such as a subset of the original\n              // query args) or a primitive, and serialize it ourselves\n              return defaultSerializeQueryArgs({ ...queryArgsApi,\n                queryArgs: initialResult\n              });\n            }\n          };\n        } else if (options.serializeQueryArgs) {\n          finalSerializeQueryArgs = options.serializeQueryArgs;\n        }\n\n        return finalSerializeQueryArgs(queryArgsApi);\n      },\n\n      tagTypes: [...(options.tagTypes || [])]\n    };\n    const context: ApiContext<EndpointDefinitions> = {\n      endpointDefinitions: {},\n\n      batch(fn) {\n        // placeholder \"batch\" method to be overridden by plugins, for example with React.unstable_batchedUpdate\n        fn();\n      },\n\n      apiUid: nanoid(),\n      extractRehydrationInfo,\n      hasRehydrationInfo: weakMapMemoize(action => extractRehydrationInfo(action) != null)\n    };\n    const api = ({\n      injectEndpoints,\n\n      enhanceEndpoints({\n        addTagTypes,\n        endpoints\n      }) {\n        if (addTagTypes) {\n          for (const eT of addTagTypes) {\n            if (!optionsWithDefaults.tagTypes!.includes((eT as any))) {\n              ;\n              (optionsWithDefaults.tagTypes as any[]).push(eT);\n            }\n          }\n        }\n\n        if (endpoints) {\n          for (const [endpointName, partialDefinition] of Object.entries(endpoints)) {\n            if (typeof partialDefinition === 'function') {\n              partialDefinition(context.endpointDefinitions[endpointName]);\n            } else {\n              Object.assign(context.endpointDefinitions[endpointName] || {}, partialDefinition);\n            }\n          }\n        }\n\n        return api;\n      }\n\n    } as Api<BaseQueryFn, {}, string, string, Modules[number]['name']>);\n    const initializedModules = modules.map(m => m.init((api as any), (optionsWithDefaults as any), context));\n\n    function injectEndpoints(inject: Parameters<typeof api.injectEndpoints>[0]) {\n      const evaluatedEndpoints = inject.endpoints({\n        query: x => ({ ...x,\n          type: DefinitionType.query\n        } as any),\n        mutation: x => ({ ...x,\n          type: DefinitionType.mutation\n        } as any)\n      });\n\n      for (const [endpointName, definition] of Object.entries(evaluatedEndpoints)) {\n        if (!inject.overrideExisting && endpointName in context.endpointDefinitions) {\n          if (typeof process !== 'undefined' && process.env.NODE_ENV === 'development') {\n            console.error(`called \\`injectEndpoints\\` to override already-existing endpointName ${endpointName} without specifying \\`overrideExisting: true\\``);\n          }\n\n          continue;\n        }\n\n        context.endpointDefinitions[endpointName] = definition;\n\n        for (const m of initializedModules) {\n          m.injectEndpoint(endpointName, definition);\n        }\n      }\n\n      return (api as any);\n    }\n\n    return api.injectEndpoints({\n      endpoints: (options.endpoints as any)\n    });\n  };\n}", "import { formatProdErrorMessage as _formatProdErrorMessage } from \"@reduxjs/toolkit\";\nimport type { BaseQueryFn } from './baseQueryTypes';\n\nconst _NEVER = /* @__PURE__ */Symbol();\n\nexport type NEVER = typeof _NEVER;\n/**\r\n * Creates a \"fake\" baseQuery to be used if your api *only* uses the `queryFn` definition syntax.\r\n * This also allows you to specify a specific error type to be shared by all your `queryFn` definitions.\r\n */\n\nexport function fakeBaseQuery<ErrorType>(): BaseQueryFn<void, NEVER, ErrorType, {}> {\n  return function () {\n    throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(33) : 'When using `fakeBaseQuery`, all queries & mutations must use the `queryFn` definition syntax.');\n  };\n}", "import type { BaseQueryFn } from '../../baseQueryTypes';\nimport type { QueryDefinition } from '../../endpointDefinitions';\nimport type { ConfigState, QueryCacheKey } from '../apiState';\nimport type { QueryStateMeta, SubMiddlewareApi, TimeoutId, InternalHandlerBuilder, ApiMiddlewareInternalHandler, InternalMiddlewareState } from './types';\nexport type ReferenceCacheCollection = never;\n\nfunction isObjectEmpty(obj: Record<any, any>) {\n  // Apparently a for..in loop is faster than `Object.keys()` here:\n  // https://stackoverflow.com/a/59787784/62937\n  for (let k in obj) {\n    // If there is at least one key, it's not empty\n    return false;\n  }\n\n  return true;\n}\n\ndeclare module '../../endpointDefinitions' {\n  interface QueryExtraOptions<TagTypes extends string, ResultType, QueryArg, BaseQuery extends BaseQueryFn, ReducerPath extends string = string> {\n    /**\r\n     * Overrides the api-wide definition of `keepUnusedDataFor` for this endpoint only. _(This value is in seconds.)_\r\n     *\r\n     * This is how long RTK Query will keep your data cached for **after** the last component unsubscribes. For example, if you query an endpoint, then unmount the component, then mount another component that makes the same request within the given time frame, the most recent value will be served from the cache.\r\n     */\n    keepUnusedDataFor?: number;\n  }\n} // Per https://developer.mozilla.org/en-US/docs/Web/API/setTimeout#maximum_delay_value , browsers store\n// `setTimeout()` timer values in a 32-bit int. If we pass a value in that's larger than that,\n// it wraps and ends up executing immediately.\n// Our `keepUnusedDataFor` values are in seconds, so adjust the numbers here accordingly.\n\nexport const THIRTY_TWO_BIT_MAX_INT = 2_147_483_647;\nexport const THIRTY_TWO_BIT_MAX_TIMER_SECONDS = 2_147_483_647 / 1_000 - 1;\nexport const buildCacheCollectionHandler: InternalHandlerBuilder = ({\n  reducerPath,\n  api,\n  context,\n  internalState\n}) => {\n  const {\n    removeQueryResult,\n    unsubscribeQueryResult\n  } = api.internalActions;\n\n  function anySubscriptionsRemainingForKey(queryCacheKey: string) {\n    const subscriptions = internalState.currentSubscriptions[queryCacheKey];\n    return !!subscriptions && !isObjectEmpty(subscriptions);\n  }\n\n  const currentRemovalTimeouts: QueryStateMeta<TimeoutId> = {};\n\n  const handler: ApiMiddlewareInternalHandler = (action, mwApi, internalState) => {\n    if (unsubscribeQueryResult.match(action)) {\n      const state = mwApi.getState()[reducerPath];\n      const {\n        queryCacheKey\n      } = action.payload;\n      handleUnsubscribe(queryCacheKey, state.queries[queryCacheKey]?.endpointName, mwApi, state.config);\n    }\n\n    if (api.util.resetApiState.match(action)) {\n      for (const [key, timeout] of Object.entries(currentRemovalTimeouts)) {\n        if (timeout) clearTimeout(timeout);\n        delete currentRemovalTimeouts[key];\n      }\n    }\n\n    if (context.hasRehydrationInfo(action)) {\n      const state = mwApi.getState()[reducerPath];\n      const {\n        queries\n      } = context.extractRehydrationInfo(action)!;\n\n      for (const [queryCacheKey, queryState] of Object.entries(queries)) {\n        // Gotcha:\n        // If rehydrating before the endpoint has been injected,the global `keepUnusedDataFor`\n        // will be used instead of the endpoint-specific one.\n        handleUnsubscribe((queryCacheKey as QueryCacheKey), queryState?.endpointName, mwApi, state.config);\n      }\n    }\n  };\n\n  function handleUnsubscribe(queryCacheKey: QueryCacheKey, endpointName: string | undefined, api: SubMiddlewareApi, config: ConfigState<string>) {\n    const endpointDefinition = (context.endpointDefinitions[endpointName!] as QueryDefinition<any, any, any, any>);\n    const keepUnusedDataFor = endpointDefinition?.keepUnusedDataFor ?? config.keepUnusedDataFor;\n\n    if (keepUnusedDataFor === Infinity) {\n      // Hey, user said keep this forever!\n      return;\n    } // Prevent `setTimeout` timers from overflowing a 32-bit internal int, by\n    // clamping the max value to be at most 1000ms less than the 32-bit max.\n    // Look, a 24.8-day keepalive ought to be enough for anybody, right? :)\n    // Also avoid negative values too.\n\n\n    const finalKeepUnusedDataFor = Math.max(0, Math.min(keepUnusedDataFor, THIRTY_TWO_BIT_MAX_TIMER_SECONDS));\n\n    if (!anySubscriptionsRemainingForKey(queryCacheKey)) {\n      const currentTimeout = currentRemovalTimeouts[queryCacheKey];\n\n      if (currentTimeout) {\n        clearTimeout(currentTimeout);\n      }\n\n      currentRemovalTimeouts[queryCacheKey] = setTimeout(() => {\n        if (!anySubscriptionsRemainingForKey(queryCacheKey)) {\n          api.dispatch(removeQueryResult({\n            queryCacheKey\n          }));\n        }\n\n        delete currentRemovalTimeouts![queryCacheKey];\n      }, finalKeepUnusedDataFor * 1000);\n    }\n  }\n\n  return handler;\n};", "import { isAnyOf, isFulfilled, isRejected, isRejectedWithValue } from '../rtkImports';\nimport type { EndpointDefinitions, FullTagDescription } from '../../endpointDefinitions';\nimport { calculateProvidedBy } from '../../endpointDefinitions';\nimport type { CombinedState, QueryCacheKey } from '../apiState';\nimport { QueryStatus } from '../apiState';\nimport { calculateProvidedByThunk } from '../buildThunks';\nimport type { SubMiddlewareApi, InternalHandlerBuilder, ApiMiddlewareInternalHandler, InternalMiddlewareState } from './types';\nimport { countObjectKeys } from '../../utils/countObjectKeys';\nexport const buildInvalidationByTagsHandler: InternalHandlerBuilder = ({\n  reducerPath,\n  context,\n  context: {\n    endpointDefinitions\n  },\n  mutationThunk,\n  queryThunk,\n  api,\n  assertTagType,\n  refetchQuery,\n  internalState\n}) => {\n  const {\n    removeQueryResult\n  } = api.internalActions;\n  const isThunkActionWithTags = isAnyOf(isFulfilled(mutationThunk), isRejectedWithValue(mutationThunk));\n  const isQueryEnd = isAnyOf(isFulfilled(mutationThunk, queryThunk), isRejected(mutationThunk, queryThunk));\n  let pendingTagInvalidations: FullTagDescription<string>[] = [];\n\n  const handler: ApiMiddlewareInternalHandler = (action, mwApi) => {\n    if (isThunkActionWithTags(action)) {\n      invalidateTags(calculateProvidedByThunk(action, 'invalidatesTags', endpointDefinitions, assertTagType), mwApi);\n    } else if (isQueryEnd(action)) {\n      invalidateTags([], mwApi);\n    } else if (api.util.invalidateTags.match(action)) {\n      invalidateTags(calculateProvidedBy(action.payload, undefined, undefined, undefined, undefined, assertTagType), mwApi);\n    }\n  };\n\n  function hasPendingRequests(state: CombinedState<EndpointDefinitions, string, string>) {\n    for (const key in state.queries) {\n      if (state.queries[key]?.status === QueryStatus.pending) return true;\n    }\n\n    for (const key in state.mutations) {\n      if (state.mutations[key]?.status === QueryStatus.pending) return true;\n    }\n\n    return false;\n  }\n\n  function invalidateTags(newTags: readonly FullTagDescription<string>[], mwApi: SubMiddlewareApi) {\n    const rootState = mwApi.getState();\n    const state = rootState[reducerPath];\n    pendingTagInvalidations.push(...newTags);\n\n    if (state.config.invalidationBehavior === 'delayed' && hasPendingRequests(state)) {\n      return;\n    }\n\n    const tags = pendingTagInvalidations;\n    pendingTagInvalidations = [];\n    if (tags.length === 0) return;\n    const toInvalidate = api.util.selectInvalidatedBy(rootState, tags);\n    context.batch(() => {\n      const valuesArray = Array.from(toInvalidate.values());\n\n      for (const {\n        queryCacheKey\n      } of valuesArray) {\n        const querySubState = state.queries[queryCacheKey];\n        const subscriptionSubState = internalState.currentSubscriptions[queryCacheKey] ?? {};\n\n        if (querySubState) {\n          if (countObjectKeys(subscriptionSubState) === 0) {\n            mwApi.dispatch(removeQueryResult({\n              queryCacheKey: (queryCacheKey as QueryCacheKey)\n            }));\n          } else if (querySubState.status !== QueryStatus.uninitialized) {\n            mwApi.dispatch(refetchQuery(querySubState, queryCacheKey));\n          }\n        }\n      }\n    });\n  }\n\n  return handler;\n};", "import type { QuerySubstateIdentifier, Subscribers } from '../apiState';\nimport { QueryStatus } from '../apiState';\nimport type { QueryStateMeta, SubMiddlewareApi, TimeoutId, InternalHandlerBuilder, ApiMiddlewareInternalHandler, InternalMiddlewareState } from './types';\nexport const buildPollingHandler: InternalHandlerBuilder = ({\n  reducerPath,\n  queryThunk,\n  api,\n  refetchQuery,\n  internalState\n}) => {\n  const currentPolls: QueryStateMeta<{\n    nextPollTimestamp: number;\n    timeout?: TimeoutId;\n    pollingInterval: number;\n  }> = {};\n\n  const handler: ApiMiddlewareInternalHandler = (action, mwApi) => {\n    if (api.internalActions.updateSubscriptionOptions.match(action) || api.internalActions.unsubscribeQueryResult.match(action)) {\n      updatePollingInterval(action.payload, mwApi);\n    }\n\n    if (queryThunk.pending.match(action) || queryThunk.rejected.match(action) && action.meta.condition) {\n      updatePollingInterval(action.meta.arg, mwApi);\n    }\n\n    if (queryThunk.fulfilled.match(action) || queryThunk.rejected.match(action) && !action.meta.condition) {\n      startNextPoll(action.meta.arg, mwApi);\n    }\n\n    if (api.util.resetApiState.match(action)) {\n      clearPolls();\n    }\n  };\n\n  function startNextPoll({\n    queryCacheKey\n  }: QuerySubstateIdentifier, api: SubMiddlewareApi) {\n    const state = api.getState()[reducerPath];\n    const querySubState = state.queries[queryCacheKey];\n    const subscriptions = internalState.currentSubscriptions[queryCacheKey];\n    if (!querySubState || querySubState.status === QueryStatus.uninitialized) return;\n    const lowestPollingInterval = findLowestPollingInterval(subscriptions);\n    if (!Number.isFinite(lowestPollingInterval)) return;\n    const currentPoll = currentPolls[queryCacheKey];\n\n    if (currentPoll?.timeout) {\n      clearTimeout(currentPoll.timeout);\n      currentPoll.timeout = undefined;\n    }\n\n    const nextPollTimestamp = Date.now() + lowestPollingInterval;\n    const currentInterval: typeof currentPolls[number] = currentPolls[queryCacheKey] = {\n      nextPollTimestamp,\n      pollingInterval: lowestPollingInterval,\n      timeout: setTimeout(() => {\n        currentInterval!.timeout = undefined;\n        api.dispatch(refetchQuery(querySubState, queryCacheKey));\n      }, lowestPollingInterval)\n    };\n  }\n\n  function updatePollingInterval({\n    queryCacheKey\n  }: QuerySubstateIdentifier, api: SubMiddlewareApi) {\n    const state = api.getState()[reducerPath];\n    const querySubState = state.queries[queryCacheKey];\n    const subscriptions = internalState.currentSubscriptions[queryCacheKey];\n\n    if (!querySubState || querySubState.status === QueryStatus.uninitialized) {\n      return;\n    }\n\n    const lowestPollingInterval = findLowestPollingInterval(subscriptions);\n\n    if (!Number.isFinite(lowestPollingInterval)) {\n      cleanupPollForKey(queryCacheKey);\n      return;\n    }\n\n    const currentPoll = currentPolls[queryCacheKey];\n    const nextPollTimestamp = Date.now() + lowestPollingInterval;\n\n    if (!currentPoll || nextPollTimestamp < currentPoll.nextPollTimestamp) {\n      startNextPoll({\n        queryCacheKey\n      }, api);\n    }\n  }\n\n  function cleanupPollForKey(key: string) {\n    const existingPoll = currentPolls[key];\n\n    if (existingPoll?.timeout) {\n      clearTimeout(existingPoll.timeout);\n    }\n\n    delete currentPolls[key];\n  }\n\n  function clearPolls() {\n    for (const key of Object.keys(currentPolls)) {\n      cleanupPollForKey(key);\n    }\n  }\n\n  function findLowestPollingInterval(subscribers: Subscribers = {}) {\n    let lowestPollingInterval = Number.POSITIVE_INFINITY;\n\n    for (let key in subscribers) {\n      if (!!subscribers[key].pollingInterval) {\n        lowestPollingInterval = Math.min(subscribers[key].pollingInterval!, lowestPollingInterval);\n      }\n    }\n\n    return lowestPollingInterval;\n  }\n\n  return handler;\n};", "import { QueryStatus } from '../apiState';\nimport type { QueryCacheKey } from '../apiState';\nimport { onFocus, onOnline } from '../setupListeners';\nimport type { ApiMiddlewareInternalHandler, InternalHandlerBuilder, SubMiddlewareApi } from './types';\nimport { countObjectKeys } from '../../utils/countObjectKeys';\nexport const buildWindowEventHandler: InternalHandlerBuilder = ({\n  reducerPath,\n  context,\n  api,\n  refetchQuery,\n  internalState\n}) => {\n  const {\n    removeQueryResult\n  } = api.internalActions;\n\n  const handler: ApiMiddlewareInternalHandler = (action, mwApi) => {\n    if (onFocus.match(action)) {\n      refetchValidQueries(mwApi, 'refetchOnFocus');\n    }\n\n    if (onOnline.match(action)) {\n      refetchValidQueries(mwApi, 'refetchOnReconnect');\n    }\n  };\n\n  function refetchValidQueries(api: SubMiddlewareApi, type: 'refetchOnFocus' | 'refetchOnReconnect') {\n    const state = api.getState()[reducerPath];\n    const queries = state.queries;\n    const subscriptions = internalState.currentSubscriptions;\n    context.batch(() => {\n      for (const queryCacheKey of Object.keys(subscriptions)) {\n        const querySubState = queries[queryCacheKey];\n        const subscriptionSubState = subscriptions[queryCacheKey];\n        if (!subscriptionSubState || !querySubState) continue;\n        const shouldRefetch = Object.values(subscriptionSubState).some(sub => sub[type] === true) || Object.values(subscriptionSubState).every(sub => sub[type] === undefined) && state.config[type];\n\n        if (shouldRefetch) {\n          if (countObjectKeys(subscriptionSubState) === 0) {\n            api.dispatch(removeQueryResult({\n              queryCacheKey: (queryCacheKey as QueryCacheKey)\n            }));\n          } else if (querySubState.status !== QueryStatus.uninitialized) {\n            api.dispatch(refetchQuery(querySubState, queryCacheKey));\n          }\n        }\n      }\n    });\n  }\n\n  return handler;\n};", "import { isAsyncThunkAction, isFulfilled } from '../rtkImports';\nimport type { UnknownAction } from 'redux';\nimport type { ThunkDispatch } from 'redux-thunk';\nimport type { BaseQueryFn, BaseQueryMeta } from '../../baseQueryTypes';\nimport { DefinitionType } from '../../endpointDefinitions';\nimport type { RootState } from '../apiState';\nimport type { MutationResultSelectorResult, QueryResultSelectorResult } from '../buildSelectors';\nimport { getMutationCacheKey } from '../buildSlice';\nimport type { PatchCollection, Recipe } from '../buildThunks';\nimport type { <PERSON>piMiddlewareInternalHandler, InternalHandlerBuilder, PromiseWithKnownReason, SubMiddlewareApi } from './types';\nexport type ReferenceCacheLifecycle = never;\ndeclare module '../../endpointDefinitions' {\n  export interface QueryBaseLifecycleApi<QueryArg, BaseQuery extends BaseQueryFn, ResultType, ReducerPath extends string = string> extends LifecycleApi<ReducerPath> {\n    /**\r\n     * Gets the current value of this cache entry.\r\n     */\n    getCacheEntry(): QueryResultSelectorResult<{\n      type: DefinitionType.query;\n    } & BaseEndpointDefinition<QueryArg, BaseQuery, ResultType>>;\n    /**\r\n     * Updates the current cache entry value.\r\n     * For documentation see `api.util.updateQueryData`.\r\n     */\n\n    updateCachedData(updateRecipe: Recipe<ResultType>): PatchCollection;\n  }\n  export interface MutationBaseLifecycleApi<QueryArg, BaseQuery extends BaseQueryFn, ResultType, ReducerPath extends string = string> extends LifecycleApi<ReducerPath> {\n    /**\r\n     * Gets the current value of this cache entry.\r\n     */\n    getCacheEntry(): MutationResultSelectorResult<{\n      type: DefinitionType.mutation;\n    } & BaseEndpointDefinition<QueryArg, BaseQuery, ResultType>>;\n  }\n  export interface LifecycleApi<ReducerPath extends string = string> {\n    /**\r\n     * The dispatch method for the store\r\n     */\n    dispatch: ThunkDispatch<any, any, UnknownAction>;\n    /**\r\n     * A method to get the current state\r\n     */\n\n    getState(): RootState<any, any, ReducerPath>;\n    /**\r\n     * `extra` as provided as `thunk.extraArgument` to the `configureStore` `getDefaultMiddleware` option.\r\n     */\n\n    extra: unknown;\n    /**\r\n     * A unique ID generated for the mutation\r\n     */\n\n    requestId: string;\n  }\n  export interface CacheLifecyclePromises<ResultType = unknown, MetaType = unknown> {\n    /**\r\n     * Promise that will resolve with the first value for this cache key.\r\n     * This allows you to `await` until an actual value is in cache.\r\n     *\r\n     * If the cache entry is removed from the cache before any value has ever\r\n     * been resolved, this Promise will reject with\r\n     * `new Error('Promise never resolved before cacheEntryRemoved.')`\r\n     * to prevent memory leaks.\r\n     * You can just re-throw that error (or not handle it at all) -\r\n     * it will be caught outside of `cacheEntryAdded`.\r\n     *\r\n     * If you don't interact with this promise, it will not throw.\r\n     */\n    cacheDataLoaded: PromiseWithKnownReason<{\n      /**\r\n       * The (transformed) query result.\r\n       */\n      data: ResultType;\n      /**\r\n       * The `meta` returned by the `baseQuery`\r\n       */\n\n      meta: MetaType;\n    }, typeof neverResolvedError>;\n    /**\r\n     * Promise that allows you to wait for the point in time when the cache entry\r\n     * has been removed from the cache, by not being used/subscribed to any more\r\n     * in the application for too long or by dispatching `api.util.resetApiState`.\r\n     */\n\n    cacheEntryRemoved: Promise<void>;\n  }\n  export interface QueryCacheLifecycleApi<QueryArg, BaseQuery extends BaseQueryFn, ResultType, ReducerPath extends string = string> extends QueryBaseLifecycleApi<QueryArg, BaseQuery, ResultType, ReducerPath>, CacheLifecyclePromises<ResultType, BaseQueryMeta<BaseQuery>> {}\n  export interface MutationCacheLifecycleApi<QueryArg, BaseQuery extends BaseQueryFn, ResultType, ReducerPath extends string = string> extends MutationBaseLifecycleApi<QueryArg, BaseQuery, ResultType, ReducerPath>, CacheLifecyclePromises<ResultType, BaseQueryMeta<BaseQuery>> {}\n  interface QueryExtraOptions<TagTypes extends string, ResultType, QueryArg, BaseQuery extends BaseQueryFn, ReducerPath extends string = string> {\n    onCacheEntryAdded?(arg: QueryArg, api: QueryCacheLifecycleApi<QueryArg, BaseQuery, ResultType, ReducerPath>): Promise<void> | void;\n  }\n  interface MutationExtraOptions<TagTypes extends string, ResultType, QueryArg, BaseQuery extends BaseQueryFn, ReducerPath extends string = string> {\n    onCacheEntryAdded?(arg: QueryArg, api: MutationCacheLifecycleApi<QueryArg, BaseQuery, ResultType, ReducerPath>): Promise<void> | void;\n  }\n}\nconst neverResolvedError = (new Error('Promise never resolved before cacheEntryRemoved.') as Error & {\n  message: 'Promise never resolved before cacheEntryRemoved.';\n});\nexport const buildCacheLifecycleHandler: InternalHandlerBuilder = ({\n  api,\n  reducerPath,\n  context,\n  queryThunk,\n  mutationThunk,\n  internalState\n}) => {\n  const isQueryThunk = isAsyncThunkAction(queryThunk);\n  const isMutationThunk = isAsyncThunkAction(mutationThunk);\n  const isFulfilledThunk = isFulfilled(queryThunk, mutationThunk);\n  type CacheLifecycle = {\n    valueResolved?(value: {\n      data: unknown;\n      meta: unknown;\n    }): unknown;\n    cacheEntryRemoved(): void;\n  };\n  const lifecycleMap: Record<string, CacheLifecycle> = {};\n\n  const handler: ApiMiddlewareInternalHandler = (action, mwApi, stateBefore) => {\n    const cacheKey = getCacheKey(action);\n\n    if (queryThunk.pending.match(action)) {\n      const oldState = stateBefore[reducerPath].queries[cacheKey];\n      const state = mwApi.getState()[reducerPath].queries[cacheKey];\n\n      if (!oldState && state) {\n        handleNewKey(action.meta.arg.endpointName, action.meta.arg.originalArgs, cacheKey, mwApi, action.meta.requestId);\n      }\n    } else if (mutationThunk.pending.match(action)) {\n      const state = mwApi.getState()[reducerPath].mutations[cacheKey];\n\n      if (state) {\n        handleNewKey(action.meta.arg.endpointName, action.meta.arg.originalArgs, cacheKey, mwApi, action.meta.requestId);\n      }\n    } else if (isFulfilledThunk(action)) {\n      const lifecycle = lifecycleMap[cacheKey];\n\n      if (lifecycle?.valueResolved) {\n        lifecycle.valueResolved({\n          data: action.payload,\n          meta: action.meta.baseQueryMeta\n        });\n        delete lifecycle.valueResolved;\n      }\n    } else if (api.internalActions.removeQueryResult.match(action) || api.internalActions.removeMutationResult.match(action)) {\n      const lifecycle = lifecycleMap[cacheKey];\n\n      if (lifecycle) {\n        delete lifecycleMap[cacheKey];\n        lifecycle.cacheEntryRemoved();\n      }\n    } else if (api.util.resetApiState.match(action)) {\n      for (const [cacheKey, lifecycle] of Object.entries(lifecycleMap)) {\n        delete lifecycleMap[cacheKey];\n        lifecycle.cacheEntryRemoved();\n      }\n    }\n  };\n\n  function getCacheKey(action: any) {\n    if (isQueryThunk(action)) return action.meta.arg.queryCacheKey;\n    if (isMutationThunk(action)) return action.meta.requestId;\n    if (api.internalActions.removeQueryResult.match(action)) return action.payload.queryCacheKey;\n    if (api.internalActions.removeMutationResult.match(action)) return getMutationCacheKey(action.payload);\n    return '';\n  }\n\n  function handleNewKey(endpointName: string, originalArgs: any, queryCacheKey: string, mwApi: SubMiddlewareApi, requestId: string) {\n    const endpointDefinition = context.endpointDefinitions[endpointName];\n    const onCacheEntryAdded = endpointDefinition?.onCacheEntryAdded;\n    if (!onCacheEntryAdded) return;\n    let lifecycle = ({} as CacheLifecycle);\n    const cacheEntryRemoved = new Promise<void>(resolve => {\n      lifecycle.cacheEntryRemoved = resolve;\n    });\n    const cacheDataLoaded: PromiseWithKnownReason<{\n      data: unknown;\n      meta: unknown;\n    }, typeof neverResolvedError> = Promise.race([new Promise<{\n      data: unknown;\n      meta: unknown;\n    }>(resolve => {\n      lifecycle.valueResolved = resolve;\n    }), cacheEntryRemoved.then(() => {\n      throw neverResolvedError;\n    })]); // prevent uncaught promise rejections from happening.\n    // if the original promise is used in any way, that will create a new promise that will throw again\n\n    cacheDataLoaded.catch(() => {});\n    lifecycleMap[queryCacheKey] = lifecycle;\n    const selector = (api.endpoints[endpointName] as any).select(endpointDefinition.type === DefinitionType.query ? originalArgs : queryCacheKey);\n    const extra = mwApi.dispatch((_, __, extra) => extra);\n    const lifecycleApi = { ...mwApi,\n      getCacheEntry: () => selector(mwApi.getState()),\n      requestId,\n      extra,\n      updateCachedData: ((endpointDefinition.type === DefinitionType.query ? (updateRecipe: Recipe<any>) => mwApi.dispatch(api.util.updateQueryData((endpointName as never), originalArgs, updateRecipe)) : undefined) as any),\n      cacheDataLoaded,\n      cacheEntryRemoved\n    };\n    const runningHandler = onCacheEntryAdded(originalArgs, lifecycleApi); // if a `neverResolvedError` was thrown, but not handled in the running handler, do not let it leak out further\n\n    Promise.resolve(runningHandler).catch(e => {\n      if (e === neverResolvedError) return;\n      throw e;\n    });\n  }\n\n  return handler;\n};", "import { isPending, isRejected, isFulfilled } from '../rtkImports';\nimport type { BaseQueryError, BaseQueryFn, BaseQueryMeta } from '../../baseQueryTypes';\nimport { DefinitionType } from '../../endpointDefinitions';\nimport type { QueryFulfilledRejectionReason } from '../../endpointDefinitions';\nimport type { Recipe } from '../buildThunks';\nimport type { PromiseWithKnownReason, PromiseConstructorWithKnownReason, InternalHandlerBuilder, ApiMiddlewareInternalHandler } from './types';\nexport type ReferenceQueryLifecycle = never;\ndeclare module '../../endpointDefinitions' {\n  export interface QueryLifecyclePromises<ResultType, BaseQuery extends BaseQueryFn> {\n    /**\r\n     * Promise that will resolve with the (transformed) query result.\r\n     *\r\n     * If the query fails, this promise will reject with the error.\r\n     *\r\n     * This allows you to `await` for the query to finish.\r\n     *\r\n     * If you don't interact with this promise, it will not throw.\r\n     */\n    queryFulfilled: PromiseWithKnownReason<{\n      /**\r\n       * The (transformed) query result.\r\n       */\n      data: ResultType;\n      /**\r\n       * The `meta` returned by the `baseQuery`\r\n       */\n\n      meta: BaseQueryMeta<BaseQuery>;\n    }, QueryFulfilledRejectionReason<BaseQuery>>;\n  }\n  type QueryFulfilledRejectionReason<BaseQuery extends BaseQueryFn> = {\n    error: BaseQueryError<BaseQuery>;\n    /**\r\n     * If this is `false`, that means this error was returned from the `baseQuery` or `queryFn` in a controlled manner.\r\n     */\n\n    isUnhandledError: false;\n    /**\r\n     * The `meta` returned by the `baseQuery`\r\n     */\n\n    meta: BaseQueryMeta<BaseQuery>;\n  } | {\n    error: unknown;\n    meta?: undefined;\n    /**\r\n     * If this is `true`, that means that this error is the result of `baseQueryFn`, `queryFn`, `transformResponse` or `transformErrorResponse` throwing an error instead of handling it properly.\r\n     * There can not be made any assumption about the shape of `error`.\r\n     */\n\n    isUnhandledError: true;\n  };\n  interface QueryExtraOptions<TagTypes extends string, ResultType, QueryArg, BaseQuery extends BaseQueryFn, ReducerPath extends string = string> {\n    /**\r\n     * A function that is called when the individual query is started. The function is called with a lifecycle api object containing properties such as `queryFulfilled`, allowing code to be run when a query is started, when it succeeds, and when it fails (i.e. throughout the lifecycle of an individual query/mutation call).\r\n     *\r\n     * Can be used to perform side-effects throughout the lifecycle of the query.\r\n     *\r\n     * @example\r\n     * ```ts\r\n     * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query'\r\n     * import { messageCreated } from './notificationsSlice\r\n     * export interface Post {\r\n     *   id: number\r\n     *   name: string\r\n     * }\r\n     *\r\n     * const api = createApi({\r\n     *   baseQuery: fetchBaseQuery({\r\n     *     baseUrl: '/',\r\n     *   }),\r\n     *   endpoints: (build) => ({\r\n     *     getPost: build.query<Post, number>({\r\n     *       query: (id) => `post/${id}`,\r\n     *       async onQueryStarted(id, { dispatch, queryFulfilled }) {\r\n     *         // `onStart` side-effect\r\n     *         dispatch(messageCreated('Fetching posts...'))\r\n     *         try {\r\n     *           const { data } = await queryFulfilled\r\n     *           // `onSuccess` side-effect\r\n     *           dispatch(messageCreated('Posts received!'))\r\n     *         } catch (err) {\r\n     *           // `onError` side-effect\r\n     *           dispatch(messageCreated('Error fetching posts!'))\r\n     *         }\r\n     *       }\r\n     *     }),\r\n     *   }),\r\n     * })\r\n     * ```\r\n     */\n    onQueryStarted?(arg: QueryArg, api: QueryLifecycleApi<QueryArg, BaseQuery, ResultType, ReducerPath>): Promise<void> | void;\n  }\n  interface MutationExtraOptions<TagTypes extends string, ResultType, QueryArg, BaseQuery extends BaseQueryFn, ReducerPath extends string = string> {\n    /**\r\n     * A function that is called when the individual mutation is started. The function is called with a lifecycle api object containing properties such as `queryFulfilled`, allowing code to be run when a query is started, when it succeeds, and when it fails (i.e. throughout the lifecycle of an individual query/mutation call).\r\n     *\r\n     * Can be used for `optimistic updates`.\r\n     *\r\n     * @example\r\n     *\r\n     * ```ts\r\n     * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query'\r\n     * export interface Post {\r\n     *   id: number\r\n     *   name: string\r\n     * }\r\n     *\r\n     * const api = createApi({\r\n     *   baseQuery: fetchBaseQuery({\r\n     *     baseUrl: '/',\r\n     *   }),\r\n     *   tagTypes: ['Post'],\r\n     *   endpoints: (build) => ({\r\n     *     getPost: build.query<Post, number>({\r\n     *       query: (id) => `post/${id}`,\r\n     *       providesTags: ['Post'],\r\n     *     }),\r\n     *     updatePost: build.mutation<void, Pick<Post, 'id'> & Partial<Post>>({\r\n     *       query: ({ id, ...patch }) => ({\r\n     *         url: `post/${id}`,\r\n     *         method: 'PATCH',\r\n     *         body: patch,\r\n     *       }),\r\n     *       invalidatesTags: ['Post'],\r\n     *       async onQueryStarted({ id, ...patch }, { dispatch, queryFulfilled }) {\r\n     *         const patchResult = dispatch(\r\n     *           api.util.updateQueryData('getPost', id, (draft) => {\r\n     *             Object.assign(draft, patch)\r\n     *           })\r\n     *         )\r\n     *         try {\r\n     *           await queryFulfilled\r\n     *         } catch {\r\n     *           patchResult.undo()\r\n     *         }\r\n     *       },\r\n     *     }),\r\n     *   }),\r\n     * })\r\n     * ```\r\n     */\n    onQueryStarted?(arg: QueryArg, api: MutationLifecycleApi<QueryArg, BaseQuery, ResultType, ReducerPath>): Promise<void> | void;\n  }\n  export interface QueryLifecycleApi<QueryArg, BaseQuery extends BaseQueryFn, ResultType, ReducerPath extends string = string> extends QueryBaseLifecycleApi<QueryArg, BaseQuery, ResultType, ReducerPath>, QueryLifecyclePromises<ResultType, BaseQuery> {}\n  export interface MutationLifecycleApi<QueryArg, BaseQuery extends BaseQueryFn, ResultType, ReducerPath extends string = string> extends MutationBaseLifecycleApi<QueryArg, BaseQuery, ResultType, ReducerPath>, QueryLifecyclePromises<ResultType, BaseQuery> {}\n}\nexport const buildQueryLifecycleHandler: InternalHandlerBuilder = ({\n  api,\n  context,\n  queryThunk,\n  mutationThunk\n}) => {\n  const isPendingThunk = isPending(queryThunk, mutationThunk);\n  const isRejectedThunk = isRejected(queryThunk, mutationThunk);\n  const isFullfilledThunk = isFulfilled(queryThunk, mutationThunk);\n  type CacheLifecycle = {\n    resolve(value: {\n      data: unknown;\n      meta: unknown;\n    }): unknown;\n    reject(value: QueryFulfilledRejectionReason<any>): unknown;\n  };\n  const lifecycleMap: Record<string, CacheLifecycle> = {};\n\n  const handler: ApiMiddlewareInternalHandler = (action, mwApi) => {\n    if (isPendingThunk(action)) {\n      const {\n        requestId,\n        arg: {\n          endpointName,\n          originalArgs\n        }\n      } = action.meta;\n      const endpointDefinition = context.endpointDefinitions[endpointName];\n      const onQueryStarted = endpointDefinition?.onQueryStarted;\n\n      if (onQueryStarted) {\n        const lifecycle = ({} as CacheLifecycle);\n        const queryFulfilled = new (Promise as PromiseConstructorWithKnownReason)<{\n          data: unknown;\n          meta: unknown;\n        }, QueryFulfilledRejectionReason<any>>((resolve, reject) => {\n          lifecycle.resolve = resolve;\n          lifecycle.reject = reject;\n        }); // prevent uncaught promise rejections from happening.\n        // if the original promise is used in any way, that will create a new promise that will throw again\n\n        queryFulfilled.catch(() => {});\n        lifecycleMap[requestId] = lifecycle;\n        const selector = (api.endpoints[endpointName] as any).select(endpointDefinition.type === DefinitionType.query ? originalArgs : requestId);\n        const extra = mwApi.dispatch((_, __, extra) => extra);\n        const lifecycleApi = { ...mwApi,\n          getCacheEntry: () => selector(mwApi.getState()),\n          requestId,\n          extra,\n          updateCachedData: ((endpointDefinition.type === DefinitionType.query ? (updateRecipe: Recipe<any>) => mwApi.dispatch(api.util.updateQueryData((endpointName as never), originalArgs, updateRecipe)) : undefined) as any),\n          queryFulfilled\n        };\n        onQueryStarted(originalArgs, lifecycleApi);\n      }\n    } else if (isFullfilledThunk(action)) {\n      const {\n        requestId,\n        baseQueryMeta\n      } = action.meta;\n      lifecycleMap[requestId]?.resolve({\n        data: action.payload,\n        meta: baseQueryMeta\n      });\n      delete lifecycleMap[requestId];\n    } else if (isRejectedThunk(action)) {\n      const {\n        requestId,\n        rejectedWithValue,\n        baseQueryMeta\n      } = action.meta;\n      lifecycleMap[requestId]?.reject({\n        error: action.payload ?? action.error,\n        isUnhandledError: !rejectedWithValue,\n        meta: (baseQueryMeta as any)\n      });\n      delete lifecycleMap[requestId];\n    }\n  };\n\n  return handler;\n};", "import type { InternalHandlerBuilder } from './types';\nexport const buildDevCheckHandler: InternalHandlerBuilder = ({\n  api,\n  context: {\n    apiUid\n  },\n  reducerPath\n}) => {\n  return (action, mwApi) => {\n    if (api.util.resetApiState.match(action)) {\n      // dispatch after api reset\n      mwApi.dispatch(api.internalActions.middlewareRegistered(apiUid));\n    }\n\n    if (typeof process !== 'undefined' && process.env.NODE_ENV === 'development') {\n      if (api.internalActions.middlewareRegistered.match(action) && action.payload === apiUid && mwApi.getState()[reducerPath]?.config?.middlewareRegistered === 'conflict') {\n        console.warn(`There is a mismatch between slice and middleware for the reducerPath \"${reducerPath}\".\nYou can only have one api per reducer path, this will lead to crashes in various situations!${reducerPath === 'api' ? `\nIf you have multiple apis, you *have* to specify the reducerPath option when using createApi!` : ''}`);\n      }\n    }\n  };\n};", "import type { InternalHandlerBuilder, SubscriptionSelectors } from './types';\nimport type { SubscriptionState } from '../apiState';\nimport { produceWithPatches } from 'immer';\nimport type { Action } from '@reduxjs/toolkit';\nimport { countObjectKeys } from '../../utils/countObjectKeys';\nexport const buildBatchedActionsHandler: InternalHandlerBuilder<[actionShouldContinue: boolean, returnValue: SubscriptionSelectors | boolean]> = ({\n  api,\n  queryThunk,\n  internalState\n}) => {\n  const subscriptionsPrefix = `${api.reducerPath}/subscriptions`;\n  let previousSubscriptions: SubscriptionState = ((null as unknown) as SubscriptionState);\n  let updateSyncTimer: ReturnType<typeof window.setTimeout> | null = null;\n  const {\n    updateSubscriptionOptions,\n    unsubscribeQueryResult\n  } = api.internalActions; // Actually intentionally mutate the subscriptions state used in the middleware\n  // This is done to speed up perf when loading many components\n\n  const actuallyMutateSubscriptions = (mutableState: SubscriptionState, action: Action) => {\n    if (updateSubscriptionOptions.match(action)) {\n      const {\n        queryCacheKey,\n        requestId,\n        options\n      } = action.payload;\n\n      if (mutableState?.[queryCacheKey]?.[requestId]) {\n        mutableState[queryCacheKey]![requestId] = options;\n      }\n\n      return true;\n    }\n\n    if (unsubscribeQueryResult.match(action)) {\n      const {\n        queryCacheKey,\n        requestId\n      } = action.payload;\n\n      if (mutableState[queryCacheKey]) {\n        delete mutableState[queryCacheKey]![requestId];\n      }\n\n      return true;\n    }\n\n    if (api.internalActions.removeQueryResult.match(action)) {\n      delete mutableState[action.payload.queryCacheKey];\n      return true;\n    }\n\n    if (queryThunk.pending.match(action)) {\n      const {\n        meta: {\n          arg,\n          requestId\n        }\n      } = action;\n      const substate = mutableState[arg.queryCacheKey] ??= {};\n      substate[`${requestId}_running`] = {};\n\n      if (arg.subscribe) {\n        substate[requestId] = arg.subscriptionOptions ?? substate[requestId] ?? {};\n      }\n\n      return true;\n    }\n\n    let mutated = false;\n\n    if (queryThunk.fulfilled.match(action) || queryThunk.rejected.match(action)) {\n      const state = mutableState[action.meta.arg.queryCacheKey] || {};\n      const key = `${action.meta.requestId}_running`;\n      mutated ||= !!state[key];\n      delete state[key];\n    }\n\n    if (queryThunk.rejected.match(action)) {\n      const {\n        meta: {\n          condition,\n          arg,\n          requestId\n        }\n      } = action;\n\n      if (condition && arg.subscribe) {\n        const substate = mutableState[arg.queryCacheKey] ??= {};\n        substate[requestId] = arg.subscriptionOptions ?? substate[requestId] ?? {};\n        mutated = true;\n      }\n    }\n\n    return mutated;\n  };\n\n  const getSubscriptions = () => internalState.currentSubscriptions;\n\n  const getSubscriptionCount = (queryCacheKey: string) => {\n    const subscriptions = getSubscriptions();\n    const subscriptionsForQueryArg = subscriptions[queryCacheKey] ?? {};\n    return countObjectKeys(subscriptionsForQueryArg);\n  };\n\n  const isRequestSubscribed = (queryCacheKey: string, requestId: string) => {\n    const subscriptions = getSubscriptions();\n    return !!subscriptions?.[queryCacheKey]?.[requestId];\n  };\n\n  const subscriptionSelectors: SubscriptionSelectors = {\n    getSubscriptions,\n    getSubscriptionCount,\n    isRequestSubscribed\n  };\n  return (action, mwApi): [actionShouldContinue: boolean, result: SubscriptionSelectors | boolean] => {\n    if (!previousSubscriptions) {\n      // Initialize it the first time this handler runs\n      previousSubscriptions = JSON.parse(JSON.stringify(internalState.currentSubscriptions));\n    }\n\n    if (api.util.resetApiState.match(action)) {\n      previousSubscriptions = internalState.currentSubscriptions = {};\n      updateSyncTimer = null;\n      return [true, false];\n    } // Intercept requests by hooks to see if they're subscribed\n    // We return the internal state reference so that hooks\n    // can do their own checks to see if they're still active.\n    // It's stupid and hacky, but it does cut down on some dispatch calls.\n\n\n    if (api.internalActions.internal_getRTKQSubscriptions.match(action)) {\n      return [false, subscriptionSelectors];\n    } // Update subscription data based on this action\n\n\n    const didMutate = actuallyMutateSubscriptions(internalState.currentSubscriptions, action);\n    let actionShouldContinue = true;\n\n    if (didMutate) {\n      if (!updateSyncTimer) {\n        // We only use the subscription state for the Redux DevTools at this point,\n        // as the real data is kept here in the middleware.\n        // Given that, we can throttle synchronizing this state significantly to\n        // save on overall perf.\n        // In 1.9, it was updated in a microtask, but now we do it at most every 500ms.\n        updateSyncTimer = setTimeout(() => {\n          // Deep clone the current subscription data\n          const newSubscriptions: SubscriptionState = JSON.parse(JSON.stringify(internalState.currentSubscriptions)); // Figure out a smaller diff between original and current\n\n          const [, patches] = produceWithPatches(previousSubscriptions, () => newSubscriptions); // Sync the store state for visibility\n\n          mwApi.next(api.internalActions.subscriptionsUpdated(patches)); // Save the cloned state for later reference\n\n          previousSubscriptions = newSubscriptions;\n          updateSyncTimer = null;\n        }, 500);\n      }\n\n      const isSubscriptionSliceAction = typeof action.type == 'string' && !!action.type.startsWith(subscriptionsPrefix);\n      const isAdditionalSubscriptionAction = queryThunk.rejected.match(action) && action.meta.condition && !!action.meta.arg.subscribe;\n      actionShouldContinue = !isSubscriptionSliceAction && !isAdditionalSubscriptionAction;\n    }\n\n    return [actionShouldContinue, false];\n  };\n};", "import type { Action, Middleware, ThunkDispatch, UnknownAction } from '@reduxjs/toolkit';\nimport { isAction, createAction } from '../rtkImports';\nimport type { EndpointDefinitions, FullTagDescription } from '../../endpointDefinitions';\nimport type { QueryStatus, QuerySubState, RootState } from '../apiState';\nimport type { QueryThunkArg } from '../buildThunks';\nimport { buildCacheCollectionHandler } from './cacheCollection';\nimport { buildInvalidationByTagsHandler } from './invalidationByTags';\nimport { buildPollingHandler } from './polling';\nimport type { BuildMiddlewareInput, InternalHandlerBuilder, InternalMiddlewareState } from './types';\nimport { buildWindowEventHandler } from './windowEventHandling';\nimport { buildCacheLifecycleHandler } from './cacheLifecycle';\nimport { buildQueryLifecycleHandler } from './queryLifecycle';\nimport { buildDevCheckHandler } from './devMiddleware';\nimport { buildBatchedActionsHandler } from './batchActions';\nexport function buildMiddleware<Definitions extends EndpointDefinitions, ReducerPath extends string, TagTypes extends string>(input: BuildMiddlewareInput<Definitions, ReducerPath, TagTypes>) {\n  const {\n    reducerPath,\n    queryThunk,\n    api,\n    context\n  } = input;\n  const {\n    apiUid\n  } = context;\n  const actions = {\n    invalidateTags: createAction<Array<TagTypes | FullTagDescription<TagTypes>>>(`${reducerPath}/invalidateTags`)\n  };\n\n  const isThisApiSliceAction = (action: Action) => action.type.startsWith(`${reducerPath}/`);\n\n  const handlerBuilders: InternalHandlerBuilder[] = [buildDevCheckHandler, buildCacheCollectionHandler, buildInvalidationByTagsHandler, buildPollingHandler, buildCacheLifecycleHandler, buildQueryLifecycleHandler];\n\n  const middleware: Middleware<{}, RootState<Definitions, string, ReducerPath>, ThunkDispatch<any, any, UnknownAction>> = mwApi => {\n    let initialized = false;\n    let internalState: InternalMiddlewareState = {\n      currentSubscriptions: {}\n    };\n    const builderArgs = { ...((input as any) as BuildMiddlewareInput<EndpointDefinitions, string, string>),\n      internalState,\n      refetchQuery,\n      isThisApiSliceAction\n    };\n    const handlers = handlerBuilders.map(build => build(builderArgs));\n    const batchedActionsHandler = buildBatchedActionsHandler(builderArgs);\n    const windowEventsHandler = buildWindowEventHandler(builderArgs);\n    return next => {\n      return action => {\n        if (!isAction(action)) {\n          return next(action);\n        }\n\n        if (!initialized) {\n          initialized = true; // dispatch before any other action\n\n          mwApi.dispatch(api.internalActions.middlewareRegistered(apiUid));\n        }\n\n        const mwApiWithNext = { ...mwApi,\n          next\n        };\n        const stateBefore = mwApi.getState();\n        const [actionShouldContinue, internalProbeResult] = batchedActionsHandler(action, mwApiWithNext, stateBefore);\n        let res: any;\n\n        if (actionShouldContinue) {\n          res = next(action);\n        } else {\n          res = internalProbeResult;\n        }\n\n        if (!!mwApi.getState()[reducerPath]) {\n          // Only run these checks if the middleware is registered okay\n          // This looks for actions that aren't specific to the API slice\n          windowEventsHandler(action, mwApiWithNext, stateBefore);\n\n          if (isThisApiSliceAction(action) || context.hasRehydrationInfo(action)) {\n            // Only run these additional checks if the actions are part of the API slice,\n            // or the action has hydration-related data\n            for (let handler of handlers) {\n              handler(action, mwApiWithNext, stateBefore);\n            }\n          }\n        }\n\n        return res;\n      };\n    };\n  };\n\n  return {\n    middleware,\n    actions\n  };\n\n  function refetchQuery(querySubState: Exclude<QuerySubState<any>, {\n    status: QueryStatus.uninitialized;\n  }>, queryCacheKey: string, override: Partial<QueryThunkArg> = {}) {\n    return queryThunk({\n      type: 'query',\n      endpointName: querySubState.endpointName,\n      originalArgs: querySubState.originalArgs,\n      subscribe: false,\n      forceRefetch: true,\n      queryCacheKey: (queryCacheKey as any),\n      ...override\n    });\n  }\n}", "export type Id<T> = { [K in keyof T]: T[K] } & {};\nexport type WithRequiredProp<T, K extends keyof T> = Omit<T, K> & Required<Pick<T, K>>;\nexport type Override<T1, T2> = T2 extends any ? Omit<T1, keyof T2> & T2 : never;\nexport function assertCast<T>(v: any): asserts v is T {}\nexport function safeAssign<T extends object>(target: T, ...args: Array<Partial<NoInfer<T>>>): T {\n  return Object.assign(target, ...args);\n}\n/**\r\n * Convert a Union type `(A|B)` to an intersection type `(A&B)`\r\n */\n\nexport type UnionToIntersection<U> = (U extends any ? (k: U) => void : never) extends (k: infer I) => void ? I : never;\nexport type NonOptionalKeys<T> = { [K in keyof T]-?: undefined extends T[K] ? never : K }[keyof T];\nexport type HasRequiredProps<T, True, False> = NonOptionalKeys<T> extends never ? False : True;\nexport type OptionalIfAllPropsOptional<T> = HasRequiredProps<T, T, T | never>;\nexport type NoInfer<T> = [T][T extends any ? 0 : never];\nexport type NonUndefined<T> = T extends undefined ? never : T;\nexport type UnwrapPromise<T> = T extends PromiseLike<infer V> ? V : T;\nexport type MaybePromise<T> = T | PromiseLike<T>;\nexport type OmitFromUnion<T, K extends keyof T> = T extends any ? Omit<T, K> : never;\nexport type IsAny<T, True, False = never> = true | false extends (T extends never ? true : false) ? True : False;\nexport type CastAny<T, CastTo> = IsAny<T, CastTo, T>;", "/**\r\n * Note: this file should import all other files for type discovery and declaration merging\r\n */\nimport type { PatchQueryDataThunk, UpdateQueryDataThunk, UpsertQueryDataThunk } from './buildThunks';\nimport { buildThunks } from './buildThunks';\nimport type { ActionCreatorWithPayload, Middleware, Reducer, ThunkAction, ThunkDispatch, UnknownAction } from '@reduxjs/toolkit';\nimport type { EndpointDefinitions, QueryArgFrom, QueryDefinition, MutationDefinition, AssertTagTypes, TagDescription } from '../endpointDefinitions';\nimport { isQueryDefinition, isMutationDefinition } from '../endpointDefinitions';\nimport type { CombinedState, QueryKeys, MutationKeys, RootState } from './apiState';\nimport type { Api, Module } from '../apiTypes';\nimport { onFocus, onFocusLost, onOnline, onOffline } from './setupListeners';\nimport { buildSlice } from './buildSlice';\nimport { buildMiddleware } from './buildMiddleware';\nimport { buildSelectors } from './buildSelectors';\nimport type { MutationActionCreatorResult, QueryActionCreatorResult } from './buildInitiate';\nimport { buildInitiate } from './buildInitiate';\nimport { assertCast, safeAssign } from '../tsHelpers';\nimport type { InternalSerializeQueryArgs } from '../defaultSerializeQueryArgs';\nimport type { SliceActions } from './buildSlice';\nimport type { BaseQueryFn } from '../baseQueryTypes';\nimport type { ReferenceCacheLifecycle } from './buildMiddleware/cacheLifecycle';\nimport type { ReferenceQueryLifecycle } from './buildMiddleware/queryLifecycle';\nimport type { ReferenceCacheCollection } from './buildMiddleware/cacheCollection';\nimport { enablePatches } from 'immer';\n/**\r\n * `ifOlderThan` - (default: `false` | `number`) - _number is value in seconds_\r\n * - If specified, it will only run the query if the difference between `new Date()` and the last `fulfilledTimeStamp` is greater than the given value\r\n *\r\n * @overloadSummary\r\n * `force`\r\n * - If `force: true`, it will ignore the `ifOlderThan` value if it is set and the query will be run even if it exists in the cache.\r\n */\n\nexport type PrefetchOptions = {\n  ifOlderThan?: false | number;\n} | {\n  force?: boolean;\n};\nexport const coreModuleName = /* @__PURE__ */Symbol();\nexport type CoreModule = typeof coreModuleName | ReferenceCacheLifecycle | ReferenceQueryLifecycle | ReferenceCacheCollection;\nexport interface ThunkWithReturnValue<T> extends ThunkAction<T, any, any, UnknownAction> {}\ndeclare module '../apiTypes' {\n  export interface ApiModules< // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  BaseQuery extends BaseQueryFn, Definitions extends EndpointDefinitions, ReducerPath extends string, TagTypes extends string> {\n    [coreModuleName]: {\n      /**\r\n       * This api's reducer should be mounted at `store[api.reducerPath]`.\r\n       *\r\n       * @example\r\n       * ```ts\r\n       * configureStore({\r\n       *   reducer: {\r\n       *     [api.reducerPath]: api.reducer,\r\n       *   },\r\n       *   middleware: (getDefaultMiddleware) => getDefaultMiddleware().concat(api.middleware),\r\n       * })\r\n       * ```\r\n       */\n      reducerPath: ReducerPath;\n      /**\r\n       * Internal actions not part of the public API. Note: These are subject to change at any given time.\r\n       */\n\n      internalActions: InternalActions;\n      /**\r\n       *  A standard redux reducer that enables core functionality. Make sure it's included in your store.\r\n       *\r\n       * @example\r\n       * ```ts\r\n       * configureStore({\r\n       *   reducer: {\r\n       *     [api.reducerPath]: api.reducer,\r\n       *   },\r\n       *   middleware: (getDefaultMiddleware) => getDefaultMiddleware().concat(api.middleware),\r\n       * })\r\n       * ```\r\n       */\n\n      reducer: Reducer<CombinedState<Definitions, TagTypes, ReducerPath>, UnknownAction>;\n      /**\r\n       * This is a standard redux middleware and is responsible for things like polling, garbage collection and a handful of other things. Make sure it's included in your store.\r\n       *\r\n       * @example\r\n       * ```ts\r\n       * configureStore({\r\n       *   reducer: {\r\n       *     [api.reducerPath]: api.reducer,\r\n       *   },\r\n       *   middleware: (getDefaultMiddleware) => getDefaultMiddleware().concat(api.middleware),\r\n       * })\r\n       * ```\r\n       */\n\n      middleware: Middleware<{}, RootState<Definitions, string, ReducerPath>, ThunkDispatch<any, any, UnknownAction>>;\n      /**\r\n       * A collection of utility thunks for various situations.\r\n       */\n\n      util: {\n        /**\r\n         * A thunk that (if dispatched) will return a specific running query, identified\r\n         * by `endpointName` and `args`.\r\n         * If that query is not running, dispatching the thunk will result in `undefined`.\r\n         *\r\n         * Can be used to await a specific query triggered in any way,\r\n         * including via hook calls or manually dispatching `initiate` actions.\r\n         *\r\n         * See https://redux-toolkit.js.org/rtk-query/usage/server-side-rendering for details.\r\n         */\n        getRunningQueryThunk<EndpointName extends QueryKeys<Definitions>>(endpointName: EndpointName, args: QueryArgFrom<Definitions[EndpointName]>): ThunkWithReturnValue<QueryActionCreatorResult<Definitions[EndpointName] & {\n          type: 'query';\n        }> | undefined>;\n        /**\r\n         * A thunk that (if dispatched) will return a specific running mutation, identified\r\n         * by `endpointName` and `fixedCacheKey` or `requestId`.\r\n         * If that mutation is not running, dispatching the thunk will result in `undefined`.\r\n         *\r\n         * Can be used to await a specific mutation triggered in any way,\r\n         * including via hook trigger functions or manually dispatching `initiate` actions.\r\n         *\r\n         * See https://redux-toolkit.js.org/rtk-query/usage/server-side-rendering for details.\r\n         */\n\n        getRunningMutationThunk<EndpointName extends MutationKeys<Definitions>>(endpointName: EndpointName, fixedCacheKeyOrRequestId: string): ThunkWithReturnValue<MutationActionCreatorResult<Definitions[EndpointName] & {\n          type: 'mutation';\n        }> | undefined>;\n        /**\r\n         * A thunk that (if dispatched) will return all running queries.\r\n         *\r\n         * Useful for SSR scenarios to await all running queries triggered in any way,\r\n         * including via hook calls or manually dispatching `initiate` actions.\r\n         *\r\n         * See https://redux-toolkit.js.org/rtk-query/usage/server-side-rendering for details.\r\n         */\n\n        getRunningQueriesThunk(): ThunkWithReturnValue<Array<QueryActionCreatorResult<any>>>;\n        /**\r\n         * A thunk that (if dispatched) will return all running mutations.\r\n         *\r\n         * Useful for SSR scenarios to await all running mutations triggered in any way,\r\n         * including via hook calls or manually dispatching `initiate` actions.\r\n         *\r\n         * See https://redux-toolkit.js.org/rtk-query/usage/server-side-rendering for details.\r\n         */\n\n        getRunningMutationsThunk(): ThunkWithReturnValue<Array<MutationActionCreatorResult<any>>>;\n        /**\r\n         * A Redux thunk that can be used to manually trigger pre-fetching of data.\r\n         *\r\n         * The thunk accepts three arguments: the name of the endpoint we are updating (such as `'getPost'`), the appropriate query arg values to construct the desired cache key, and a set of options used to determine if the data actually should be re-fetched based on cache staleness.\r\n         *\r\n         * React Hooks users will most likely never need to use this directly, as the `usePrefetch` hook will dispatch this thunk internally as needed when you call the prefetching function supplied by the hook.\r\n         *\r\n         * @example\r\n         *\r\n         * ```ts no-transpile\r\n         * dispatch(api.util.prefetch('getPosts', undefined, { force: true }))\r\n         * ```\r\n         */\n\n        prefetch<EndpointName extends QueryKeys<Definitions>>(endpointName: EndpointName, arg: QueryArgFrom<Definitions[EndpointName]>, options: PrefetchOptions): ThunkAction<void, any, any, UnknownAction>;\n        /**\r\n         * A Redux thunk action creator that, when dispatched, creates and applies a set of JSON diff/patch objects to the current state. This immediately updates the Redux state with those changes.\r\n         *\r\n         * The thunk action creator accepts three arguments: the name of the endpoint we are updating (such as `'getPost'`), the appropriate query arg values to construct the desired cache key, and an `updateRecipe` callback function. The callback receives an Immer-wrapped `draft` of the current state, and may modify the draft to match the expected results after the mutation completes successfully.\r\n         *\r\n         * The thunk executes _synchronously_, and returns an object containing `{patches: Patch[], inversePatches: Patch[], undo: () => void}`. The `patches` and `inversePatches` are generated using Immer's [`produceWithPatches` method](https://immerjs.github.io/immer/patches).\r\n         *\r\n         * This is typically used as the first step in implementing optimistic updates. The generated `inversePatches` can be used to revert the updates by calling `dispatch(patchQueryData(endpointName, args, inversePatches))`. Alternatively, the `undo` method can be called directly to achieve the same effect.\r\n         *\r\n         * Note that the first two arguments (`endpointName` and `args`) are used to determine which existing cache entry to update. If no existing cache entry is found, the `updateRecipe` callback will not run.\r\n         *\r\n         * @example\r\n         *\r\n         * ```ts\r\n         * const patchCollection = dispatch(\r\n         *   api.util.updateQueryData('getPosts', undefined, (draftPosts) => {\r\n         *     draftPosts.push({ id: 1, name: 'Teddy' })\r\n         *   })\r\n         * )\r\n         * ```\r\n         */\n\n        updateQueryData: UpdateQueryDataThunk<Definitions, RootState<Definitions, string, ReducerPath>>;\n        /**\r\n         * A Redux thunk action creator that, when dispatched, acts as an artificial API request to upsert a value into the cache.\r\n         *\r\n         * The thunk action creator accepts three arguments: the name of the endpoint we are updating (such as `'getPost'`), the appropriate query arg values to construct the desired cache key, and the data to upsert.\r\n         *\r\n         * If no cache entry for that cache key exists, a cache entry will be created and the data added. If a cache entry already exists, this will _overwrite_ the existing cache entry data.\r\n         *\r\n         * The thunk executes _asynchronously_, and returns a promise that resolves when the store has been updated.\r\n         *\r\n         * If dispatched while an actual request is in progress, both the upsert and request will be handled as soon as they resolve, resulting in a \"last result wins\" update behavior.\r\n         *\r\n         * @example\r\n         *\r\n         * ```ts\r\n         * await dispatch(\r\n         *   api.util.upsertQueryData('getPost', {id: 1}, {id: 1, text: \"Hello!\"})\r\n         * )\r\n         * ```\r\n         */\n\n        upsertQueryData: UpsertQueryDataThunk<Definitions, RootState<Definitions, string, ReducerPath>>;\n        /**\r\n         * A Redux thunk that applies a JSON diff/patch array to the cached data for a given query result. This immediately updates the Redux state with those changes.\r\n         *\r\n         * The thunk accepts three arguments: the name of the endpoint we are updating (such as `'getPost'`), the appropriate query arg values to construct the desired cache key, and a JSON diff/patch array as produced by Immer's `produceWithPatches`.\r\n         *\r\n         * This is typically used as the second step in implementing optimistic updates. If a request fails, the optimistically-applied changes can be reverted by dispatching `patchQueryData` with the `inversePatches` that were generated by `updateQueryData` earlier.\r\n         *\r\n         * In cases where it is desired to simply revert the previous changes, it may be preferable to call the `undo` method returned from dispatching `updateQueryData` instead.\r\n         *\r\n         * @example\r\n         * ```ts\r\n         * const patchCollection = dispatch(\r\n         *   api.util.updateQueryData('getPosts', undefined, (draftPosts) => {\r\n         *     draftPosts.push({ id: 1, name: 'Teddy' })\r\n         *   })\r\n         * )\r\n         *\r\n         * // later\r\n         * dispatch(\r\n         *   api.util.patchQueryData('getPosts', undefined, patchCollection.inversePatches)\r\n         * )\r\n         *\r\n         * // or\r\n         * patchCollection.undo()\r\n         * ```\r\n         */\n\n        patchQueryData: PatchQueryDataThunk<Definitions, RootState<Definitions, string, ReducerPath>>;\n        /**\r\n         * A Redux action creator that can be dispatched to manually reset the api state completely. This will immediately remove all existing cache entries, and all queries will be considered 'uninitialized'.\r\n         *\r\n         * @example\r\n         *\r\n         * ```ts\r\n         * dispatch(api.util.resetApiState())\r\n         * ```\r\n         */\n\n        resetApiState: SliceActions['resetApiState'];\n        /**\r\n         * A Redux action creator that can be used to manually invalidate cache tags for [automated re-fetching](../../usage/automated-refetching.mdx).\r\n         *\r\n         * The action creator accepts one argument: the cache tags to be invalidated. It returns an action with those tags as a payload, and the corresponding `invalidateTags` action type for the api.\r\n         *\r\n         * Dispatching the result of this action creator will [invalidate](../../usage/automated-refetching.mdx#invalidating-cache-data) the given tags, causing queries to automatically re-fetch if they are subscribed to cache data that [provides](../../usage/automated-refetching.mdx#providing-cache-data) the corresponding tags.\r\n         *\r\n         * The array of tags provided to the action creator should be in one of the following formats, where `TagType` is equal to a string provided to the [`tagTypes`](../createApi.mdx#tagtypes) property of the api:\r\n         *\r\n         * - `[TagType]`\r\n         * - `[{ type: TagType }]`\r\n         * - `[{ type: TagType, id: number | string }]`\r\n         *\r\n         * @example\r\n         *\r\n         * ```ts\r\n         * dispatch(api.util.invalidateTags(['Post']))\r\n         * dispatch(api.util.invalidateTags([{ type: 'Post', id: 1 }]))\r\n         * dispatch(\r\n         *   api.util.invalidateTags([\r\n         *     { type: 'Post', id: 1 },\r\n         *     { type: 'Post', id: 'LIST' },\r\n         *   ])\r\n         * )\r\n         * ```\r\n         */\n\n        invalidateTags: ActionCreatorWithPayload<Array<TagDescription<TagTypes>>, string>;\n        /**\r\n         * A function to select all `{ endpointName, originalArgs, queryCacheKey }` combinations that would be invalidated by a specific set of tags.\r\n         *\r\n         * Can be used for mutations that want to do optimistic updates instead of invalidating a set of tags, but don't know exactly what they need to update.\r\n         */\n\n        selectInvalidatedBy: (state: RootState<Definitions, string, ReducerPath>, tags: ReadonlyArray<TagDescription<TagTypes>>) => Array<{\n          endpointName: string;\n          originalArgs: any;\n          queryCacheKey: string;\n        }>;\n        /**\r\n         * A function to select all arguments currently cached for a given endpoint.\r\n         *\r\n         * Can be used for mutations that want to do optimistic updates instead of invalidating a set of tags, but don't know exactly what they need to update.\r\n         */\n\n        selectCachedArgsForQuery: <QueryName extends QueryKeys<Definitions>>(state: RootState<Definitions, string, ReducerPath>, queryName: QueryName) => Array<QueryArgFrom<Definitions[QueryName]>>;\n      };\n      /**\r\n       * Endpoints based on the input endpoints provided to `createApi`, containing `select` and `action matchers`.\r\n       */\n\n      endpoints: { [K in keyof Definitions]: Definitions[K] extends QueryDefinition<any, any, any, any, any> ? ApiEndpointQuery<Definitions[K], Definitions> : Definitions[K] extends MutationDefinition<any, any, any, any, any> ? ApiEndpointMutation<Definitions[K], Definitions> : never };\n    };\n  }\n}\nexport interface ApiEndpointQuery< // eslint-disable-next-line @typescript-eslint/no-unused-vars\nDefinition extends QueryDefinition<any, any, any, any, any>, // eslint-disable-next-line @typescript-eslint/no-unused-vars\nDefinitions extends EndpointDefinitions> {\n  name: string;\n  /**\r\n   * All of these are `undefined` at runtime, purely to be used in TypeScript declarations!\r\n   */\n\n  Types: NonNullable<Definition['Types']>;\n} // eslint-disable-next-line @typescript-eslint/no-unused-vars\n\nexport interface ApiEndpointMutation< // eslint-disable-next-line @typescript-eslint/no-unused-vars\nDefinition extends MutationDefinition<any, any, any, any, any>, // eslint-disable-next-line @typescript-eslint/no-unused-vars\nDefinitions extends EndpointDefinitions> {\n  name: string;\n  /**\r\n   * All of these are `undefined` at runtime, purely to be used in TypeScript declarations!\r\n   */\n\n  Types: NonNullable<Definition['Types']>;\n}\nexport type ListenerActions = {\n  /**\r\n   * Will cause the RTK Query middleware to trigger any refetchOnReconnect-related behavior\r\n   * @link https://rtk-query-docs.netlify.app/api/setupListeners\r\n   */\n  onOnline: typeof onOnline;\n  onOffline: typeof onOffline;\n  /**\r\n   * Will cause the RTK Query middleware to trigger any refetchOnFocus-related behavior\r\n   * @link https://rtk-query-docs.netlify.app/api/setupListeners\r\n   */\n\n  onFocus: typeof onFocus;\n  onFocusLost: typeof onFocusLost;\n};\nexport type InternalActions = SliceActions & ListenerActions;\n/**\r\n * Creates a module containing the basic redux logic for use with `buildCreateApi`.\r\n *\r\n * @example\r\n * ```ts\r\n * const createBaseApi = buildCreateApi(coreModule());\r\n * ```\r\n */\n\nexport const coreModule = (): Module<CoreModule> => ({\n  name: coreModuleName,\n\n  init(api, {\n    baseQuery,\n    tagTypes,\n    reducerPath,\n    serializeQueryArgs,\n    keepUnusedDataFor,\n    refetchOnMountOrArgChange,\n    refetchOnFocus,\n    refetchOnReconnect,\n    invalidationBehavior\n  }, context) {\n    enablePatches();\n    assertCast<InternalSerializeQueryArgs>(serializeQueryArgs);\n\n    const assertTagType: AssertTagTypes = tag => {\n      if (typeof process !== 'undefined' && process.env.NODE_ENV === 'development') {\n        if (!tagTypes.includes((tag.type as any))) {\n          console.error(`Tag type '${tag.type}' was used, but not specified in \\`tagTypes\\`!`);\n        }\n      }\n\n      return tag;\n    };\n\n    Object.assign(api, {\n      reducerPath,\n      endpoints: {},\n      internalActions: {\n        onOnline,\n        onOffline,\n        onFocus,\n        onFocusLost\n      },\n      util: {}\n    });\n    const {\n      queryThunk,\n      mutationThunk,\n      patchQueryData,\n      updateQueryData,\n      upsertQueryData,\n      prefetch,\n      buildMatchThunkActions\n    } = buildThunks({\n      baseQuery,\n      reducerPath,\n      context,\n      api,\n      serializeQueryArgs,\n      assertTagType\n    });\n    const {\n      reducer,\n      actions: sliceActions\n    } = buildSlice({\n      context,\n      queryThunk,\n      mutationThunk,\n      reducerPath,\n      assertTagType,\n      config: {\n        refetchOnFocus,\n        refetchOnReconnect,\n        refetchOnMountOrArgChange,\n        keepUnusedDataFor,\n        reducerPath,\n        invalidationBehavior\n      }\n    });\n    safeAssign(api.util, {\n      patchQueryData,\n      updateQueryData,\n      upsertQueryData,\n      prefetch,\n      resetApiState: sliceActions.resetApiState\n    });\n    safeAssign(api.internalActions, sliceActions);\n    const {\n      middleware,\n      actions: middlewareActions\n    } = buildMiddleware({\n      reducerPath,\n      context,\n      queryThunk,\n      mutationThunk,\n      api,\n      assertTagType\n    });\n    safeAssign(api.util, middlewareActions);\n    safeAssign(api, {\n      reducer: (reducer as any),\n      middleware\n    });\n    const {\n      buildQuerySelector,\n      buildMutationSelector,\n      selectInvalidatedBy,\n      selectCachedArgsForQuery\n    } = buildSelectors({\n      serializeQueryArgs: (serializeQueryArgs as any),\n      reducerPath\n    });\n    safeAssign(api.util, {\n      selectInvalidatedBy,\n      selectCachedArgsForQuery\n    });\n    const {\n      buildInitiateQuery,\n      buildInitiateMutation,\n      getRunningMutationThunk,\n      getRunningMutationsThunk,\n      getRunningQueriesThunk,\n      getRunningQueryThunk\n    } = buildInitiate({\n      queryThunk,\n      mutationThunk,\n      api,\n      serializeQueryArgs: (serializeQueryArgs as any),\n      context\n    });\n    safeAssign(api.util, {\n      getRunningMutationThunk,\n      getRunningMutationsThunk,\n      getRunningQueryThunk,\n      getRunningQueriesThunk\n    });\n    return {\n      name: coreModuleName,\n\n      injectEndpoint(endpointName, definition) {\n        const anyApi = ((api as any) as Api<any, Record<string, any>, string, string, CoreModule>);\n        anyApi.endpoints[endpointName] ??= ({} as any);\n\n        if (isQueryDefinition(definition)) {\n          safeAssign(anyApi.endpoints[endpointName], {\n            name: endpointName,\n            select: buildQuerySelector(endpointName, definition),\n            initiate: buildInitiateQuery(endpointName, definition)\n          }, buildMatchThunkActions(queryThunk, endpointName));\n        } else if (isMutationDefinition(definition)) {\n          safeAssign(anyApi.endpoints[endpointName], {\n            name: endpointName,\n            select: buildMutationSelector(),\n            initiate: buildInitiateMutation(endpointName)\n          }, buildMatchThunkActions(mutationThunk, endpointName));\n        }\n      }\n\n    };\n  }\n\n});", "import { buildCreate<PERSON><PERSON>, Create<PERSON>pi } from '../createApi';\nimport { coreModule, coreModuleName } from './module';\nconst createApi = /* @__PURE__ */buildCreateApi(coreModule());\nexport { createApi, coreModule, coreModuleName };"], "mappings": "ubAAA,IAAAA,GAAA,GAAAC,GAAAD,GAAA,iBAAAE,GAAA,mBAAAC,GAAA,8BAAAC,EAAA,eAAAC,GAAA,mBAAAC,GAAA,cAAAC,GAAA,8BAAAC,GAAA,kBAAAC,GAAA,mBAAAC,GAAA,UAAAC,GAAA,mBAAAC,GAAA,cAAAC,KAAA,eAAAC,GAAAd,IC0BO,IAAKe,QACVA,EAAA,cAAgB,gBAChBA,EAAA,QAAU,UACVA,EAAA,UAAY,YACZA,EAAA,SAAW,WAJDA,QAAA,IA+BL,SAASC,GAAsBC,EAAyC,CAC7E,MAAQ,CACN,OAAAA,EACA,gBAAiBA,IAAW,gBAC5B,UAAWA,IAAW,UACtB,UAAWA,IAAW,YACtB,QAASA,IAAW,UACtB,CACF,CC5DO,SAASC,GAAcC,EAAa,CACzC,OAAO,IAAI,OAAO,SAAS,EAAE,KAAKA,CAAG,CACvC,CCLA,IAAMC,GAAwBC,GAAgBA,EAAI,QAAQ,MAAO,EAAE,EAE7DC,GAAuBD,GAAgBA,EAAI,QAAQ,MAAO,EAAE,EAE3D,SAASE,GAASC,EAA0BH,EAAiC,CAClF,GAAI,CAACG,EACH,OAAOH,EAGT,GAAI,CAACA,EACH,OAAOG,EAGT,GAAIC,GAAcJ,CAAG,EACnB,OAAOA,EAGT,IAAMK,EAAYF,EAAK,SAAS,GAAG,GAAK,CAACH,EAAI,WAAW,GAAG,EAAI,IAAM,GACrE,OAAAG,EAAOJ,GAAqBI,CAAI,EAChCH,EAAMC,GAAoBD,CAAG,EACtB,GAAGG,CAAI,GAAGE,CAAS,GAAGL,CAAG,EAClC,CClBO,IAAMM,GAAWC,GAAwB,CAAC,EAAE,OAAO,GAAGA,CAAG,ECDzD,SAASC,IAAW,CAEzB,OAAO,OAAO,UAAc,KAAqB,UAAU,SAAW,OAA5B,GAA+C,UAAU,MACrG,CCHO,SAASC,IAA6B,CAE3C,OAAI,OAAO,SAAa,IACf,GAIF,SAAS,kBAAoB,QACtC,CCTA,IAAAC,EAAoR,4BCDpR,IAAMC,GAAqC,gBAEpC,SAASC,EAA0BC,EAAaC,EAAkB,CACvE,GAAID,IAAWC,GAAU,EAAEH,GAAcE,CAAM,GAAKF,GAAcG,CAAM,GAAK,MAAM,QAAQD,CAAM,GAAK,MAAM,QAAQC,CAAM,GACxH,OAAOA,EAGT,IAAMC,EAAU,OAAO,KAAKD,CAAM,EAC5BE,EAAU,OAAO,KAAKH,CAAM,EAC9BI,EAAeF,EAAQ,SAAWC,EAAQ,OACxCE,EAAgB,MAAM,QAAQJ,CAAM,EAAI,CAAC,EAAI,CAAC,EAEpD,QAAWK,KAAOJ,EAChBG,EAASC,CAAG,EAAIP,EAA0BC,EAAOM,CAAG,EAAGL,EAAOK,CAAG,CAAC,EAC9DF,IAAcA,EAAeJ,EAAOM,CAAG,IAAMD,EAASC,CAAG,GAG/D,OAAOF,EAAeJ,EAASK,CACjC,CCMA,IAAME,GAA+B,IAAIC,IAAS,MAAM,GAAGA,CAAI,EAEzDC,GAAyBC,GAAuBA,EAAS,QAAU,KAAOA,EAAS,QAAU,IAE7FC,GAA4BC,GAElC,yBAAyB,KAAKA,EAAQ,IAAI,cAAc,GAAK,EAAE,EA8C/D,SAASC,GAAeC,EAAU,CAChC,GAAI,IAAC,iBAAcA,CAAG,EACpB,OAAOA,EAGT,IAAMC,EAA4B,CAAE,GAAGD,CACvC,EAEA,OAAW,CAACE,EAAGC,CAAC,IAAK,OAAO,QAAQF,CAAI,EAClCE,IAAM,QAAW,OAAOF,EAAKC,CAAC,EAGpC,OAAOD,CACT,CA+EO,SAASG,GAAe,CAC7B,QAAAC,EACA,eAAAC,EAAiBC,GAAKA,EACtB,QAAAC,EAAUf,GACV,iBAAAgB,EACA,kBAAAC,EAAoBb,GACpB,gBAAAc,EAAkB,mBAClB,aAAAC,EACA,QAASC,EACT,gBAAiBC,EACjB,eAAgBC,EAChB,GAAGC,CACL,EAAwB,CAAC,EAA0F,CACjH,OAAI,OAAO,MAAU,KAAeR,IAAYf,IAC9C,QAAQ,KAAK,2HAA2H,EAGnI,MAAOwB,EAAKC,IAAQ,CACzB,GAAM,CACJ,OAAAC,EACA,SAAAC,EACA,MAAAC,EACA,SAAAC,EACA,OAAAC,EACA,KAAAC,CACF,EAAIN,EACAO,EACA,CACF,IAAAC,EACA,QAAA5B,EAAU,IAAI,QAAQkB,EAAiB,OAAO,EAC9C,OAAAW,EAAS,OACT,gBAAAC,EAAkBd,GAA0B,OAC5C,eAAAe,EAAiBd,GAAwBpB,GACzC,QAAAmC,EAAUjB,EACV,GAAGkB,CACL,EAAI,OAAOd,GAAO,SAAW,CAC3B,IAAKA,CACP,EAAIA,EACAe,EAAsB,CAAE,GAAGhB,EAC7B,OAAAG,EACA,GAAGY,CACL,EACAjC,EAAU,IAAI,QAAQC,GAAeD,CAAO,CAAC,EAC7CkC,EAAO,QAAW,MAAM1B,EAAeR,EAAS,CAC9C,SAAAsB,EACA,MAAAC,EACA,SAAAC,EACA,OAAAC,EACA,KAAAC,CACF,CAAC,GAAM1B,EAEP,IAAMmC,EAAiBC,GAAc,OAAOA,GAAS,cAAa,iBAAcA,CAAI,GAAK,MAAM,QAAQA,CAAI,GAAK,OAAOA,EAAK,QAAW,YAUvI,GARI,CAACF,EAAO,QAAQ,IAAI,cAAc,GAAKC,EAAcD,EAAO,IAAI,GAClEA,EAAO,QAAQ,IAAI,eAAgBrB,CAAe,EAGhDsB,EAAcD,EAAO,IAAI,GAAKtB,EAAkBsB,EAAO,OAAO,IAChEA,EAAO,KAAO,KAAK,UAAUA,EAAO,KAAMpB,CAAY,GAGpDe,EAAQ,CACV,IAAMQ,EAAU,CAACT,EAAI,QAAQ,GAAG,EAAI,IAAM,IACpCU,EAAQ3B,EAAmBA,EAAiBkB,CAAM,EAAI,IAAI,gBAAgB5B,GAAe4B,CAAM,CAAC,EACtGD,GAAOS,EAAUC,CACnB,CAEAV,EAAMW,GAAShC,EAASqB,CAAG,EAC3B,IAAMY,EAAU,IAAI,QAAQZ,EAAKM,CAAM,EAEvCP,EAAO,CACL,QAFmB,IAAI,QAAQC,EAAKM,CAAM,CAG5C,EACA,IAAIpC,EACA2C,EAAW,GACXC,EAAYV,GAAW,WAAW,IAAM,CAC1CS,EAAW,GACXrB,EAAI,MAAM,CACZ,EAAGY,CAAO,EAEV,GAAI,CACFlC,EAAW,MAAMY,EAAQ8B,CAAO,CAClC,OAASG,EAAG,CACV,MAAO,CACL,MAAO,CACL,OAAQF,EAAW,gBAAkB,cACrC,MAAO,OAAOE,CAAC,CACjB,EACA,KAAAhB,CACF,CACF,QAAE,CACIe,GAAW,aAAaA,CAAS,CACvC,CAEA,IAAME,EAAgB9C,EAAS,MAAM,EACrC6B,EAAK,SAAWiB,EAChB,IAAIC,EACAC,EAAuB,GAE3B,GAAI,CACF,IAAIC,EAIJ,GAHA,MAAM,QAAQ,IAAI,CAACC,EAAelD,EAAUgC,CAAe,EAAE,KAAKmB,GAAKJ,EAAaI,EAAGN,GAAKI,EAAsBJ,CAAC,EAEnHC,EAAc,KAAK,EAAE,KAAKK,GAAKH,EAAeG,EAAG,IAAM,CAAC,CAAC,CAAC,CAAC,EACvDF,EAAqB,MAAMA,CACjC,OAASJ,EAAG,CACV,MAAO,CACL,MAAO,CACL,OAAQ,gBACR,eAAgB7C,EAAS,OACzB,KAAMgD,EACN,MAAO,OAAOH,CAAC,CACjB,EACA,KAAAhB,CACF,CACF,CAEA,OAAOI,EAAejC,EAAU+C,CAAU,EAAI,CAC5C,KAAMA,EACN,KAAAlB,CACF,EAAI,CACF,MAAO,CACL,OAAQ7B,EAAS,OACjB,KAAM+C,CACR,EACA,KAAAlB,CACF,CACF,EAEA,eAAeqB,EAAelD,EAAoBgC,EAAkC,CAClF,GAAI,OAAOA,GAAoB,WAC7B,OAAOA,EAAgBhC,CAAQ,EAOjC,GAJIgC,IAAoB,iBACtBA,EAAkBlB,EAAkBd,EAAS,OAAO,EAAI,OAAS,QAG/DgC,IAAoB,OAAQ,CAC9B,IAAMoB,EAAO,MAAMpD,EAAS,KAAK,EACjC,OAAOoD,EAAK,OAAS,KAAK,MAAMA,CAAI,EAAI,IAC1C,CAEA,OAAOpD,EAAS,KAAK,CACvB,CACF,CC3TO,IAAMqD,EAAN,KAAmB,CACxB,YAA4BC,EAA4BC,EAAY,OAAW,CAAnD,WAAAD,EAA4B,UAAAC,CAAwB,CAElF,ECcA,eAAeC,GAAeC,EAAkB,EAAGC,EAAqB,EAAG,CACzE,IAAMC,EAAW,KAAK,IAAIF,EAASC,CAAU,EACvCE,EAAU,CAAC,GAAG,KAAK,OAAO,EAAI,KAAQ,KAAOD,IAEnD,MAAM,IAAI,QAAQE,GAAW,WAAYC,GAAaD,EAAQC,CAAG,EAAGF,CAAO,CAAC,CAC9E,CA2BA,SAASG,GAAK,EAAe,CAC3B,MAAM,OAAO,OAAO,IAAIC,EAAa,CACnC,MAAO,CACT,CAAC,EAAG,CACF,iBAAkB,EACpB,CAAC,CACH,CAEA,IAAMC,GAAgB,CAAC,EAEjBC,GAAkF,CAACC,EAAWC,IAAmB,MAAOC,EAAMC,EAAKC,IAAiB,CAIxJ,IAAMC,EAA+B,CAAC,GAAKJ,GAA0BH,IAAe,YAAcM,GAAwBN,IAAe,UAAU,EAAE,OAAOQ,GAAKA,IAAM,MAAS,EAC1K,CAACf,CAAU,EAAIc,EAAmB,MAAM,EAAE,EAM1CE,EAIF,CACF,WAAAhB,EACA,QAASF,GACT,eAXoD,CAACmB,EAAGC,EAAI,CAC5D,QAAAnB,CACF,IAAMA,GAAWC,EAUf,GAAGU,EACH,GAAGG,CACL,EACIM,EAAQ,EAEZ,OACE,GAAI,CACF,IAAMC,EAAS,MAAMX,EAAUE,EAAMC,EAAKC,CAAY,EAEtD,GAAIO,EAAO,MACT,MAAM,IAAId,EAAac,CAAM,EAG/B,OAAOA,CACT,OAASC,EAAQ,CAGf,GAFAF,IAEIE,EAAE,iBAAkB,CACtB,GAAIA,aAAaf,EACf,OAAOe,EAAE,MAIX,MAAMA,CACR,CAEA,GAAIA,aAAaf,GAAgB,CAACU,EAAQ,eAAgBK,EAAE,MAAM,MAA+BV,EAAM,CACrG,QAASQ,EACT,aAAcP,EACd,aAAAC,CACF,CAAC,EACC,OAAOQ,EAAE,MAGX,MAAML,EAAQ,QAAQG,EAAOH,EAAQ,UAAU,CACjD,CAEJ,EAmCaG,GAAuB,OAAO,OAAOX,GAAkB,CAClE,KAAAH,EACF,CAAC,ECrJM,IAAMiB,KAAyB,gBAAa,gBAAgB,EACtDC,KAA6B,gBAAa,kBAAkB,EAC5DC,KAA0B,gBAAa,eAAe,EACtDC,KAA2B,gBAAa,gBAAgB,EACjEC,GAAc,GAkBX,SAASC,GAAeC,EAAwCC,EAKrD,CAChB,SAASC,GAAiB,CACxB,IAAMC,EAAc,IAAMH,EAASN,EAAQ,CAAC,EAEtCU,EAAkB,IAAMJ,EAASL,EAAY,CAAC,EAE9CU,EAAe,IAAML,EAASJ,EAAS,CAAC,EAExCU,EAAgB,IAAMN,EAASH,EAAU,CAAC,EAE1CU,EAAyB,IAAM,CAC/B,OAAO,SAAS,kBAAoB,UACtCJ,EAAY,EAEZC,EAAgB,CAEpB,EAEA,OAAKN,IACC,OAAO,OAAW,KAAe,OAAO,mBAE1C,OAAO,iBAAiB,mBAAoBS,EAAwB,EAAK,EACzE,OAAO,iBAAiB,QAASJ,EAAa,EAAK,EAEnD,OAAO,iBAAiB,SAAUE,EAAc,EAAK,EACrD,OAAO,iBAAiB,UAAWC,EAAe,EAAK,EACvDR,GAAc,IAIE,IAAM,CACxB,OAAO,oBAAoB,QAASK,CAAW,EAC/C,OAAO,oBAAoB,mBAAoBI,CAAsB,EACrE,OAAO,oBAAoB,SAAUF,CAAY,EACjD,OAAO,oBAAoB,UAAWC,CAAa,EACnDR,GAAc,EAChB,CAGF,CAEA,OAAOG,EAAgBA,EAAcD,EAAU,CAC7C,QAAAN,EACA,YAAAC,EACA,UAAAE,EACA,SAAAD,CACF,CAAC,EAAIM,EAAe,CACtB,CCwYO,SAASM,GAAkB,EAAqF,CACrH,OAAO,EAAE,OAAS,OACpB,CACO,SAASC,GAAqB,EAAwF,CAC3H,OAAO,EAAE,OAAS,UACpB,CA2DO,SAASC,EAA+DC,EAA+FC,EAAgCC,EAA8BC,EAAoBC,EAA4BC,EAAuE,CACjW,OAAIC,GAAWN,CAAW,EACjBA,EAAaC,EAAwBC,EAAqBC,EAAWC,CAAiB,EAAE,IAAIG,EAAoB,EAAE,IAAIF,CAAc,EAGzI,MAAM,QAAQL,CAAW,EACpBA,EAAY,IAAIO,EAAoB,EAAE,IAAIF,CAAc,EAG1D,CAAC,CACV,CAEA,SAASC,GAAcE,EAAiC,CACtD,OAAO,OAAOA,GAAM,UACtB,CAEO,SAASD,GAAqBP,EAAiE,CACpG,OAAO,OAAOA,GAAgB,SAAW,CACvC,KAAMA,CACR,EAAIA,CACN,CCziBA,IAAAS,GAAkE,4BCA3D,SAASC,GAAgBC,EAAiC,CAC/D,OAAOA,GAAK,IACd,CCEO,SAASC,EAAgBC,EAAuB,CACrD,IAAIC,EAAQ,EAEZ,QAAWC,KAAQF,EACjBC,IAGF,OAAOA,CACT,CFYO,IAAME,EAAqB,OAAO,cAAc,EAC1CC,EAAiBC,GAAuB,OAAOA,EAAIF,CAAkB,GAAM,WAyHjF,SAASG,GAAc,CAC5B,mBAAAC,EACA,WAAAC,EACA,cAAAC,EACA,IAAAC,EACA,QAAAC,CACF,EAMG,CACD,IAAMC,EAA2F,IAAI,IAC/FC,EAAgG,IAAI,IACpG,CACJ,uBAAAC,EACA,qBAAAC,EACA,0BAAAC,CACF,EAAIN,EAAI,gBACR,MAAO,CACL,mBAAAO,EACA,sBAAAC,EACA,qBAAAC,EACA,wBAAAC,EACA,uBAAAC,EACA,yBAAAC,CACF,EAEA,SAASH,EAAqBI,EAAsBC,EAAgB,CAClE,OAAQC,GAAuB,CAC7B,IAAMC,EAAqBf,EAAQ,oBAAoBY,CAAY,EAC7DI,EAAgBpB,EAAmB,CACvC,UAAAiB,EACA,mBAAAE,EACA,aAAAH,CACF,CAAC,EACD,OAAQX,EAAe,IAAIa,CAAQ,IAAIE,CAAa,CACtD,CACF,CAEA,SAASP,EAKTQ,EAAuBC,EAAkC,CACvD,OAAQJ,GACEZ,EAAiB,IAAIY,CAAQ,IAAII,CAAwB,CAErE,CAEA,SAASR,GAAyB,CAChC,OAAQI,GAAuB,OAAO,OAAOb,EAAe,IAAIa,CAAQ,GAAK,CAAC,CAAC,EAAE,OAAOK,EAAY,CACtG,CAEA,SAASR,GAA2B,CAClC,OAAQG,GAAuB,OAAO,OAAOZ,EAAiB,IAAIY,CAAQ,GAAK,CAAC,CAAC,EAAE,OAAOK,EAAY,CACxG,CAEA,SAASC,EAAkBN,EAAoB,CAa/C,CAEA,SAASR,EAAmBM,EAAsBG,EAAyD,CACzG,IAAMM,EAA4C,CAAC3B,EAAK,CACtD,UAAA4B,EAAY,GACZ,aAAAC,EACA,oBAAAC,EACA,CAAChC,GAAqBiC,CACxB,EAAI,CAAC,IAAM,CAACX,EAAUY,IAAa,CACjC,IAAMV,EAAgBpB,EAAmB,CACvC,UAAWF,EACX,mBAAAqB,EACA,aAAAH,CACF,CAAC,EACKe,EAAQ9B,EAAW,CACvB,KAAM,QACN,UAAAyB,EACA,aAAcC,EACd,oBAAAC,EACA,aAAAZ,EACA,aAAclB,EACd,cAAAsB,EACA,CAACxB,CAAkB,EAAGiC,CACxB,CAAC,EACKG,EAAY7B,EAAI,UAAUa,CAAY,EAAiC,OAAOlB,CAAG,EACjFmC,EAAcf,EAASa,CAAK,EAC5BG,EAAaF,EAASF,EAAS,CAAC,EAEtC,GAAM,CACJ,UAAAK,EACA,MAAAC,CACF,EAAIH,EACEI,EAAuBH,EAAW,YAAcC,EAChDG,EAAejC,EAAe,IAAIa,CAAQ,IAAIE,CAAa,EAE3DmB,EAAkB,IAAMP,EAASF,EAAS,CAAC,EAE3CU,EAA8C,OAAO,OAAOX,EAElEI,EAAY,KAAKM,CAAe,EAAIF,GAAwB,CAACC,EAE7D,QAAQ,QAAQJ,CAAU,EAE1B,QAAQ,IAAI,CAACI,EAAcL,CAAW,CAAC,EAAE,KAAKM,CAAe,EAAG,CAC9D,IAAAzC,EACA,UAAAqC,EACA,oBAAAP,EACA,cAAAR,EACA,MAAAgB,EAEA,MAAM,QAAS,CACb,IAAMK,EAAS,MAAMD,EAErB,GAAIC,EAAO,QACT,MAAMA,EAAO,MAGf,OAAOA,EAAO,IAChB,EAEA,QAAS,IAAMvB,EAASO,EAAY3B,EAAK,CACvC,UAAW,GACX,aAAc,EAChB,CAAC,CAAC,EAEF,aAAc,CACR4B,GAAWR,EAASX,EAAuB,CAC7C,cAAAa,EACA,UAAAe,CACF,CAAC,CAAC,CACJ,EAEA,0BAA0BO,EAA8B,CACtDF,EAAa,oBAAsBE,EACnCxB,EAAST,EAA0B,CACjC,aAAAO,EACA,UAAAmB,EACA,cAAAf,EACA,QAAAsB,CACF,CAAC,CAAC,CACJ,CAEF,CAAC,EAED,GAAI,CAACJ,GAAgB,CAACD,GAAwB,CAACR,EAAc,CAC3D,IAAMc,EAAUtC,EAAe,IAAIa,CAAQ,GAAK,CAAC,EACjDyB,EAAQvB,CAAa,EAAIoB,EACzBnC,EAAe,IAAIa,EAAUyB,CAAO,EACpCH,EAAa,KAAK,IAAM,CACtB,OAAOG,EAAQvB,CAAa,EAEvBwB,EAAgBD,CAAO,GAC1BtC,EAAe,OAAOa,CAAQ,CAElC,CAAC,CACH,CAEA,OAAOsB,CACT,EAEA,OAAOf,CACT,CAEA,SAASd,EAAsBK,EAAuD,CACpF,MAAO,CAAClB,EAAK,CACX,MAAA+C,EAAQ,GACR,cAAAC,CACF,EAAI,CAAC,IAAM,CAAC5B,EAAUY,IAAa,CACjC,IAAMC,EAAQ7B,EAAc,CAC1B,KAAM,WACN,aAAAc,EACA,aAAclB,EACd,MAAA+C,EACA,cAAAC,CACF,CAAC,EACKb,EAAcf,EAASa,CAAK,EAElC,GAAM,CACJ,UAAAI,EACA,MAAAC,EACA,OAAAW,CACF,EAAId,EACEe,EAAqBf,EAAY,OAAO,EAAE,KAAKgB,IAAS,CAC5D,KAAAA,CACF,EAAE,EAAE,MAAMC,IAAU,CAClB,MAAAA,CACF,EAAE,EAEIC,EAAQ,IAAM,CAClBjC,EAASV,EAAqB,CAC5B,UAAA2B,EACA,cAAAW,CACF,CAAC,CAAC,CACJ,EAEMM,EAAM,OAAO,OAAOJ,EAAoB,CAC5C,IAAKf,EAAY,IACjB,UAAAE,EACA,MAAAC,EACA,OAAAW,EACA,MAAAI,CACF,CAAC,EACKR,EAAUrC,EAAiB,IAAIY,CAAQ,GAAK,CAAC,EACnD,OAAAZ,EAAiB,IAAIY,EAAUyB,CAAO,EACtCA,EAAQR,CAAS,EAAIiB,EACrBA,EAAI,KAAK,IAAM,CACb,OAAOT,EAAQR,CAAS,EAEnBS,EAAgBD,CAAO,GAC1BrC,EAAiB,OAAOY,CAAQ,CAEpC,CAAC,EAEG4B,IACFH,EAAQG,CAAa,EAAIM,EACzBA,EAAI,KAAK,IAAM,CACTT,EAAQG,CAAa,IAAMM,IAC7B,OAAOT,EAAQG,CAAa,EAEvBF,EAAgBD,CAAO,GAC1BrC,EAAiB,OAAOY,CAAQ,EAGtC,CAAC,GAGIkC,CACT,CACF,CACF,CGvXA,IAAAC,GAAgD,iBAwDhD,SAASC,GAAyBC,EAA+B,CAC/D,OAAOA,CACT,CA4BO,SAASC,GAAgH,CAC9H,YAAAC,EACA,UAAAC,EACA,QAAS,CACP,oBAAAC,CACF,EACA,mBAAAC,EACA,IAAAC,EACA,cAAAC,CACF,EAOG,CAGD,IAAMC,EAAkE,CAACC,EAAcC,EAAMC,EAASC,IAAmB,CAACC,EAAUC,IAAa,CAC/I,IAAMC,EAAqBX,EAAoBK,CAAY,EACrDO,EAAgBX,EAAmB,CACvC,UAAWK,EACX,mBAAAK,EACA,aAAAN,CACF,CAAC,EAMD,GALAI,EAASP,EAAI,gBAAgB,mBAAmB,CAC9C,cAAAU,EACA,QAAAL,CACF,CAAC,CAAC,EAEE,CAACC,EACH,OAGF,IAAMK,EAAWX,EAAI,UAAUG,CAAY,EAAE,OAAOC,CAAI,EACxDI,EAAS,CAA8B,EACjCI,EAAeC,EAAoBJ,EAAmB,aAAcE,EAAS,KAAM,OAAWP,EAAM,CAAC,EAAGH,CAAa,EAC3HM,EAASP,EAAI,gBAAgB,iBAAiB,CAC5C,cAAAU,EACA,aAAAE,CACF,CAAC,CAAC,CACJ,EAEME,EAAoE,CAACX,EAAcC,EAAMW,EAAcT,EAAiB,KAAS,CAACC,EAAUC,IAAa,CAE7J,IAAMQ,EADqBhB,EAAI,UAAUG,CAAY,EACb,OAAOC,CAAI,EACnDI,EAAS,CAA8B,EACnCS,EAAuB,CACzB,QAAS,CAAC,EACV,eAAgB,CAAC,EACjB,KAAM,IAAMV,EAASP,EAAI,KAAK,eAAeG,EAAcC,EAAMa,EAAI,eAAgBX,CAAc,CAAC,CACtG,EAEA,GAAIU,EAAa,yBACf,OAAOC,EAGT,IAAIN,EAEJ,GAAI,SAAUK,EACZ,MAAI,gBAAYA,EAAa,IAAI,EAAG,CAClC,GAAM,CAACE,EAAOb,EAASc,CAAc,KAAI,uBAAmBH,EAAa,KAAMD,CAAY,EAC3FE,EAAI,QAAQ,KAAK,GAAGZ,CAAO,EAC3BY,EAAI,eAAe,KAAK,GAAGE,CAAc,EACzCR,EAAWO,CACb,MACEP,EAAWI,EAAaC,EAAa,IAAI,EACzCC,EAAI,QAAQ,KAAK,CACf,GAAI,UACJ,KAAM,CAAC,EACP,MAAON,CACT,CAAC,EACDM,EAAI,eAAe,KAAK,CACtB,GAAI,UACJ,KAAM,CAAC,EACP,MAAOD,EAAa,IACtB,CAAC,EAIL,OAAAT,EAASP,EAAI,KAAK,eAAeG,EAAcC,EAAMa,EAAI,QAASX,CAAc,CAAC,EAC1EW,CACT,EAEMG,EAA4D,CAACjB,EAAcC,EAAMc,IAAUX,GACxFA,EAAUP,EAAI,UAAUG,CAAY,EAA8E,SAASC,EAAM,CACtI,UAAW,GACX,aAAc,GACd,CAACiB,CAAkB,EAAG,KAAO,CAC3B,KAAMH,CACR,EACF,CAAC,CAAC,EAGEI,EAED,MAAOC,EAAK,CACf,OAAAC,EACA,MAAAC,EACA,gBAAAC,EACA,iBAAAC,EACA,SAAApB,EACA,SAAAC,EACA,MAAAoB,CACF,IAAM,CACJ,IAAMnB,EAAqBX,EAAoByB,EAAI,YAAY,EAE/D,GAAI,CACF,IAAIM,EAA6EpC,GAC7EqC,EACEC,EAAe,CACnB,OAAAP,EACA,MAAAC,EACA,SAAAlB,EACA,SAAAC,EACA,MAAAoB,EACA,SAAUL,EAAI,aACd,KAAMA,EAAI,KACV,OAAQA,EAAI,OAAS,QAAUS,EAAcT,EAAKf,EAAS,CAAC,EAAI,MAClE,EACMyB,EAAeV,EAAI,OAAS,QAAUA,EAAIF,CAAkB,EAAI,OA2CtE,GAzCIY,EACFH,EAASG,EAAa,EACbxB,EAAmB,OAC5BqB,EAAS,MAAMjC,EAAUY,EAAmB,MAAMc,EAAI,YAAY,EAAGQ,EAAetB,EAAmB,YAAoB,EAEvHA,EAAmB,oBACrBoB,EAAoBpB,EAAmB,oBAGzCqB,EAAS,MAAMrB,EAAmB,QAAQc,EAAI,aAAcQ,EAAetB,EAAmB,aAAsBc,GAAO1B,EAAU0B,EAAKQ,EAAetB,EAAmB,YAAoB,CAAC,EAG/L,OAAO,QAAY,IA6BnBqB,EAAO,MAAO,MAAM,IAAII,EAAaJ,EAAO,MAAOA,EAAO,IAAI,EAClE,OAAOH,EAAiB,MAAME,EAAkBC,EAAO,KAAMA,EAAO,KAAMP,EAAI,YAAY,EAAG,CAC3F,mBAAoB,KAAK,IAAI,EAC7B,cAAeO,EAAO,KACtB,CAAC,kBAAgB,EAAG,EACtB,CAAC,CACH,OAASK,EAAO,CACd,IAAIC,EAAeD,EAEnB,GAAIC,aAAwBF,EAAc,CACxC,IAAIG,EAAkF5C,GAElFgB,EAAmB,OAASA,EAAmB,yBACjD4B,EAAyB5B,EAAmB,wBAG9C,GAAI,CACF,OAAOiB,EAAgB,MAAMW,EAAuBD,EAAa,MAAOA,EAAa,KAAMb,EAAI,YAAY,EAAG,CAC5G,cAAea,EAAa,KAC5B,CAAC,kBAAgB,EAAG,EACtB,CAAC,CACH,OAASE,EAAG,CACVF,EAAeE,CACjB,CACF,CAEI,aAAO,QAAY,IAIrB,QAAQ,MAAMF,CAAY,EAGtBA,CACR,CACF,EAEA,SAASJ,EAAcT,EAAoBgB,EAA4C,CACrF,IAAMC,EAAeD,EAAM3C,CAAW,GAAG,UAAU2B,EAAI,aAAa,EAC9DkB,EAA8BF,EAAM3C,CAAW,GAAG,OAAO,0BACzD8C,EAAeF,GAAc,mBAC7BG,EAAapB,EAAI,eAAiBA,EAAI,WAAakB,GAEzD,OAAIE,EAEKA,IAAe,KAAS,OAAO,IAAI,IAAM,EAAI,OAAOD,CAAY,GAAK,KAAQC,EAG/E,EACT,CAEA,IAAMC,KAAa,oBAEhB,GAAGhD,CAAW,gBAAiB0B,EAAiB,CACjD,gBAAiB,CACf,MAAO,CACL,iBAAkB,KAAK,IAAI,EAC3B,CAAC,kBAAgB,EAAG,EACtB,CACF,EAEA,UAAUuB,EAAgB,CACxB,SAAArC,CACF,EAAG,CACD,IAAM+B,EAAQ/B,EAAS,EACjBgC,EAAeD,EAAM3C,CAAW,GAAG,UAAUiD,EAAe,aAAa,EACzEH,EAAeF,GAAc,mBAC7BM,EAAaD,EAAe,aAC5BE,EAAcP,GAAc,aAC5B/B,EAAqBX,EAAoB+C,EAAe,YAAY,EAI1E,OAAIG,EAAcH,CAAc,EACvB,GAILL,GAAc,SAAW,UACpB,GAILR,EAAca,EAAgBN,CAAK,GAInCU,GAAkBxC,CAAkB,GAAKA,GAAoB,eAAe,CAC9E,WAAAqC,EACA,YAAAC,EACA,cAAeP,EACf,MAAAD,CACF,CAAC,EACQ,GAIL,CAAAG,CAMN,EAEA,2BAA4B,EAC9B,CAAC,EACKQ,KAAgB,oBAEnB,GAAGtD,CAAW,mBAAoB0B,EAAiB,CACpD,gBAAiB,CACf,MAAO,CACL,iBAAkB,KAAK,IAAI,EAC3B,CAAC,kBAAgB,EAAG,EACtB,CACF,CAEF,CAAC,EAEK6B,EAAeC,GAEhB,UAAWA,EAEVC,EAAaD,GAEd,gBAAiBA,EAEhBE,EAAW,CAA+CnD,EAA4BoB,EAAU6B,IAAyE,CAAC7C,EAAwCC,IAAwB,CAC9O,IAAM+C,EAAQJ,EAAYC,CAAO,GAAKA,EAAQ,MACxCI,EAASH,EAAUD,CAAO,GAAKA,EAAQ,YAEvCK,EAAc,CAACF,EAAiB,KAAUvD,EAAI,UAAUG,CAAY,EAAiC,SAASoB,EAAK,CACvH,aAAcgC,CAChB,CAAC,EAEKG,EAAoB1D,EAAI,UAAUG,CAAY,EAAiC,OAAOoB,CAAG,EAAEf,EAAS,CAAC,EAE3G,GAAI+C,EACFhD,EAASkD,EAAY,CAAC,UACbD,EAAQ,CACjB,IAAMG,EAAkBD,GAAkB,mBAE1C,GAAI,CAACC,EAAiB,CACpBpD,EAASkD,EAAY,CAAC,EACtB,MACF,EAEyB,OAAO,IAAI,IAAM,EAAI,OAAO,IAAI,KAAKE,CAAe,CAAC,GAAK,KAAQH,GAGzFjD,EAASkD,EAAY,CAAC,CAE1B,MAEElD,EAASkD,EAAY,EAAK,CAAC,CAE/B,EAEA,SAASG,EAAgBzD,EAAsB,CAC7C,OAAQ0D,GAAyCA,GAAQ,MAAM,KAAK,eAAiB1D,CACvF,CAEA,SAAS2D,EAAiJC,EAAc5D,EAAsB,CAC5L,MAAQ,CACN,gBAAc,cAAQ,aAAU4D,CAAK,EAAGH,EAAgBzD,CAAY,CAAC,EACrE,kBAAgB,cAAQ,eAAY4D,CAAK,EAAGH,EAAgBzD,CAAY,CAAC,EACzE,iBAAe,cAAQ,cAAW4D,CAAK,EAAGH,EAAgBzD,CAAY,CAAC,CACzE,CACF,CAEA,MAAO,CACL,WAAAyC,EACA,cAAAM,EACA,SAAAI,EACA,gBAAAxC,EACA,gBAAAM,EACA,eAAAlB,EACA,uBAAA4D,CACF,CACF,CACO,SAASE,GAAyBH,EAAmGI,EAA0CnE,EAA0CG,EAA+B,CAC7P,OAAOY,EAAoBf,EAAoB+D,EAAO,KAAK,IAAI,YAAY,EAAEI,CAAI,KAAG,eAAYJ,CAAM,EAAIA,EAAO,QAAU,UAAW,uBAAoBA,CAAM,EAAIA,EAAO,QAAU,OAAWA,EAAO,KAAK,IAAI,aAAc,kBAAmBA,EAAO,KAAOA,EAAO,KAAK,cAAgB,OAAW5D,CAAa,CACrT,CCrbA,IAAAiE,GAAwB,iBACxBA,GAAuC,iBAMvC,SAASC,GAA4BC,EAAwBC,EAA8BC,EAAgD,CACzI,IAAMC,EAAWH,EAAMC,CAAa,EAEhCE,GACFD,EAAOC,CAAQ,CAEnB,CAYO,SAASC,EAAoBC,EAQb,CACrB,OAAQ,QAASA,EAAKA,EAAG,IAAI,cAAgBA,EAAG,gBAAkBA,EAAG,SACvE,CAEA,SAASC,GAA+BN,EAA2BK,EAKhEH,EAAmD,CACpD,IAAMC,EAAWH,EAAMI,EAAoBC,CAAE,CAAC,EAE1CF,GACFD,EAAOC,CAAQ,CAEnB,CAEA,IAAMI,EAAgB,CAAC,EAChB,SAASC,GAAW,CACzB,YAAAC,EACA,WAAAC,EACA,cAAAC,EACA,QAAS,CACP,oBAAqBC,EACrB,OAAAC,EACA,uBAAAC,EACA,mBAAAC,CACF,EACA,cAAAC,EACA,OAAAC,CACF,EAOG,CACD,IAAMC,KAAgB,gBAAa,GAAGT,CAAW,gBAAgB,EAC3DU,KAAa,eAAY,CAC7B,KAAM,GAAGV,CAAW,WACpB,aAAeF,EACf,SAAU,CACR,kBAAmB,CACjB,QAAQa,EAAO,CACb,QAAS,CACP,cAAAnB,CACF,CACF,EAA2C,CACzC,OAAOmB,EAAMnB,CAAa,CAC5B,EAEA,WAAS,sBAA4C,CACvD,EACA,mBAAoB,CAClB,QAAQmB,EAAO,CACb,QAAS,CACP,cAAAnB,EACA,QAAAoB,CACF,CACF,EAEI,CACFtB,GAA4BqB,EAAOnB,EAAeE,GAAY,CAC5DA,EAAS,QAAO,iBAAcA,EAAS,KAAckB,EAAQ,OAAO,CAAC,CACvE,CAAC,CACH,EAEA,WAAS,sBAEN,CACL,CACF,EAEA,cAAcC,EAAS,CACrBA,EAAQ,QAAQZ,EAAW,QAAS,CAACU,EAAO,CAC1C,KAAAG,EACA,KAAM,CACJ,IAAAC,CACF,CACF,IAAM,CACJ,IAAMC,EAAYC,EAAcF,CAAG,EACnCJ,EAAMI,EAAI,aAAa,IAAM,CAC3B,uBACA,aAAcA,EAAI,YACpB,EACAzB,GAA4BqB,EAAOI,EAAI,cAAerB,GAAY,CAChEA,EAAS,iBACTA,EAAS,UAAYsB,GAAatB,EAAS,UAC3CA,EAAS,UACToB,EAAK,UAEDC,EAAI,eAAiB,SACvBrB,EAAS,aAAeqB,EAAI,cAG9BrB,EAAS,iBAAmBoB,EAAK,gBACnC,CAAC,CACH,CAAC,EAAE,QAAQb,EAAW,UAAW,CAACU,EAAO,CACvC,KAAAG,EACA,QAAAI,CACF,IAAM,CACJ5B,GAA4BqB,EAAOG,EAAK,IAAI,cAAepB,GAAY,CACrE,GAAIA,EAAS,YAAcoB,EAAK,WAAa,CAACG,EAAcH,EAAK,GAAG,EAAG,OACvE,GAAM,CACJ,MAAAK,CACF,EAAKhB,EAAYW,EAAK,IAAI,YAAY,EAGtC,GAFApB,EAAS,mBAELyB,EACF,GAAIzB,EAAS,OAAS,OAAW,CAC/B,GAAM,CACJ,mBAAA0B,EACA,IAAAL,EACA,cAAAM,EACA,UAAAC,CACF,EAAIR,EAKAS,KAAU,mBAAgB7B,EAAS,KAAM8B,GAEpCL,EAAMK,EAAmBN,EAAS,CACvC,IAAKH,EAAI,aACT,cAAAM,EACA,mBAAAD,EACA,UAAAE,CACF,CAAC,CACF,EACD5B,EAAS,KAAO6B,CAClB,MAEE7B,EAAS,KAAOwB,OAIlBxB,EAAS,KAAOS,EAAYW,EAAK,IAAI,YAAY,EAAE,mBAAqB,GAAOW,KAA0B,YAAQ/B,EAAS,IAAI,KAAI,aAASA,EAAS,IAAI,EAAIA,EAAS,KAAMwB,CAAO,EAAIA,EAGxL,OAAOxB,EAAS,MAChBA,EAAS,mBAAqBoB,EAAK,kBACrC,CAAC,CACH,CAAC,EAAE,QAAQb,EAAW,SAAU,CAACU,EAAO,CACtC,KAAM,CACJ,UAAAe,EACA,IAAAX,EACA,UAAAO,CACF,EACA,MAAAK,EACA,QAAAT,CACF,IAAM,CACJ5B,GAA4BqB,EAAOI,EAAI,cAAerB,GAAY,CAChE,GAAI,CAAAgC,EACG,CAEL,GAAIhC,EAAS,YAAc4B,EAAW,OACtC5B,EAAS,kBACTA,EAAS,MAASwB,GAAWS,CAC/B,CACF,CAAC,CACH,CAAC,EAAE,WAAWrB,EAAoB,CAACK,EAAOiB,IAAW,CACnD,GAAM,CACJ,QAAAC,CACF,EAAIxB,EAAuBuB,CAAM,EAEjC,OAAW,CAACE,EAAKC,CAAK,IAAK,OAAO,QAAQF,CAAO,GAE/CE,GAAO,sBAAoCA,GAAO,uBAChDpB,EAAMmB,CAAG,EAAIC,EAGnB,CAAC,CACH,CAEF,CAAC,EACKC,KAAgB,eAAY,CAChC,KAAM,GAAGhC,CAAW,aACpB,aAAeF,EACf,SAAU,CACR,qBAAsB,CACpB,QAAQa,EAAO,CACb,QAAAO,CACF,EAA8C,CAC5C,IAAMe,EAAWtC,EAAoBuB,CAAO,EAExCe,KAAYtB,GACd,OAAOA,EAAMsB,CAAQ,CAEzB,EAEA,WAAS,sBAA+C,CAC1D,CACF,EAEA,cAAcpB,EAAS,CACrBA,EAAQ,QAAQX,EAAc,QAAS,CAACS,EAAO,CAC7C,KAAAG,EACA,KAAM,CACJ,UAAAQ,EACA,IAAAP,EACA,iBAAAmB,CACF,CACF,IAAM,CACCnB,EAAI,QACTJ,EAAMhB,EAAoBmB,CAAI,CAAC,EAAI,CACjC,UAAAQ,EACA,iBACA,aAAcP,EAAI,aAClB,iBAAAmB,CACF,EACF,CAAC,EAAE,QAAQhC,EAAc,UAAW,CAACS,EAAO,CAC1C,QAAAO,EACA,KAAAJ,CACF,IAAM,CACCA,EAAK,IAAI,OACdjB,GAA+Bc,EAAOG,EAAMpB,GAAY,CAClDA,EAAS,YAAcoB,EAAK,YAChCpB,EAAS,mBACTA,EAAS,KAAOwB,EAChBxB,EAAS,mBAAqBoB,EAAK,mBACrC,CAAC,CACH,CAAC,EAAE,QAAQZ,EAAc,SAAU,CAACS,EAAO,CACzC,QAAAO,EACA,MAAAS,EACA,KAAAb,CACF,IAAM,CACCA,EAAK,IAAI,OACdjB,GAA+Bc,EAAOG,EAAMpB,GAAY,CAClDA,EAAS,YAAcoB,EAAK,YAChCpB,EAAS,kBACTA,EAAS,MAASwB,GAAWS,EAC/B,CAAC,CACH,CAAC,EAAE,WAAWrB,EAAoB,CAACK,EAAOiB,IAAW,CACnD,GAAM,CACJ,UAAAO,CACF,EAAI9B,EAAuBuB,CAAM,EAEjC,OAAW,CAACE,EAAKC,CAAK,IAAK,OAAO,QAAQI,CAAS,GAEhDJ,GAAO,sBAAoCA,GAAO,sBACnDD,IAAQC,GAAO,YACbpB,EAAMmB,CAAG,EAAIC,EAGnB,CAAC,CACH,CAEF,CAAC,EACKK,KAAoB,eAAY,CACpC,KAAM,GAAGpC,CAAW,gBACpB,aAAeF,EACf,SAAU,CACR,iBAAkB,CAChB,QAAQa,EAAOiB,EAGX,CACF,GAAM,CACJ,cAAApC,EACA,aAAA6C,CACF,EAAIT,EAAO,QAEX,QAAWU,KAAwB,OAAO,OAAO3B,CAAK,EACpD,QAAW4B,KAAmB,OAAO,OAAOD,CAAoB,EAAG,CACjE,IAAME,EAAUD,EAAgB,QAAQ/C,CAAa,EAEjDgD,IAAY,IACdD,EAAgB,OAAOC,EAAS,CAAC,CAErC,CAGF,OAAW,CACT,KAAAC,EACA,GAAA7C,CACF,IAAKyC,EAAc,CACjB,IAAMK,GAAqB/B,EAAM8B,CAAI,IAAM,CAAC,GAAG7C,GAAM,uBAAuB,IAAM,CAAC,EACzD8C,EAAkB,SAASlD,CAAa,GAGhEkD,EAAkB,KAAKlD,CAAa,CAExC,CACF,EAEA,WAAS,sBAGN,CACL,CACF,EAEA,cAAcqB,EAAS,CACrBA,EAAQ,QAAQH,EAAW,QAAQ,kBAAmB,CAACC,EAAO,CAC5D,QAAS,CACP,cAAAnB,CACF,CACF,IAAM,CACJ,QAAW8C,KAAwB,OAAO,OAAO3B,CAAK,EACpD,QAAW4B,KAAmB,OAAO,OAAOD,CAAoB,EAAG,CACjE,IAAME,EAAUD,EAAgB,QAAQ/C,CAAa,EAEjDgD,IAAY,IACdD,EAAgB,OAAOC,EAAS,CAAC,CAErC,CAEJ,CAAC,EAAE,WAAWlC,EAAoB,CAACK,EAAOiB,IAAW,CACnD,GAAM,CACJ,SAAAe,CACF,EAAItC,EAAuBuB,CAAM,EAEjC,OAAW,CAACa,EAAMG,CAAY,IAAK,OAAO,QAAQD,CAAQ,EACxD,OAAW,CAAC/C,EAAIiD,CAAS,IAAK,OAAO,QAAQD,CAAY,EAAG,CAC1D,IAAMF,GAAqB/B,EAAM8B,CAAI,IAAM,CAAC,GAAG7C,GAAM,uBAAuB,IAAM,CAAC,EAEnF,QAAWJ,KAAiBqD,EACAH,EAAkB,SAASlD,CAAa,GAGhEkD,EAAkB,KAAKlD,CAAa,CAG1C,CAEJ,CAAC,EAAE,cAAW,cAAQ,eAAYS,CAAU,KAAG,uBAAoBA,CAAU,CAAC,EAAG,CAACU,EAAOiB,IAAW,CAClG,IAAMS,EAAeS,GAAyBlB,EAAQ,eAAgBzB,EAAaI,CAAa,EAC1F,CACJ,cAAAf,CACF,EAAIoC,EAAO,KAAK,IAChBQ,EAAkB,aAAa,iBAAiBzB,EAAOyB,EAAkB,QAAQ,iBAAiB,CAChG,cAAA5C,EACA,aAAA6C,CACF,CAAC,CAAC,CACJ,CAAC,CACH,CAEF,CAAC,EAEKU,KAAoB,eAAY,CACpC,KAAM,GAAG/C,CAAW,iBACpB,aAAeF,EACf,SAAU,CACR,0BAA0BkD,EAAGC,EAIC,CAC9B,EAEA,uBAAuBD,EAAGC,EAEI,CAC9B,EAEA,+BAAgC,CAAC,CAEnC,CACF,CAAC,EACKC,KAA6B,eAAY,CAC7C,KAAM,GAAGlD,CAAW,yBACpB,aAAeF,EACf,SAAU,CACR,qBAAsB,CACpB,QAAQP,EAAOqC,EAAgC,CAC7C,SAAO,iBAAarC,EAAOqC,EAAO,OAAO,CAC3C,EAEA,WAAS,sBAA4B,CACvC,CACF,CACF,CAAC,EACKuB,KAAc,eAAY,CAC9B,KAAM,GAAGnD,CAAW,UACpB,aAAe,CACb,OAAQoD,GAAS,EACjB,QAASC,GAAkB,EAC3B,qBAAsB,GACtB,GAAG7C,CACL,EACA,SAAU,CACR,qBAAqBjB,EAAO,CAC1B,QAAA2B,CACF,EAA0B,CACxB3B,EAAM,qBAAuBA,EAAM,uBAAyB,YAAca,IAAWc,EAAU,WAAa,EAC9G,CAEF,EACA,cAAeL,GAAW,CACxBA,EAAQ,QAAQyC,EAAU/D,GAAS,CACjCA,EAAM,OAAS,EACjB,CAAC,EAAE,QAAQgE,EAAWhE,GAAS,CAC7BA,EAAM,OAAS,EACjB,CAAC,EAAE,QAAQiE,EAASjE,GAAS,CAC3BA,EAAM,QAAU,EAClB,CAAC,EAAE,QAAQkE,EAAalE,GAAS,CAC/BA,EAAM,QAAU,EAClB,CAAC,EAEA,WAAWe,EAAoBK,IAAU,CAAE,GAAGA,CAC/C,EAAE,CACJ,CACF,CAAC,EACK+C,KAAkB,mBAAgB,CACtC,QAAShD,EAAW,QACpB,UAAWsB,EAAc,QACzB,SAAUI,EAAkB,QAC5B,cAAec,EAA2B,QAC1C,OAAQC,EAAY,OACtB,CAAC,EAEKQ,EAAkC,CAACpE,EAAOqC,IAAW8B,EAAgBjD,EAAc,MAAMmB,CAAM,EAAI,OAAYrC,EAAOqC,CAAM,EAE5HgC,EAAU,CAAE,GAAGT,EAAY,QAC/B,GAAGzC,EAAW,QACd,GAAGqC,EAAkB,QACrB,GAAGG,EAA2B,QAC9B,GAAGlB,EAAc,QACjB,GAAGI,EAAkB,QACrB,cAAA3B,CACF,EACA,MAAO,CACL,QAAAkD,EACA,QAAAC,CACF,CACF,CCnbO,IAAMC,GAA2B,OAAO,IAAI,gBAAgB,EAgB7DC,GAAsC,CAC1C,sBACF,EAEMC,MAAsC,mBAAgBD,GAAiB,IAAM,CAAC,CAAC,EAC/EE,MAAyC,mBAAiBF,GAA2C,IAAM,CAAC,CAAC,EAC5G,SAASG,GAAoF,CAClG,mBAAAC,EACA,YAAAC,CACF,EAGG,CAGD,IAAMC,EAAsBC,GAAqBN,GAE3CO,EAAyBD,GAAqBL,GAEpD,MAAO,CACL,mBAAAO,EACA,sBAAAC,EACA,oBAAAC,EACA,yBAAAC,CACF,EAEA,SAASC,EAENC,EAAqC,CACtC,MAAO,CAAE,GAAGA,EACV,GAAGC,GAAsBD,EAAS,MAAM,CAC1C,CACF,CAEA,SAASE,EAAoBC,EAAsB,CAWjD,OAVcA,EAAUZ,CAAW,CAWrC,CAEA,SAASI,EAAmBS,EAAsBC,EAAyD,CACzG,OAAUC,GAAmB,CAC3B,IAAMC,EAAiBjB,EAAmB,CACxC,UAAAgB,EACA,mBAAAD,EACA,aAAAD,CACF,CAAC,EAKD,SAAO,kBAD0BE,IAAcrB,GAAYO,EAF9BC,GAAqBS,EAAoBT,CAAK,GAAG,UAAUc,CAAc,GAAKpB,GAG3DY,CAAgB,CAClE,CACF,CAEA,SAASH,GAAwB,CAC/B,OAASY,GAAM,CACb,IAAIC,EAEJ,OAAI,OAAOD,GAAO,SAChBC,EAAaC,EAAoBF,CAAE,GAAKvB,GAExCwB,EAAaD,KAMR,kBAD6BC,IAAexB,GAAYS,EAF/BD,GAAqBS,EAAoBT,CAAK,GAAG,YAAagB,CAAqB,GAAKrB,GAGrEW,CAAgB,CACrE,CACF,CAEA,SAASF,EAAoBJ,EAAkBkB,EAI5C,CACD,IAAMC,EAAWnB,EAAMF,CAAW,EAC5BsB,EAAe,IAAI,IAEzB,QAAWC,KAAOH,EAAK,IAAII,EAAoB,EAAG,CAChD,IAAMC,EAAWJ,EAAS,SAASE,EAAI,IAAI,EAE3C,GAAI,CAACE,EACH,SAGF,IAAIC,GAA2BH,EAAI,KAAO,OAC1CE,EAASF,EAAI,EAAE,EACfI,GAAQ,OAAO,OAAOF,CAAQ,CAAC,IAAM,CAAC,EAEtC,QAAWG,KAAcF,EACvBJ,EAAa,IAAIM,CAAU,CAE/B,CAEA,OAAOD,GAAQ,MAAM,KAAKL,EAAa,OAAO,CAAC,EAAE,IAAIO,GAAiB,CACpE,IAAMC,EAAgBT,EAAS,QAAQQ,CAAa,EACpD,OAAOC,EAAgB,CAAC,CACtB,cAAAD,EACA,aAAcC,EAAc,aAC5B,aAAcA,EAAc,YAC9B,CAAC,EAAI,CAAC,CACR,CAAC,CAAC,CACJ,CAEA,SAASvB,EAAmEL,EAAkB6B,EAAmE,CAC/J,OAAO,OAAO,OAAQ7B,EAAMF,CAAW,EAAE,OAA2B,EAAE,OAAQgC,GAExEA,GAAO,eAAiBD,GAAaC,EAAM,wBAAoC,EAAE,IAAIA,GAASA,EAAM,YAAY,CACxH,CACF,CCpKA,IAAMC,GAA0C,QAAU,IAAI,QAAY,OAC7DC,GAAqD,CAAC,CACjE,aAAAC,EACA,UAAAC,CACF,IAAM,CACJ,IAAIC,EAAa,GACXC,EAASL,IAAO,IAAIG,CAAS,EAEnC,GAAI,OAAOE,GAAW,SACpBD,EAAaC,MACR,CACL,IAAMC,EAAc,KAAK,UAAUH,EAAW,CAACI,EAAKC,OAAU,iBAAcA,CAAK,EAAI,OAAO,KAAKA,CAAK,EAAE,KAAK,EAAE,OAAY,CAACC,EAAKF,KAC/HE,EAAIF,CAAG,EAAKC,EAAcD,CAAG,EACtBE,GACN,CAAC,CAAC,EAAID,CAAK,KAEV,iBAAcL,CAAS,GACzBH,IAAO,IAAIG,EAAWG,CAAW,EAGnCF,EAAaE,CACf,CAGA,MAAO,GAAGJ,CAAY,IAAIE,CAAU,GACtC,EClBA,IAAAM,GAA+B,oBA+NxB,SAASC,MAAmEC,EAAsD,CACvI,OAAO,SAAuBC,EAAS,CACrC,IAAMC,KAAyB,mBAAgBC,GAA0BF,EAAQ,yBAAyBE,EAAQ,CAChH,YAAcF,EAAQ,aAAe,KACvC,CAAC,CAAC,EACIG,EAA4D,CAChE,YAAa,MACb,kBAAmB,GACnB,0BAA2B,GAC3B,eAAgB,GAChB,mBAAoB,GACpB,qBAAsB,UACtB,GAAGH,EACH,uBAAAC,EAEA,mBAAmBG,EAAc,CAC/B,IAAIC,EAA0BC,GAE9B,GAAI,uBAAwBF,EAAa,mBAAoB,CAC3D,IAAMG,EAAcH,EAAa,mBAAmB,mBAEpDC,EAA0BD,GAAgB,CACxC,IAAMI,EAAgBD,EAAYH,CAAY,EAE9C,OAAI,OAAOI,GAAkB,SAEpBA,EAIAF,GAA0B,CAAE,GAAGF,EACpC,UAAWI,CACb,CAAC,CAEL,CACF,MAAWR,EAAQ,qBACjBK,EAA0BL,EAAQ,oBAGpC,OAAOK,EAAwBD,CAAY,CAC7C,EAEA,SAAU,CAAC,GAAIJ,EAAQ,UAAY,CAAC,CAAE,CACxC,EACMS,EAA2C,CAC/C,oBAAqB,CAAC,EAEtB,MAAMC,EAAI,CAERA,EAAG,CACL,EAEA,UAAQ,UAAO,EACf,uBAAAT,EACA,sBAAoB,mBAAeC,GAAUD,EAAuBC,CAAM,GAAK,IAAI,CACrF,EACMS,EAAO,CACX,gBAAAC,EAEA,iBAAiB,CACf,YAAAC,EACA,UAAAC,CACF,EAAG,CACD,GAAID,EACF,QAAWE,KAAMF,EACVV,EAAoB,SAAU,SAAUY,CAAU,GAEpDZ,EAAoB,SAAmB,KAAKY,CAAE,EAKrD,GAAID,EACF,OAAW,CAACE,EAAcC,CAAiB,IAAK,OAAO,QAAQH,CAAS,EAClE,OAAOG,GAAsB,WAC/BA,EAAkBR,EAAQ,oBAAoBO,CAAY,CAAC,EAE3D,OAAO,OAAOP,EAAQ,oBAAoBO,CAAY,GAAK,CAAC,EAAGC,CAAiB,EAKtF,OAAON,CACT,CAEF,EACMO,EAAqBnB,EAAQ,IAAIoB,GAAKA,EAAE,KAAMR,EAAcR,EAA6BM,CAAO,CAAC,EAEvG,SAASG,EAAgBQ,EAAmD,CAC1E,IAAMC,EAAqBD,EAAO,UAAU,CAC1C,MAAOE,IAAM,CAAE,GAAGA,EAChB,YACF,GACA,SAAUA,IAAM,CAAE,GAAGA,EACnB,eACF,EACF,CAAC,EAED,OAAW,CAACN,EAAcO,CAAU,IAAK,OAAO,QAAQF,CAAkB,EAAG,CAC3E,GAAI,CAACD,EAAO,kBAAoBJ,KAAgBP,EAAQ,oBAAqB,CACvE,OAAO,QAAY,IAIvB,QACF,CAEAA,EAAQ,oBAAoBO,CAAY,EAAIO,EAE5C,QAAWJ,KAAKD,EACdC,EAAE,eAAeH,EAAcO,CAAU,CAE7C,CAEA,OAAQZ,CACV,CAEA,OAAOA,EAAI,gBAAgB,CACzB,UAAYX,EAAQ,SACtB,CAAC,CACH,CACF,CClWA,IAAAwB,GAAkE,4BAW3D,SAASC,IAAoE,CAClF,OAAO,UAAY,CACjB,MAAM,IAAI,SAA8C,GAAAC,wBAAwB,EAAE,CAAmG,CACvL,CACF,CCTA,SAASC,GAAcC,EAAuB,CAG5C,QAASC,KAAKD,EAEZ,MAAO,GAGT,MAAO,EACT,CAiBO,IAAME,GAAmC,WAAgB,IAAQ,EAC3DC,GAAsD,CAAC,CAClE,YAAAC,EACA,IAAAC,EACA,QAAAC,EACA,cAAAC,CACF,IAAM,CACJ,GAAM,CACJ,kBAAAC,EACA,uBAAAC,CACF,EAAIJ,EAAI,gBAER,SAASK,EAAgCC,EAAuB,CAC9D,IAAMC,EAAgBL,EAAc,qBAAqBI,CAAa,EACtE,MAAO,CAAC,CAACC,GAAiB,CAACC,GAAcD,CAAa,CACxD,CAEA,IAAME,EAAoD,CAAC,EAErDC,EAAwC,CAACC,EAAQC,EAAOV,IAAkB,CAC9E,GAAIE,EAAuB,MAAMO,CAAM,EAAG,CACxC,IAAME,EAAQD,EAAM,SAAS,EAAEb,CAAW,EACpC,CACJ,cAAAO,CACF,EAAIK,EAAO,QACXG,EAAkBR,EAAeO,EAAM,QAAQP,CAAa,GAAG,aAAcM,EAAOC,EAAM,MAAM,CAClG,CAEA,GAAIb,EAAI,KAAK,cAAc,MAAMW,CAAM,EACrC,OAAW,CAACI,EAAKC,CAAO,IAAK,OAAO,QAAQP,CAAsB,EAC5DO,GAAS,aAAaA,CAAO,EACjC,OAAOP,EAAuBM,CAAG,EAIrC,GAAId,EAAQ,mBAAmBU,CAAM,EAAG,CACtC,IAAME,EAAQD,EAAM,SAAS,EAAEb,CAAW,EACpC,CACJ,QAAAkB,CACF,EAAIhB,EAAQ,uBAAuBU,CAAM,EAEzC,OAAW,CAACL,EAAeY,CAAU,IAAK,OAAO,QAAQD,CAAO,EAI9DH,EAAmBR,EAAiCY,GAAY,aAAcN,EAAOC,EAAM,MAAM,CAErG,CACF,EAEA,SAASC,EAAkBR,EAA8Ba,EAAkCnB,EAAuBoB,EAA6B,CAE7I,IAAMC,EADsBpB,EAAQ,oBAAoBkB,CAAa,GACvB,mBAAqBC,EAAO,kBAE1E,GAAIC,IAAsB,IAExB,OAOF,IAAMC,EAAyB,KAAK,IAAI,EAAG,KAAK,IAAID,EAAmBxB,EAAgC,CAAC,EAExG,GAAI,CAACQ,EAAgCC,CAAa,EAAG,CACnD,IAAMiB,EAAiBd,EAAuBH,CAAa,EAEvDiB,GACF,aAAaA,CAAc,EAG7Bd,EAAuBH,CAAa,EAAI,WAAW,IAAM,CAClDD,EAAgCC,CAAa,GAChDN,EAAI,SAASG,EAAkB,CAC7B,cAAAG,CACF,CAAC,CAAC,EAGJ,OAAOG,EAAwBH,CAAa,CAC9C,EAAGgB,EAAyB,GAAI,CAClC,CACF,CAEA,OAAOZ,CACT,EC7GO,IAAMc,GAAyD,CAAC,CACrE,YAAAC,EACA,QAAAC,EACA,QAAS,CACP,oBAAAC,CACF,EACA,cAAAC,EACA,WAAAC,EACA,IAAAC,EACA,cAAAC,EACA,aAAAC,EACA,cAAAC,CACF,IAAM,CACJ,GAAM,CACJ,kBAAAC,CACF,EAAIJ,EAAI,gBACFK,KAAwB,cAAQ,eAAYP,CAAa,KAAG,uBAAoBA,CAAa,CAAC,EAC9FQ,KAAa,cAAQ,eAAYR,EAAeC,CAAU,KAAG,cAAWD,EAAeC,CAAU,CAAC,EACpGQ,EAAwD,CAAC,EAEvDC,EAAwC,CAACC,EAAQC,IAAU,CAC3DL,EAAsBI,CAAM,EAC9BE,EAAeC,GAAyBH,EAAQ,kBAAmBZ,EAAqBI,CAAa,EAAGS,CAAK,EACpGJ,EAAWG,CAAM,EAC1BE,EAAe,CAAC,EAAGD,CAAK,EACfV,EAAI,KAAK,eAAe,MAAMS,CAAM,GAC7CE,EAAeE,EAAoBJ,EAAO,QAAS,OAAW,OAAW,OAAW,OAAWR,CAAa,EAAGS,CAAK,CAExH,EAEA,SAASI,EAAmBC,EAA2D,CACrF,QAAWC,KAAOD,EAAM,QACtB,GAAIA,EAAM,QAAQC,CAAG,GAAG,mBAAgC,MAAO,GAGjE,QAAWA,KAAOD,EAAM,UACtB,GAAIA,EAAM,UAAUC,CAAG,GAAG,mBAAgC,MAAO,GAGnE,MAAO,EACT,CAEA,SAASL,EAAeM,EAAgDP,EAAyB,CAC/F,IAAMQ,EAAYR,EAAM,SAAS,EAC3BK,EAAQG,EAAUvB,CAAW,EAGnC,GAFAY,EAAwB,KAAK,GAAGU,CAAO,EAEnCF,EAAM,OAAO,uBAAyB,WAAaD,EAAmBC,CAAK,EAC7E,OAGF,IAAMI,EAAOZ,EAEb,GADAA,EAA0B,CAAC,EACvBY,EAAK,SAAW,EAAG,OACvB,IAAMC,EAAepB,EAAI,KAAK,oBAAoBkB,EAAWC,CAAI,EACjEvB,EAAQ,MAAM,IAAM,CAClB,IAAMyB,EAAc,MAAM,KAAKD,EAAa,OAAO,CAAC,EAEpD,OAAW,CACT,cAAAE,CACF,IAAKD,EAAa,CAChB,IAAME,EAAgBR,EAAM,QAAQO,CAAa,EAC3CE,EAAuBrB,EAAc,qBAAqBmB,CAAa,GAAK,CAAC,EAE/EC,IACEE,EAAgBD,CAAoB,IAAM,EAC5Cd,EAAM,SAASN,EAAkB,CAC/B,cAAgBkB,CAClB,CAAC,CAAC,EACOC,EAAc,0BACvBb,EAAM,SAASR,EAAaqB,EAAeD,CAAa,CAAC,EAG/D,CACF,CAAC,CACH,CAEA,OAAOd,CACT,ECnFO,IAAMkB,GAA8C,CAAC,CAC1D,YAAAC,EACA,WAAAC,EACA,IAAAC,EACA,aAAAC,EACA,cAAAC,CACF,IAAM,CACJ,IAAMC,EAID,CAAC,EAEAC,EAAwC,CAACC,EAAQC,IAAU,EAC3DN,EAAI,gBAAgB,0BAA0B,MAAMK,CAAM,GAAKL,EAAI,gBAAgB,uBAAuB,MAAMK,CAAM,IACxHE,EAAsBF,EAAO,QAASC,CAAK,GAGzCP,EAAW,QAAQ,MAAMM,CAAM,GAAKN,EAAW,SAAS,MAAMM,CAAM,GAAKA,EAAO,KAAK,YACvFE,EAAsBF,EAAO,KAAK,IAAKC,CAAK,GAG1CP,EAAW,UAAU,MAAMM,CAAM,GAAKN,EAAW,SAAS,MAAMM,CAAM,GAAK,CAACA,EAAO,KAAK,YAC1FG,EAAcH,EAAO,KAAK,IAAKC,CAAK,EAGlCN,EAAI,KAAK,cAAc,MAAMK,CAAM,GACrCI,EAAW,CAEf,EAEA,SAASD,EAAc,CACrB,cAAAE,CACF,EAA4BV,EAAuB,CAEjD,IAAMW,EADQX,EAAI,SAAS,EAAEF,CAAW,EACZ,QAAQY,CAAa,EAC3CE,EAAgBV,EAAc,qBAAqBQ,CAAa,EACtE,GAAI,CAACC,GAAiBA,EAAc,yBAAsC,OAC1E,IAAME,EAAwBC,EAA0BF,CAAa,EACrE,GAAI,CAAC,OAAO,SAASC,CAAqB,EAAG,OAC7C,IAAME,EAAcZ,EAAaO,CAAa,EAE1CK,GAAa,UACf,aAAaA,EAAY,OAAO,EAChCA,EAAY,QAAU,QAGxB,IAAMC,EAAoB,KAAK,IAAI,EAAIH,EACjCI,EAA+Cd,EAAaO,CAAa,EAAI,CACjF,kBAAAM,EACA,gBAAiBH,EACjB,QAAS,WAAW,IAAM,CACxBI,EAAiB,QAAU,OAC3BjB,EAAI,SAASC,EAAaU,EAAeD,CAAa,CAAC,CACzD,EAAGG,CAAqB,CAC1B,CACF,CAEA,SAASN,EAAsB,CAC7B,cAAAG,CACF,EAA4BV,EAAuB,CAEjD,IAAMW,EADQX,EAAI,SAAS,EAAEF,CAAW,EACZ,QAAQY,CAAa,EAC3CE,EAAgBV,EAAc,qBAAqBQ,CAAa,EAEtE,GAAI,CAACC,GAAiBA,EAAc,yBAClC,OAGF,IAAME,EAAwBC,EAA0BF,CAAa,EAErE,GAAI,CAAC,OAAO,SAASC,CAAqB,EAAG,CAC3CK,EAAkBR,CAAa,EAC/B,MACF,CAEA,IAAMK,EAAcZ,EAAaO,CAAa,EACxCM,EAAoB,KAAK,IAAI,EAAIH,GAEnC,CAACE,GAAeC,EAAoBD,EAAY,oBAClDP,EAAc,CACZ,cAAAE,CACF,EAAGV,CAAG,CAEV,CAEA,SAASkB,EAAkBC,EAAa,CACtC,IAAMC,EAAejB,EAAagB,CAAG,EAEjCC,GAAc,SAChB,aAAaA,EAAa,OAAO,EAGnC,OAAOjB,EAAagB,CAAG,CACzB,CAEA,SAASV,GAAa,CACpB,QAAWU,KAAO,OAAO,KAAKhB,CAAY,EACxCe,EAAkBC,CAAG,CAEzB,CAEA,SAASL,EAA0BO,EAA2B,CAAC,EAAG,CAChE,IAAIR,EAAwB,OAAO,kBAEnC,QAASM,KAAOE,EACRA,EAAYF,CAAG,EAAE,kBACrBN,EAAwB,KAAK,IAAIQ,EAAYF,CAAG,EAAE,gBAAkBN,CAAqB,GAI7F,OAAOA,CACT,CAEA,OAAOT,CACT,ECjHO,IAAMkB,GAAkD,CAAC,CAC9D,YAAAC,EACA,QAAAC,EACA,IAAAC,EACA,aAAAC,EACA,cAAAC,CACF,IAAM,CACJ,GAAM,CACJ,kBAAAC,CACF,EAAIH,EAAI,gBAEFI,EAAwC,CAACC,EAAQC,IAAU,CAC3DC,EAAQ,MAAMF,CAAM,GACtBG,EAAoBF,EAAO,gBAAgB,EAGzCG,EAAS,MAAMJ,CAAM,GACvBG,EAAoBF,EAAO,oBAAoB,CAEnD,EAEA,SAASE,EAAoBR,EAAuBU,EAA+C,CACjG,IAAMC,EAAQX,EAAI,SAAS,EAAEF,CAAW,EAClCc,EAAUD,EAAM,QAChBE,EAAgBX,EAAc,qBACpCH,EAAQ,MAAM,IAAM,CAClB,QAAWe,KAAiB,OAAO,KAAKD,CAAa,EAAG,CACtD,IAAME,EAAgBH,EAAQE,CAAa,EACrCE,EAAuBH,EAAcC,CAAa,EACxD,GAAI,CAACE,GAAwB,CAACD,EAAe,UACvB,OAAO,OAAOC,CAAoB,EAAE,KAAKC,GAAOA,EAAIP,CAAI,IAAM,EAAI,GAAK,OAAO,OAAOM,CAAoB,EAAE,MAAMC,GAAOA,EAAIP,CAAI,IAAM,MAAS,GAAKC,EAAM,OAAOD,CAAI,KAGrLQ,EAAgBF,CAAoB,IAAM,EAC5ChB,EAAI,SAASG,EAAkB,CAC7B,cAAgBW,CAClB,CAAC,CAAC,EACOC,EAAc,0BACvBf,EAAI,SAASC,EAAac,EAAeD,CAAa,CAAC,EAG7D,CACF,CAAC,CACH,CAEA,OAAOV,CACT,EC8CA,IAAMe,GAAsB,IAAI,MAAM,kDAAkD,EAG3EC,GAAqD,CAAC,CACjE,IAAAC,EACA,YAAAC,EACA,QAAAC,EACA,WAAAC,EACA,cAAAC,EACA,cAAAC,CACF,IAAM,CACJ,IAAMC,KAAe,sBAAmBH,CAAU,EAC5CI,KAAkB,sBAAmBH,CAAa,EAClDI,KAAmB,eAAYL,EAAYC,CAAa,EAQxDK,EAA+C,CAAC,EAEhDC,EAAwC,CAACC,EAAQC,EAAOC,IAAgB,CAC5E,IAAMC,EAAWC,EAAYJ,CAAM,EAEnC,GAAIR,EAAW,QAAQ,MAAMQ,CAAM,EAAG,CACpC,IAAMK,EAAWH,EAAYZ,CAAW,EAAE,QAAQa,CAAQ,EACpDG,EAAQL,EAAM,SAAS,EAAEX,CAAW,EAAE,QAAQa,CAAQ,EAExD,CAACE,GAAYC,GACfC,EAAaP,EAAO,KAAK,IAAI,aAAcA,EAAO,KAAK,IAAI,aAAcG,EAAUF,EAAOD,EAAO,KAAK,SAAS,CAEnH,SAAWP,EAAc,QAAQ,MAAMO,CAAM,EAC7BC,EAAM,SAAS,EAAEX,CAAW,EAAE,UAAUa,CAAQ,GAG5DI,EAAaP,EAAO,KAAK,IAAI,aAAcA,EAAO,KAAK,IAAI,aAAcG,EAAUF,EAAOD,EAAO,KAAK,SAAS,UAExGH,EAAiBG,CAAM,EAAG,CACnC,IAAMQ,EAAYV,EAAaK,CAAQ,EAEnCK,GAAW,gBACbA,EAAU,cAAc,CACtB,KAAMR,EAAO,QACb,KAAMA,EAAO,KAAK,aACpB,CAAC,EACD,OAAOQ,EAAU,cAErB,SAAWnB,EAAI,gBAAgB,kBAAkB,MAAMW,CAAM,GAAKX,EAAI,gBAAgB,qBAAqB,MAAMW,CAAM,EAAG,CACxH,IAAMQ,EAAYV,EAAaK,CAAQ,EAEnCK,IACF,OAAOV,EAAaK,CAAQ,EAC5BK,EAAU,kBAAkB,EAEhC,SAAWnB,EAAI,KAAK,cAAc,MAAMW,CAAM,EAC5C,OAAW,CAACG,EAAUK,CAAS,IAAK,OAAO,QAAQV,CAAY,EAC7D,OAAOA,EAAaK,CAAQ,EAC5BK,EAAU,kBAAkB,CAGlC,EAEA,SAASJ,EAAYJ,EAAa,CAChC,OAAIL,EAAaK,CAAM,EAAUA,EAAO,KAAK,IAAI,cAC7CJ,EAAgBI,CAAM,EAAUA,EAAO,KAAK,UAC5CX,EAAI,gBAAgB,kBAAkB,MAAMW,CAAM,EAAUA,EAAO,QAAQ,cAC3EX,EAAI,gBAAgB,qBAAqB,MAAMW,CAAM,EAAUS,EAAoBT,EAAO,OAAO,EAC9F,EACT,CAEA,SAASO,EAAaG,EAAsBC,EAAmBC,EAAuBX,EAAyBY,EAAmB,CAChI,IAAMC,EAAqBvB,EAAQ,oBAAoBmB,CAAY,EAC7DK,EAAoBD,GAAoB,kBAC9C,GAAI,CAACC,EAAmB,OACxB,IAAIP,EAAa,CAAC,EACZQ,EAAoB,IAAI,QAAcC,GAAW,CACrDT,EAAU,kBAAoBS,CAChC,CAAC,EACKC,EAG0B,QAAQ,KAAK,CAAC,IAAI,QAG/CD,GAAW,CACZT,EAAU,cAAgBS,CAC5B,CAAC,EAAGD,EAAkB,KAAK,IAAM,CAC/B,MAAM7B,EACR,CAAC,CAAC,CAAC,EAGH+B,EAAgB,MAAM,IAAM,CAAC,CAAC,EAC9BpB,EAAac,CAAa,EAAIJ,EAC9B,IAAMW,EAAY9B,EAAI,UAAUqB,CAAY,EAAU,OAAOI,EAAmB,eAAgCH,EAAeC,CAAa,EACtIQ,EAAQnB,EAAM,SAAS,CAACoB,EAAGC,EAAIF,IAAUA,CAAK,EAC9CG,EAAe,CAAE,GAAGtB,EACxB,cAAe,IAAMkB,EAASlB,EAAM,SAAS,CAAC,EAC9C,UAAAY,EACA,MAAAO,EACA,iBAAoBN,EAAmB,eAAiCU,GAA8BvB,EAAM,SAASZ,EAAI,KAAK,gBAAiBqB,EAAwBC,EAAca,CAAY,CAAC,EAAI,OACtM,gBAAAN,EACA,kBAAAF,CACF,EACMS,EAAiBV,EAAkBJ,EAAcY,CAAY,EAEnE,QAAQ,QAAQE,CAAc,EAAE,MAAMC,GAAK,CACzC,GAAIA,IAAMvC,GACV,MAAMuC,CACR,CAAC,CACH,CAEA,OAAO3B,CACT,EChEO,IAAM4B,GAAqD,CAAC,CACjE,IAAAC,EACA,QAAAC,EACA,WAAAC,EACA,cAAAC,CACF,IAAM,CACJ,IAAMC,KAAiB,aAAUF,EAAYC,CAAa,EACpDE,KAAkB,cAAWH,EAAYC,CAAa,EACtDG,KAAoB,eAAYJ,EAAYC,CAAa,EAQzDI,EAA+C,CAAC,EA+DtD,MA7D8C,CAACC,EAAQC,IAAU,CAC/D,GAAIL,EAAeI,CAAM,EAAG,CAC1B,GAAM,CACJ,UAAAE,EACA,IAAK,CACH,aAAAC,EACA,aAAAC,CACF,CACF,EAAIJ,EAAO,KACLK,EAAqBZ,EAAQ,oBAAoBU,CAAY,EAC7DG,EAAiBD,GAAoB,eAE3C,GAAIC,EAAgB,CAClB,IAAMC,EAAa,CAAC,EACdC,EAAiB,IAAK,QAGW,CAACC,EAASC,IAAW,CAC1DH,EAAU,QAAUE,EACpBF,EAAU,OAASG,CACrB,CAAC,EAGDF,EAAe,MAAM,IAAM,CAAC,CAAC,EAC7BT,EAAaG,CAAS,EAAIK,EAC1B,IAAMI,EAAYnB,EAAI,UAAUW,CAAY,EAAU,OAAOE,EAAmB,eAAgCD,EAAeF,CAAS,EAClIU,EAAQX,EAAM,SAAS,CAACY,EAAGC,EAAIF,IAAUA,CAAK,EAC9CG,EAAe,CAAE,GAAGd,EACxB,cAAe,IAAMU,EAASV,EAAM,SAAS,CAAC,EAC9C,UAAAC,EACA,MAAAU,EACA,iBAAoBP,EAAmB,eAAiCW,GAA8Bf,EAAM,SAAST,EAAI,KAAK,gBAAiBW,EAAwBC,EAAcY,CAAY,CAAC,EAAI,OACtM,eAAAR,CACF,EACAF,EAAeF,EAAcW,CAAY,CAC3C,CACF,SAAWjB,EAAkBE,CAAM,EAAG,CACpC,GAAM,CACJ,UAAAE,EACA,cAAAe,CACF,EAAIjB,EAAO,KACXD,EAAaG,CAAS,GAAG,QAAQ,CAC/B,KAAMF,EAAO,QACb,KAAMiB,CACR,CAAC,EACD,OAAOlB,EAAaG,CAAS,CAC/B,SAAWL,EAAgBG,CAAM,EAAG,CAClC,GAAM,CACJ,UAAAE,EACA,kBAAAgB,EACA,cAAAD,CACF,EAAIjB,EAAO,KACXD,EAAaG,CAAS,GAAG,OAAO,CAC9B,MAAOF,EAAO,SAAWA,EAAO,MAChC,iBAAkB,CAACkB,EACnB,KAAOD,CACT,CAAC,EACD,OAAOlB,EAAaG,CAAS,CAC/B,CACF,CAGF,EClOO,IAAMiB,GAA+C,CAAC,CAC3D,IAAAC,EACA,QAAS,CACP,OAAAC,CACF,EACA,YAAAC,CACF,IACS,CAACC,EAAQC,IAAU,CACpBJ,EAAI,KAAK,cAAc,MAAMG,CAAM,GAErCC,EAAM,SAASJ,EAAI,gBAAgB,qBAAqBC,CAAM,CAAC,EAG7D,OAAO,QAAY,GAOzB,ECnBF,IAAAI,GAAmC,iBAG5B,IAAMC,GAAoI,CAAC,CAChJ,IAAAC,EACA,WAAAC,EACA,cAAAC,CACF,IAAM,CACJ,IAAMC,EAAsB,GAAGH,EAAI,WAAW,iBAC1CI,EAA6C,KAC7CC,EAA+D,KAC7D,CACJ,0BAAAC,EACA,uBAAAC,CACF,EAAIP,EAAI,gBAGFQ,EAA8B,CAACC,EAAiCC,IAAmB,CACvF,GAAIJ,EAA0B,MAAMI,CAAM,EAAG,CAC3C,GAAM,CACJ,cAAAC,EACA,UAAAC,EACA,QAAAC,CACF,EAAIH,EAAO,QAEX,OAAID,IAAeE,CAAa,IAAIC,CAAS,IAC3CH,EAAaE,CAAa,EAAGC,CAAS,EAAIC,GAGrC,EACT,CAEA,GAAIN,EAAuB,MAAMG,CAAM,EAAG,CACxC,GAAM,CACJ,cAAAC,EACA,UAAAC,CACF,EAAIF,EAAO,QAEX,OAAID,EAAaE,CAAa,GAC5B,OAAOF,EAAaE,CAAa,EAAGC,CAAS,EAGxC,EACT,CAEA,GAAIZ,EAAI,gBAAgB,kBAAkB,MAAMU,CAAM,EACpD,cAAOD,EAAaC,EAAO,QAAQ,aAAa,EACzC,GAGT,GAAIT,EAAW,QAAQ,MAAMS,CAAM,EAAG,CACpC,GAAM,CACJ,KAAM,CACJ,IAAAI,EACA,UAAAF,CACF,CACF,EAAIF,EACEK,EAAWN,EAAaK,EAAI,aAAa,IAAM,CAAC,EACtD,OAAAC,EAAS,GAAGH,CAAS,UAAU,EAAI,CAAC,EAEhCE,EAAI,YACNC,EAASH,CAAS,EAAIE,EAAI,qBAAuBC,EAASH,CAAS,GAAK,CAAC,GAGpE,EACT,CAEA,IAAII,EAAU,GAEd,GAAIf,EAAW,UAAU,MAAMS,CAAM,GAAKT,EAAW,SAAS,MAAMS,CAAM,EAAG,CAC3E,IAAMO,EAAQR,EAAaC,EAAO,KAAK,IAAI,aAAa,GAAK,CAAC,EACxDQ,EAAM,GAAGR,EAAO,KAAK,SAAS,WACpCM,IAAY,CAAC,CAACC,EAAMC,CAAG,EACvB,OAAOD,EAAMC,CAAG,CAClB,CAEA,GAAIjB,EAAW,SAAS,MAAMS,CAAM,EAAG,CACrC,GAAM,CACJ,KAAM,CACJ,UAAAS,EACA,IAAAL,EACA,UAAAF,CACF,CACF,EAAIF,EAEJ,GAAIS,GAAaL,EAAI,UAAW,CAC9B,IAAMC,EAAWN,EAAaK,EAAI,aAAa,IAAM,CAAC,EACtDC,EAASH,CAAS,EAAIE,EAAI,qBAAuBC,EAASH,CAAS,GAAK,CAAC,EACzEI,EAAU,EACZ,CACF,CAEA,OAAOA,CACT,EAEMI,EAAmB,IAAMlB,EAAc,qBAavCmB,EAA+C,CACnD,iBAAAD,EACA,qBAb4BT,GAA0B,CAEtD,IAAMW,EADgBF,EAAiB,EACQT,CAAa,GAAK,CAAC,EAClE,OAAOY,EAAgBD,CAAwB,CACjD,EAUE,oBAR0B,CAACX,EAAuBC,IAE3C,CAAC,CADcQ,EAAiB,IACdT,CAAa,IAAIC,CAAS,CAOrD,EACA,MAAO,CAACF,EAAQc,IAAoF,CAMlG,GALKpB,IAEHA,EAAwB,KAAK,MAAM,KAAK,UAAUF,EAAc,oBAAoB,CAAC,GAGnFF,EAAI,KAAK,cAAc,MAAMU,CAAM,EACrC,OAAAN,EAAwBF,EAAc,qBAAuB,CAAC,EAC9DG,EAAkB,KACX,CAAC,GAAM,EAAK,EAOrB,GAAIL,EAAI,gBAAgB,8BAA8B,MAAMU,CAAM,EAChE,MAAO,CAAC,GAAOW,CAAqB,EAItC,IAAMI,EAAYjB,EAA4BN,EAAc,qBAAsBQ,CAAM,EACpFgB,EAAuB,GAE3B,GAAID,EAAW,CACRpB,IAMHA,EAAkB,WAAW,IAAM,CAEjC,IAAMsB,EAAsC,KAAK,MAAM,KAAK,UAAUzB,EAAc,oBAAoB,CAAC,EAEnG,CAAC,CAAE0B,CAAO,KAAI,uBAAmBxB,EAAuB,IAAMuB,CAAgB,EAEpFH,EAAM,KAAKxB,EAAI,gBAAgB,qBAAqB4B,CAAO,CAAC,EAE5DxB,EAAwBuB,EACxBtB,EAAkB,IACpB,EAAG,GAAG,GAGR,IAAMwB,EAA4B,OAAOnB,EAAO,MAAQ,UAAY,CAAC,CAACA,EAAO,KAAK,WAAWP,CAAmB,EAC1G2B,EAAiC7B,EAAW,SAAS,MAAMS,CAAM,GAAKA,EAAO,KAAK,WAAa,CAAC,CAACA,EAAO,KAAK,IAAI,UACvHgB,EAAuB,CAACG,GAA6B,CAACC,CACxD,CAEA,MAAO,CAACJ,EAAsB,EAAK,CACrC,CACF,ECxJO,SAASK,GAA8GC,EAAiE,CAC7L,GAAM,CACJ,YAAAC,EACA,WAAAC,EACA,IAAAC,EACA,QAAAC,CACF,EAAIJ,EACE,CACJ,OAAAK,CACF,EAAID,EACEE,EAAU,CACd,kBAAgB,gBAA6D,GAAGL,CAAW,iBAAiB,CAC9G,EAEMM,EAAwBC,GAAmBA,EAAO,KAAK,WAAW,GAAGP,CAAW,GAAG,EAEnFQ,EAA4C,CAACC,GAAsBC,GAA6BC,GAAgCC,GAAqBC,GAA4BC,EAA0B,EA2DjN,MAAO,CACL,WA1DsHC,GAAS,CAC/H,IAAIC,EAAc,GAIZC,EAAc,CAAE,GAAKlB,EACzB,cAJ2C,CAC3C,qBAAsB,CAAC,CACzB,EAGE,aAAAmB,EACA,qBAAAZ,CACF,EACMa,EAAWX,EAAgB,IAAIY,GAASA,EAAMH,CAAW,CAAC,EAC1DI,EAAwBC,GAA2BL,CAAW,EAC9DM,EAAsBC,GAAwBP,CAAW,EAC/D,OAAOQ,GACElB,GAAU,CACf,GAAI,IAAC,YAASA,CAAM,EAClB,OAAOkB,EAAKlB,CAAM,EAGfS,IACHA,EAAc,GAEdD,EAAM,SAASb,EAAI,gBAAgB,qBAAqBE,CAAM,CAAC,GAGjE,IAAMsB,EAAgB,CAAE,GAAGX,EACzB,KAAAU,CACF,EACME,EAAcZ,EAAM,SAAS,EAC7B,CAACa,EAAsBC,CAAmB,EAAIR,EAAsBd,EAAQmB,EAAeC,CAAW,EACxGG,EAQJ,GANIF,EACFE,EAAML,EAAKlB,CAAM,EAEjBuB,EAAMD,EAGFd,EAAM,SAAS,EAAEf,CAAW,IAGhCuB,EAAoBhB,EAAQmB,EAAeC,CAAW,EAElDrB,EAAqBC,CAAM,GAAKJ,EAAQ,mBAAmBI,CAAM,GAGnE,QAASwB,KAAWZ,EAClBY,EAAQxB,EAAQmB,EAAeC,CAAW,EAKhD,OAAOG,CACT,CAEJ,EAIE,QAAAzB,CACF,EAEA,SAASa,EAAac,EAElBC,EAAuBC,EAAmC,CAAC,EAAG,CAChE,OAAOjC,EAAW,CAChB,KAAM,QACN,aAAc+B,EAAc,aAC5B,aAAcA,EAAc,aAC5B,UAAW,GACX,aAAc,GACd,cAAgBC,EAChB,GAAGC,CACL,CAAC,CACH,CACF,CCvGO,SAASC,EAA6BC,KAAcC,EAAqC,CAC9F,OAAO,OAAO,OAAOD,EAAQ,GAAGC,CAAI,CACtC,CCiBA,IAAAC,GAA8B,iBAejBC,GAAgC,OAAO,EAmTvCC,GAAa,KAA2B,CACnD,KAAMD,GAEN,KAAKE,EAAK,CACR,UAAAC,EACA,SAAAC,EACA,YAAAC,EACA,mBAAAC,EACA,kBAAAC,EACA,0BAAAC,EACA,eAAAC,EACA,mBAAAC,EACA,qBAAAC,CACF,EAAGC,EAAS,IACV,kBAAc,EAGd,IAAMC,EAAgCC,IAChC,OAAO,QAAY,IAMhBA,GAGT,OAAO,OAAOZ,EAAK,CACjB,YAAAG,EACA,UAAW,CAAC,EACZ,gBAAiB,CACf,SAAAU,EACA,UAAAC,EACA,QAAAC,EACA,YAAAC,CACF,EACA,KAAM,CAAC,CACT,CAAC,EACD,GAAM,CACJ,WAAAC,EACA,cAAAC,EACA,eAAAC,EACA,gBAAAC,EACA,gBAAAC,EACA,SAAAC,EACA,uBAAAC,CACF,EAAIC,GAAY,CACd,UAAAvB,EACA,YAAAE,EACA,QAAAO,EACA,IAAAV,EACA,mBAAAI,EACA,cAAAO,CACF,CAAC,EACK,CACJ,QAAAc,EACA,QAASC,CACX,EAAIC,GAAW,CACb,QAAAjB,EACA,WAAAO,EACA,cAAAC,EACA,YAAAf,EACA,cAAAQ,EACA,OAAQ,CACN,eAAAJ,EACA,mBAAAC,EACA,0BAAAF,EACA,kBAAAD,EACA,YAAAF,EACA,qBAAAM,CACF,CACF,CAAC,EACDmB,EAAW5B,EAAI,KAAM,CACnB,eAAAmB,EACA,gBAAAC,EACA,gBAAAC,EACA,SAAAC,EACA,cAAeI,EAAa,aAC9B,CAAC,EACDE,EAAW5B,EAAI,gBAAiB0B,CAAY,EAC5C,GAAM,CACJ,WAAAG,EACA,QAASC,CACX,EAAIC,GAAgB,CAClB,YAAA5B,EACA,QAAAO,EACA,WAAAO,EACA,cAAAC,EACA,IAAAlB,EACA,cAAAW,CACF,CAAC,EACDiB,EAAW5B,EAAI,KAAM8B,CAAiB,EACtCF,EAAW5B,EAAK,CACd,QAAUyB,EACV,WAAAI,CACF,CAAC,EACD,GAAM,CACJ,mBAAAG,EACA,sBAAAC,EACA,oBAAAC,EACA,yBAAAC,CACF,EAAIC,GAAe,CACjB,mBAAqBhC,EACrB,YAAAD,CACF,CAAC,EACDyB,EAAW5B,EAAI,KAAM,CACnB,oBAAAkC,EACA,yBAAAC,CACF,CAAC,EACD,GAAM,CACJ,mBAAAE,EACA,sBAAAC,EACA,wBAAAC,EACA,yBAAAC,EACA,uBAAAC,EACA,qBAAAC,CACF,EAAIC,GAAc,CAChB,WAAA1B,EACA,cAAAC,EACA,IAAAlB,EACA,mBAAqBI,EACrB,QAAAM,CACF,CAAC,EACD,OAAAkB,EAAW5B,EAAI,KAAM,CACnB,wBAAAuC,EACA,yBAAAC,EACA,qBAAAE,EACA,uBAAAD,CACF,CAAC,EACM,CACL,KAAM3C,GAEN,eAAe8C,EAAcC,EAAY,CACvC,IAAMC,EAAW9C,EACjB8C,EAAO,UAAUF,CAAY,IAAO,CAAC,EAEjCG,GAAkBF,CAAU,EAC9BjB,EAAWkB,EAAO,UAAUF,CAAY,EAAG,CACzC,KAAMA,EACN,OAAQZ,EAAmBY,EAAcC,CAAU,EACnD,SAAUR,EAAmBO,EAAcC,CAAU,CACvD,EAAGtB,EAAuBN,EAAY2B,CAAY,CAAC,EAC1CI,GAAqBH,CAAU,GACxCjB,EAAWkB,EAAO,UAAUF,CAAY,EAAG,CACzC,KAAMA,EACN,OAAQX,EAAsB,EAC9B,SAAUK,EAAsBM,CAAY,CAC9C,EAAGrB,EAAuBL,EAAe0B,CAAY,CAAC,CAE1D,CAEF,CACF,CAEF,GCjfA,IAAMK,GAA2BC,GAAeC,GAAW,CAAC", "names": ["query_exports", "__export", "QueryStatus", "buildCreateApi", "copyWithStructuralSharing", "coreModule", "coreModuleName", "createApi", "defaultSerializeQueryArgs", "fakeBase<PERSON><PERSON>y", "fetchBase<PERSON>uery", "retry", "setupListeners", "skipToken", "__toCommonJS", "QueryStatus", "getRequestStatusFlags", "status", "isAbsoluteUrl", "url", "withoutTrailingSlash", "url", "withoutLeadingSlash", "joinUrls", "base", "isAbsoluteUrl", "delimiter", "flatten", "arr", "isOnline", "isDocumentVisible", "import_toolkit", "isPlainObject", "copyWithStructuralSharing", "oldObj", "newObj", "newKeys", "oldKeys", "isSameObject", "mergeObj", "key", "defaultFetchFn", "args", "defaultValidateStatus", "response", "defaultIsJsonContentType", "headers", "stripUndefined", "obj", "copy", "k", "v", "fetchBase<PERSON>uery", "baseUrl", "prepareHeaders", "x", "fetchFn", "paramsSerializer", "isJsonContentType", "jsonContentType", "jsonReplacer", "defaultTimeout", "globalResponseHandler", "globalValidateStatus", "baseFetchOptions", "arg", "api", "signal", "getState", "extra", "endpoint", "forced", "type", "meta", "url", "params", "response<PERSON><PERSON>ler", "validateStatus", "timeout", "rest", "config", "isJsonifiable", "body", "divider", "query", "joinUrls", "request", "timedOut", "timeoutId", "e", "responseClone", "resultData", "responseText", "handleResponseError", "handleResponse", "r", "text", "HandledError", "value", "meta", "defaultBackoff", "attempt", "maxRetries", "attempts", "timeout", "resolve", "res", "fail", "HandledError", "EMPTY_OPTIONS", "retry<PERSON><PERSON><PERSON><PERSON><PERSON>", "base<PERSON><PERSON>y", "defaultOptions", "args", "api", "extraOptions", "possibleMaxRetries", "x", "options", "_", "__", "retry", "result", "e", "onFocus", "onFocusLost", "onOnline", "onOffline", "initialized", "setupListeners", "dispatch", "customHandler", "defaultHandler", "handleFocus", "handleFocusLost", "handleOnline", "handleOffline", "handleVisibilityChange", "isQueryDefinition", "isMutationDefinition", "calculateProvidedBy", "description", "result", "error", "queryArg", "meta", "assertTagTypes", "isFunction", "expandTagDescription", "t", "import_toolkit", "isNot<PERSON><PERSON>ish", "v", "countObjectKeys", "obj", "count", "_key", "forceQueryFnSymbol", "isUpsert<PERSON><PERSON>y", "arg", "buildInitiate", "serializeQueryArgs", "queryThunk", "mutationThunk", "api", "context", "runningQueries", "runningMutations", "unsubscribeQueryResult", "removeMutationResult", "updateSubscriptionOptions", "buildInitiateQuery", "buildInitiateMutation", "getRunningQueryThunk", "getRunningMutationThunk", "getRunningQueriesThunk", "getRunningMutationsThunk", "endpointName", "queryArgs", "dispatch", "endpointDefinition", "query<PERSON><PERSON><PERSON><PERSON>", "_endpointName", "fixedCacheKeyOrRequestId", "isNot<PERSON><PERSON>ish", "middlewareWarning", "queryAction", "subscribe", "forceRefetch", "subscriptionOptions", "forceQueryFn", "getState", "thunk", "selector", "thunkResult", "stateAfter", "requestId", "abort", "skippedSynchronously", "<PERSON><PERSON><PERSON><PERSON>", "selectFromState", "statePromise", "result", "options", "running", "countObjectKeys", "track", "fixedCacheKey", "unwrap", "returnValuePromise", "data", "error", "reset", "ret", "import_immer", "defaultTransformResponse", "baseQueryReturnValue", "buildThunks", "reducerPath", "base<PERSON><PERSON>y", "endpointDefinitions", "serializeQueryArgs", "api", "assertTagType", "patchQueryData", "endpointName", "args", "patches", "updateProvided", "dispatch", "getState", "endpointDefinition", "query<PERSON><PERSON><PERSON><PERSON>", "newValue", "providedTags", "calculateProvidedBy", "updateQueryData", "updateRecipe", "currentState", "ret", "value", "inversePatches", "upsertQueryData", "forceQueryFnSymbol", "executeEndpoint", "arg", "signal", "abort", "rejectWithValue", "fulfillWithValue", "extra", "transformResponse", "result", "baseQueryApi", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "forceQueryFn", "HandledError", "error", "catched<PERSON><PERSON><PERSON>", "transformErrorResponse", "e", "state", "requestState", "baseFetchOnMountOrArgChange", "fulfilledVal", "refetchVal", "queryThunk", "query<PERSON>hunk<PERSON><PERSON>s", "currentArg", "previousArg", "isUpsert<PERSON><PERSON>y", "isQueryDefinition", "mutationThunk", "hasTheForce", "options", "hasMaxAge", "prefetch", "force", "maxAge", "queryAction", "latestStateValue", "lastFulfilledTs", "matchesEndpoint", "action", "buildMatchThunkActions", "thunk", "calculateProvidedByThunk", "type", "import_immer", "updateQuerySubstateIfExists", "state", "query<PERSON><PERSON><PERSON><PERSON>", "update", "substate", "getMutationCacheKey", "id", "updateMutationSubstateIfExists", "initialState", "buildSlice", "reducerPath", "queryThunk", "mutationThunk", "definitions", "apiUid", "extractRehydrationInfo", "hasRehydrationInfo", "assertTagType", "config", "resetApiState", "querySlice", "draft", "patches", "builder", "meta", "arg", "upserting", "isUpsert<PERSON><PERSON>y", "payload", "merge", "fulfilledTimeStamp", "baseQueryMeta", "requestId", "newData", "draftSubstateData", "copyWithStructuralSharing", "condition", "error", "action", "queries", "key", "entry", "mutationSlice", "cache<PERSON>ey", "startedTimeStamp", "mutations", "invalidationSlice", "providedTags", "tagTypeSubscriptions", "idSubscriptions", "foundAt", "type", "subscribedQueries", "provided", "incomingTags", "cacheKeys", "calculateProvidedByThunk", "subscriptionSlice", "d", "a", "internalSubscriptionsSlice", "configSlice", "isOnline", "isDocumentVisible", "onOnline", "onOffline", "onFocus", "onFocusLost", "combinedReducer", "reducer", "actions", "skipToken", "initialSubState", "defaultQuerySubState", "defaultMutationSubState", "buildSelectors", "serializeQueryArgs", "reducerPath", "selectSkippedQuery", "state", "selectSkippedMutation", "buildQuerySelector", "buildMutationSelector", "selectInvalidatedBy", "selectCachedArgsForQuery", "withRequestFlags", "substate", "getRequestStatusFlags", "selectInternalState", "rootState", "endpointName", "endpointDefinition", "queryArgs", "serializedArgs", "id", "mutationId", "getMutationCacheKey", "tags", "apiState", "toInvalidate", "tag", "expandTagDescription", "provided", "invalidateSubscriptions", "flatten", "invalidate", "query<PERSON><PERSON><PERSON><PERSON>", "querySubState", "queryName", "entry", "cache", "defaultSerializeQueryArgs", "endpointName", "queryArgs", "serialized", "cached", "stringified", "key", "value", "acc", "import_reselect", "buildCreateApi", "modules", "options", "extractRehydrationInfo", "action", "optionsWithDefaults", "queryArgsApi", "finalSerializeQueryArgs", "defaultSerializeQueryArgs", "endpointSQA", "initialResult", "context", "fn", "api", "injectEndpoints", "addTagTypes", "endpoints", "eT", "endpointName", "partialDefinition", "initializedModules", "m", "inject", "evaluatedEndpoints", "x", "definition", "import_toolkit", "fakeBase<PERSON><PERSON>y", "_formatProdErrorMessage", "isObjectEmpty", "obj", "k", "THIRTY_TWO_BIT_MAX_TIMER_SECONDS", "buildCacheCollectionHandler", "reducerPath", "api", "context", "internalState", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "unsubscribeQueryResult", "anySubscriptionsRemainingForKey", "query<PERSON><PERSON><PERSON><PERSON>", "subscriptions", "isObjectEmpty", "currentRemovalTimeouts", "handler", "action", "mwApi", "state", "handleUnsubscribe", "key", "timeout", "queries", "queryState", "endpointName", "config", "keepUnusedDataFor", "finalKeepUnusedDataFor", "currentTimeout", "buildInvalidationByTagsHandler", "reducerPath", "context", "endpointDefinitions", "mutationThunk", "queryThunk", "api", "assertTagType", "refetch<PERSON><PERSON>y", "internalState", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isThunkActionWithTags", "isQueryEnd", "pendingTagInvalidations", "handler", "action", "mwApi", "invalidateTags", "calculateProvidedByThunk", "calculateProvidedBy", "hasPendingRequests", "state", "key", "newTags", "rootState", "tags", "toInvalidate", "valuesArray", "query<PERSON><PERSON><PERSON><PERSON>", "querySubState", "subscriptionSubState", "countObjectKeys", "buildPollingHandler", "reducerPath", "queryThunk", "api", "refetch<PERSON><PERSON>y", "internalState", "currentPolls", "handler", "action", "mwApi", "updatePollingInterval", "startNextPoll", "clearPolls", "query<PERSON><PERSON><PERSON><PERSON>", "querySubState", "subscriptions", "lowestPollingInterval", "findLowestPollingInterval", "currentPoll", "nextPollTimestamp", "currentInterval", "cleanupPollForKey", "key", "existingPoll", "subscribers", "buildWindowEventHandler", "reducerPath", "context", "api", "refetch<PERSON><PERSON>y", "internalState", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handler", "action", "mwApi", "onFocus", "refetchValidQueries", "onOnline", "type", "state", "queries", "subscriptions", "query<PERSON><PERSON><PERSON><PERSON>", "querySubState", "subscriptionSubState", "sub", "countObjectKeys", "neverResolvedError", "buildCacheLifecycleHandler", "api", "reducerPath", "context", "queryThunk", "mutationThunk", "internalState", "isQueryThunk", "isMutationThunk", "isFulfilledThunk", "lifecycleMap", "handler", "action", "mwApi", "stateBefore", "cache<PERSON>ey", "get<PERSON><PERSON><PERSON><PERSON>", "oldState", "state", "handleNewKey", "lifecycle", "getMutationCacheKey", "endpointName", "originalArgs", "query<PERSON><PERSON><PERSON><PERSON>", "requestId", "endpointDefinition", "onCacheEntryAdded", "cacheEntryRemoved", "resolve", "cacheDataLoaded", "selector", "extra", "_", "__", "lifecycleApi", "updateRecipe", "<PERSON><PERSON><PERSON><PERSON>", "e", "buildQueryLifecycleHandler", "api", "context", "queryThunk", "mutationThunk", "isPendingThunk", "isRejectedThunk", "isFullfilledThunk", "lifecycleMap", "action", "mwApi", "requestId", "endpointName", "originalArgs", "endpointDefinition", "onQueryStarted", "lifecycle", "queryFulfilled", "resolve", "reject", "selector", "extra", "_", "__", "lifecycleApi", "updateRecipe", "baseQueryMeta", "rejectedWithValue", "buildDevCheckHandler", "api", "apiUid", "reducerPath", "action", "mwApi", "import_immer", "buildBatchedActionsHandler", "api", "queryThunk", "internalState", "subscriptionsPrefix", "previousSubscriptions", "updateSyncTimer", "updateSubscriptionOptions", "unsubscribeQueryResult", "actuallyMutateSubscriptions", "mutableState", "action", "query<PERSON><PERSON><PERSON><PERSON>", "requestId", "options", "arg", "substate", "mutated", "state", "key", "condition", "getSubscriptions", "subscriptionSelectors", "subscriptionsForQueryArg", "countObjectKeys", "mwApi", "didMutate", "actionShouldContinue", "newSubscriptions", "patches", "isSubscriptionSliceAction", "isAdditionalSubscriptionAction", "buildMiddleware", "input", "reducerPath", "queryThunk", "api", "context", "apiUid", "actions", "isThisApiSliceAction", "action", "handlerBuilders", "buildDevCheckHandler", "buildCacheCollectionHandler", "buildInvalidationByTagsHandler", "buildPollingHandler", "buildCacheLifecycleHandler", "buildQueryLifecycleHandler", "mwApi", "initialized", "builderArgs", "refetch<PERSON><PERSON>y", "handlers", "build", "batchedActionsHandler", "buildBatchedActionsHandler", "windowEventsHandler", "buildWindowEventHandler", "next", "mwApiWithNext", "stateBefore", "actionShouldContinue", "internalProbeResult", "res", "handler", "querySubState", "query<PERSON><PERSON><PERSON><PERSON>", "override", "safeAssign", "target", "args", "import_immer", "coreModuleName", "coreModule", "api", "base<PERSON><PERSON>y", "tagTypes", "reducerPath", "serializeQueryArgs", "keepUnusedDataFor", "refetchOnMountOrArgChange", "refetchOnFocus", "refetchOnReconnect", "invalidation<PERSON><PERSON><PERSON><PERSON>", "context", "assertTagType", "tag", "onOnline", "onOffline", "onFocus", "onFocusLost", "queryThunk", "mutationThunk", "patchQueryData", "updateQueryData", "upsertQueryData", "prefetch", "buildMatchThunkActions", "buildThunks", "reducer", "sliceActions", "buildSlice", "safeAssign", "middleware", "middlewareActions", "buildMiddleware", "buildQuerySelector", "buildMutationSelector", "selectInvalidatedBy", "selectCachedArgsForQuery", "buildSelectors", "buildInitiateQuery", "buildInitiateMutation", "getRunningMutationThunk", "getRunningMutationsThunk", "getRunningQueriesThunk", "getRunningQueryThunk", "buildInitiate", "endpointName", "definition", "anyApi", "isQueryDefinition", "isMutationDefinition", "createApi", "buildCreateApi", "coreModule"]}