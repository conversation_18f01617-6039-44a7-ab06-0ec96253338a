{"version": 3, "sources": ["../../redux-persist/es/integration/react.js"], "sourcesContent": ["function _typeof(obj) { if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } return _assertThisInitialized(self); }\n\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\n\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); if (superClass) _setPrototypeOf(subClass, superClass); }\n\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nimport React, { PureComponent } from 'react'; // eslint-disable-line import/no-unresolved\n\nexport var PersistGate =\n/*#__PURE__*/\nfunction (_PureComponent) {\n  _inherits(PersistGate, _PureComponent);\n\n  function PersistGate() {\n    var _getPrototypeOf2;\n\n    var _this;\n\n    _classCallCheck(this, PersistGate);\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    _this = _possibleConstructorReturn(this, (_getPrototypeOf2 = _getPrototypeOf(PersistGate)).call.apply(_getPrototypeOf2, [this].concat(args)));\n\n    _defineProperty(_assertThisInitialized(_this), \"state\", {\n      bootstrapped: false\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"_unsubscribe\", void 0);\n\n    _defineProperty(_assertThisInitialized(_this), \"handlePersistorState\", function () {\n      var persistor = _this.props.persistor;\n\n      var _persistor$getState = persistor.getState(),\n          bootstrapped = _persistor$getState.bootstrapped;\n\n      if (bootstrapped) {\n        if (_this.props.onBeforeLift) {\n          Promise.resolve(_this.props.onBeforeLift()).finally(function () {\n            return _this.setState({\n              bootstrapped: true\n            });\n          });\n        } else {\n          _this.setState({\n            bootstrapped: true\n          });\n        }\n\n        _this._unsubscribe && _this._unsubscribe();\n      }\n    });\n\n    return _this;\n  }\n\n  _createClass(PersistGate, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this._unsubscribe = this.props.persistor.subscribe(this.handlePersistorState);\n      this.handlePersistorState();\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      this._unsubscribe && this._unsubscribe();\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      if (process.env.NODE_ENV !== 'production') {\n        if (typeof this.props.children === 'function' && this.props.loading) console.error('redux-persist: PersistGate expects either a function child or loading prop, but not both. The loading prop will be ignored.');\n      }\n\n      if (typeof this.props.children === 'function') {\n        return this.props.children(this.state.bootstrapped);\n      }\n\n      return this.state.bootstrapped ? this.props.children : this.props.loading;\n    }\n  }]);\n\n  return PersistGate;\n}(PureComponent);\n\n_defineProperty(PersistGate, \"defaultProps\", {\n  children: null,\n  loading: null\n});"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAoBA,mBAAqC;AApBrC,SAAS,QAAQ,KAAK;AAAE,MAAI,OAAO,WAAW,cAAc,OAAO,OAAO,aAAa,UAAU;AAAE,cAAU,SAASA,SAAQC,MAAK;AAAE,aAAO,OAAOA;AAAA,IAAK;AAAA,EAAG,OAAO;AAAE,cAAU,SAASD,SAAQC,MAAK;AAAE,aAAOA,QAAO,OAAO,WAAW,cAAcA,KAAI,gBAAgB,UAAUA,SAAQ,OAAO,YAAY,WAAW,OAAOA;AAAA,IAAK;AAAA,EAAG;AAAE,SAAO,QAAQ,GAAG;AAAG;AAE9V,SAAS,gBAAgB,UAAU,aAAa;AAAE,MAAI,EAAE,oBAAoB,cAAc;AAAE,UAAM,IAAI,UAAU,mCAAmC;AAAA,EAAG;AAAE;AAExJ,SAAS,kBAAkB,QAAQ,OAAO;AAAE,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAAE,QAAI,aAAa,MAAM,CAAC;AAAG,eAAW,aAAa,WAAW,cAAc;AAAO,eAAW,eAAe;AAAM,QAAI,WAAW;AAAY,iBAAW,WAAW;AAAM,WAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,EAAG;AAAE;AAE5T,SAAS,aAAa,aAAa,YAAY,aAAa;AAAE,MAAI;AAAY,sBAAkB,YAAY,WAAW,UAAU;AAAG,MAAI;AAAa,sBAAkB,aAAa,WAAW;AAAG,SAAO;AAAa;AAEtN,SAAS,2BAA2B,MAAM,MAAM;AAAE,MAAI,SAAS,QAAQ,IAAI,MAAM,YAAY,OAAO,SAAS,aAAa;AAAE,WAAO;AAAA,EAAM;AAAE,SAAO,uBAAuB,IAAI;AAAG;AAEhL,SAAS,gBAAgB,GAAG;AAAE,oBAAkB,OAAO,iBAAiB,OAAO,iBAAiB,SAASC,iBAAgBC,IAAG;AAAE,WAAOA,GAAE,aAAa,OAAO,eAAeA,EAAC;AAAA,EAAG;AAAG,SAAO,gBAAgB,CAAC;AAAG;AAE5M,SAAS,uBAAuB,MAAM;AAAE,MAAI,SAAS,QAAQ;AAAE,UAAM,IAAI,eAAe,2DAA2D;AAAA,EAAG;AAAE,SAAO;AAAM;AAErK,SAAS,UAAU,UAAU,YAAY;AAAE,MAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,UAAM,IAAI,UAAU,oDAAoD;AAAA,EAAG;AAAE,WAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,MAAI;AAAY,oBAAgB,UAAU,UAAU;AAAG;AAEhY,SAAS,gBAAgB,GAAG,GAAG;AAAE,oBAAkB,OAAO,kBAAkB,SAASC,iBAAgBD,IAAGE,IAAG;AAAE,IAAAF,GAAE,YAAYE;AAAG,WAAOF;AAAA,EAAG;AAAG,SAAO,gBAAgB,GAAG,CAAC;AAAG;AAEzK,SAAS,gBAAgB,KAAK,KAAK,OAAO;AAAE,MAAI,OAAO,KAAK;AAAE,WAAO,eAAe,KAAK,KAAK,EAAE,OAAc,YAAY,MAAM,cAAc,MAAM,UAAU,KAAK,CAAC;AAAA,EAAG,OAAO;AAAE,QAAI,GAAG,IAAI;AAAA,EAAO;AAAE,SAAO;AAAK;AAIzM,IAAI,cAEX,SAAU,gBAAgB;AACxB,YAAUG,cAAa,cAAc;AAErC,WAASA,eAAc;AACrB,QAAI;AAEJ,QAAI;AAEJ,oBAAgB,MAAMA,YAAW;AAEjC,aAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,WAAK,IAAI,IAAI,UAAU,IAAI;AAAA,IAC7B;AAEA,YAAQ,2BAA2B,OAAO,mBAAmB,gBAAgBA,YAAW,GAAG,KAAK,MAAM,kBAAkB,CAAC,IAAI,EAAE,OAAO,IAAI,CAAC,CAAC;AAE5I,oBAAgB,uBAAuB,KAAK,GAAG,SAAS;AAAA,MACtD,cAAc;AAAA,IAChB,CAAC;AAED,oBAAgB,uBAAuB,KAAK,GAAG,gBAAgB,MAAM;AAErE,oBAAgB,uBAAuB,KAAK,GAAG,wBAAwB,WAAY;AACjF,UAAI,YAAY,MAAM,MAAM;AAE5B,UAAI,sBAAsB,UAAU,SAAS,GACzC,eAAe,oBAAoB;AAEvC,UAAI,cAAc;AAChB,YAAI,MAAM,MAAM,cAAc;AAC5B,kBAAQ,QAAQ,MAAM,MAAM,aAAa,CAAC,EAAE,QAAQ,WAAY;AAC9D,mBAAO,MAAM,SAAS;AAAA,cACpB,cAAc;AAAA,YAChB,CAAC;AAAA,UACH,CAAC;AAAA,QACH,OAAO;AACL,gBAAM,SAAS;AAAA,YACb,cAAc;AAAA,UAChB,CAAC;AAAA,QACH;AAEA,cAAM,gBAAgB,MAAM,aAAa;AAAA,MAC3C;AAAA,IACF,CAAC;AAED,WAAO;AAAA,EACT;AAEA,eAAaA,cAAa,CAAC;AAAA,IACzB,KAAK;AAAA,IACL,OAAO,SAAS,oBAAoB;AAClC,WAAK,eAAe,KAAK,MAAM,UAAU,UAAU,KAAK,oBAAoB;AAC5E,WAAK,qBAAqB;AAAA,IAC5B;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,uBAAuB;AACrC,WAAK,gBAAgB,KAAK,aAAa;AAAA,IACzC;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,SAAS;AACvB,UAAI,QAAQ,IAAI,aAAa,cAAc;AACzC,YAAI,OAAO,KAAK,MAAM,aAAa,cAAc,KAAK,MAAM;AAAS,kBAAQ,MAAM,6HAA6H;AAAA,MAClN;AAEA,UAAI,OAAO,KAAK,MAAM,aAAa,YAAY;AAC7C,eAAO,KAAK,MAAM,SAAS,KAAK,MAAM,YAAY;AAAA,MACpD;AAEA,aAAO,KAAK,MAAM,eAAe,KAAK,MAAM,WAAW,KAAK,MAAM;AAAA,IACpE;AAAA,EACF,CAAC,CAAC;AAEF,SAAOA;AACT,EAAE,0BAAa;AAEf,gBAAgB,aAAa,gBAAgB;AAAA,EAC3C,UAAU;AAAA,EACV,SAAS;AACX,CAAC;", "names": ["_typeof", "obj", "_getPrototypeOf", "o", "_setPrototypeOf", "p", "PersistGate"]}