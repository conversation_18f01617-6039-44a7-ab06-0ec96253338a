export { B as BaseSequencer, V as VitestPlugin, c as createVitest, r as registerConsoleShortcuts, s as startVitest } from './vendor-node.00226ab1.js';
import 'pathe';
import 'vite';
import 'node:path';
import 'node:url';
import 'node:process';
import 'node:fs';
import './vendor-constants.538d9b49.js';
import './vendor-_commonjsHelpers.7d1333e8.js';
import 'os';
import 'path';
import 'util';
import 'stream';
import 'events';
import 'fs';
import 'picocolors';
import 'vite-node/utils';
import 'vite-node/client';
import '@vitest/snapshot/manager';
import 'vite-node/server';
import './vendor-index.087d1af7.js';
import 'std-env';
import '@vitest/runner/utils';
import '@vitest/utils';
import './vendor-global.97e4527c.js';
import './vendor-coverage.78040316.js';
import './vendor-paths.84fc7a99.js';
import 'node:v8';
import 'node:child_process';
import './vendor-index.b271ebe4.js';
import './vendor-base.9c08bbd0.js';
import 'node:worker_threads';
import 'node:os';
import 'tinypool';
import 'local-pkg';
import 'node:perf_hooks';
import './vendor-source-map.e6c1997b.js';
import 'node:module';
import 'node:crypto';
import './vendor-index.1f85e5f1.js';
import 'node:buffer';
import 'child_process';
import 'assert';
import 'buffer';
import 'node:util';
import 'node:fs/promises';
import 'module';
import 'acorn';
import 'acorn-walk';
import 'magic-string';
import 'strip-literal';
import './vendor-environments.443ecd82.js';
import 'node:console';
import 'node:readline';
import 'readline';
