# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.1.4](https://github.com/es-shims/Object.hasOwn/compare/v1.1.3...v1.1.4) - 2024-03-22

### Commits

- [meta] remove useless ESM [`28440a5`](https://github.com/es-shims/Object.hasOwn/commit/28440a5d2545632b6c6c95bea09ea304a3bfe94f)
- [actions] remove redundant finisher [`579b54d`](https://github.com/es-shims/Object.hasOwn/commit/579b54d37f8b2f83a8187a1525d1e3309c1d6972)
- [Deps] update `es-abstract` [`f7f9aa1`](https://github.com/es-shims/Object.hasOwn/commit/f7f9aa192bcaae10478826617dc707ea5b1ff175)
- [Dev Deps] update `call-bind`, `npmignore`, `tape` [`8a2f213`](https://github.com/es-shims/Object.hasOwn/commit/8a2f213618f4a33f83320c5b76f34b00228732dd)
- [Refactor] use `es-object-atoms` instead of `es-abstract` [`f0efe3b`](https://github.com/es-shims/Object.hasOwn/commit/f0efe3b2d40d143d44969026c9662d6b1f094c05)
- [Dev Deps] update `call-bind`, `tape` [`683b500`](https://github.com/es-shims/Object.hasOwn/commit/683b500aa26d75d8c95bc8fd57bd25c206382335)
- [Deps] update `define-properties`, `es-abstract` [`f74ecf2`](https://github.com/es-shims/Object.hasOwn/commit/f74ecf2cff80a254a8067aea12337c25198d6d78)
- [meta] add missing `engines.node` [`7ca6eeb`](https://github.com/es-shims/Object.hasOwn/commit/7ca6eeb4f9a7f706b700885419e6ddb88f8f0c70)
- [Deps] update `es-abstract` [`d5aa232`](https://github.com/es-shims/Object.hasOwn/commit/d5aa232f680e7bd0256c65922de908c41b5ac06e)
- [Dev Deps] update `aud` [`26fa7f9`](https://github.com/es-shims/Object.hasOwn/commit/26fa7f9cc8b8e1e7c1a40c2b3d19660f5f44bc49)

## [v1.1.3](https://github.com/es-shims/Object.hasOwn/compare/v1.1.2...v1.1.3) - 2023-08-28

### Commits

- [Deps] update `define-properties`, `es-abstract` [`4ca792b`](https://github.com/es-shims/Object.hasOwn/commit/4ca792b9c984bed5718f0931b7684145ec5beb09)
- [Dev Deps] update `@es-shims/api`, `@ljharb/eslint-config`, `aud`, `tape` [`ce37aaa`](https://github.com/es-shims/Object.hasOwn/commit/ce37aaa42c7612db5e6a88af784f76b76d6fcd0e)

## [v1.1.2](https://github.com/es-shims/Object.hasOwn/compare/v1.1.1...v1.1.2) - 2022-11-06

### Commits

- [actions] update rebase action to use reusable workflow [`db8c17c`](https://github.com/es-shims/Object.hasOwn/commit/db8c17c46fc53ffa1ed402308949e27d4dc13ae6)
- [Deps] update `es-abstract` [`8b549d2`](https://github.com/es-shims/Object.hasOwn/commit/8b549d28e1589e923d73b42516e2fa8cbdb358cb)
- [Dev Deps] update `@ljharb/eslint-config`, `aud`, `tape` [`25fc539`](https://github.com/es-shims/Object.hasOwn/commit/25fc539d426a6f01ad0bb8bc3593919316b88c89)

## [v1.1.1](https://github.com/es-shims/Object.hasOwn/compare/v1.1.0...v1.1.1) - 2022-05-05

### Commits

- [actions] remove unused actions [`4ca5814`](https://github.com/es-shims/Object.hasOwn/commit/4ca5814dc12ce990058574b84eccfdbe71e36506)
- [actions] reuse common workflows [`e265a2f`](https://github.com/es-shims/Object.hasOwn/commit/e265a2f9c7a538acbe7d84f8d1373c9a22d0aaa7)
- [meta] use `npmignore` to autogenerate an npmignore file [`4f54d5e`](https://github.com/es-shims/Object.hasOwn/commit/4f54d5e68bdbf390715138f1f228c51adb7e2d1d)
- [Fix] `shim`: properly install when already present [`f580b50`](https://github.com/es-shims/Object.hasOwn/commit/f580b50b84aeced35eab728629cb0e2c3d734685)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `functions-have-names`, `has-symbols`, `tape` [`335d3c1`](https://github.com/es-shims/Object.hasOwn/commit/335d3c15429aa0936cdf8019d9ec7563cc362488)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `@es-shims/api`, `safe-publish-latest`, `tape` [`c040e94`](https://github.com/es-shims/Object.hasOwn/commit/c040e94ab5edce66787873d398c68c90c594e999)
- [actions] update codecov uploader [`723dd15`](https://github.com/es-shims/Object.hasOwn/commit/723dd15c7fb49fef5c76fe95f14c2a1b32d3967d)
- [Deps] update `define-properties`, `es-abstract` [`4fb99f9`](https://github.com/es-shims/Object.hasOwn/commit/4fb99f93fa493e134416fe8dfaaa8fd2db335ee6)
- [Dev Deps] update `@ljharb/eslint-config` [`9a5e992`](https://github.com/es-shims/Object.hasOwn/commit/9a5e9921328858e359e6835d29f3a4349496aea2)

## [v1.1.0](https://github.com/es-shims/Object.hasOwn/compare/v1.0.0...v1.1.0) - 2021-10-03

### Commits

- [New] add ESM entry point [`8b8b4b2`](https://github.com/es-shims/Object.hasOwn/commit/8b8b4b22e22396b5ba080382c33e5844efbcf386)
- [Tests] add implementation tests [`5edecfe`](https://github.com/es-shims/Object.hasOwn/commit/5edecfee70af78dee7d870e55c365e2b96dd449d)
- [Deps] update `es-abstract` [`6ebc660`](https://github.com/es-shims/Object.hasOwn/commit/6ebc66079ca87f367b08051bf9b24e614b75d2f3)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `@es-shims/api`, `tape` [`e1832e2`](https://github.com/es-shims/Object.hasOwn/commit/e1832e2304dd40a899e07d50b50c603bb8a92844)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog` [`b1adc25`](https://github.com/es-shims/Object.hasOwn/commit/b1adc2505b0b19c6c21a4ea7cdab9655e2f146d4)
- [Deps] update `es-abstract` [`1e7a06c`](https://github.com/es-shims/Object.hasOwn/commit/1e7a06cd73c2d980694908d5c5b204ae2c94bc70)
- [Deps] update `es-abstract` [`622d71c`](https://github.com/es-shims/Object.hasOwn/commit/622d71c5168f5a863dd68833d760f882d5330aa7)

## v1.0.0 - 2021-05-25

### Commits

- Initial package creation [`6f74bd8`](https://github.com/es-shims/Object.hasOwn/commit/6f74bd8cd669cd5964358ef85b51466baea34af7)
- Initial commit [`cea8e05`](https://github.com/es-shims/Object.hasOwn/commit/cea8e058018ccd8ba31b15eebfe2b8181deb9946)
