var Te=(f=>(f.uninitialized="uninitialized",f.pending="pending",f.fulfilled="fulfilled",f.rejected="rejected",f))(Te||{});function Be(e){return{status:e,isUninitialized:e==="uninitialized",isLoading:e==="pending",isSuccess:e==="fulfilled",isError:e==="rejected"}}function Ee(e){return new RegExp("(^|:)//").test(e)}var ut=e=>e.replace(/\/$/,""),yt=e=>e.replace(/^\//,"");function ke(e,i){if(!e)return i;if(!i)return e;if(Ee(i))return i;let l=e.endsWith("/")||!i.startsWith("?")?"/":"";return e=ut(e),i=yt(i),`${e}${l}${i}`}var Qe=e=>[].concat(...e);function ve(){return typeof navigator>"u"||navigator.onLine===void 0?!0:navigator.onLine}function Pe(){return typeof document>"u"?!0:document.visibilityState!=="hidden"}import{createAction as j,createSlice as $,createSelector as Re,createAsyncThunk as Se,combineReducers as Ce,createNextState as ne,isAnyOf as re,isAllOf as ue,isAction as we,isPending as ye,isRejected as Y,isFulfilled as I,isRejectedWithValue as G,isAsyncThunkAction as Ae,prepareAutoBatched as X,SHOULD_AUTOBATCH as ie,isPlainObject as _,nanoid as Fe}from"@reduxjs/toolkit";var Ie=_;function de(e,i){if(e===i||!(Ie(e)&&Ie(i)||Array.isArray(e)&&Array.isArray(i)))return i;let l=Object.keys(i),m=Object.keys(e),f=l.length===m.length,R=Array.isArray(i)?[]:{};for(let S of l)R[S]=de(e[S],i[S]),f&&(f=e[S]===R[S]);return f?e:R}var Oe=(...e)=>fetch(...e),dt=e=>e.status>=200&&e.status<=299,pt=e=>/ion\/(vnd\.api\+)?json/.test(e.get("content-type")||"");function qe(e){if(!_(e))return e;let i={...e};for(let[l,m]of Object.entries(i))m===void 0&&delete i[l];return i}function ct({baseUrl:e,prepareHeaders:i=d=>d,fetchFn:l=Oe,paramsSerializer:m,isJsonContentType:f=pt,jsonContentType:R="application/json",jsonReplacer:S,timeout:x,responseHandler:E,validateStatus:h,...u}={}){return typeof fetch>"u"&&l===Oe&&console.warn("Warning: `fetch` is not available. Please supply a custom `fetchFn` property to use `fetchBaseQuery` on SSR environments."),async(r,t)=>{let{signal:c,getState:A,extra:g,endpoint:y,forced:o,type:s}=t,n,{url:a,headers:T=new Headers(u.headers),params:p=void 0,responseHandler:Q=E??"json",validateStatus:D=h??dt,timeout:b=x,...M}=typeof r=="string"?{url:r}:r,B={...u,signal:c,...M};T=new Headers(qe(T)),B.headers=await i(T,{getState:A,extra:g,endpoint:y,forced:o,type:s})||T;let v=k=>typeof k=="object"&&(_(k)||Array.isArray(k)||typeof k.toJSON=="function");if(!B.headers.has("content-type")&&v(B.body)&&B.headers.set("content-type",R),v(B.body)&&f(B.headers)&&(B.body=JSON.stringify(B.body,S)),p){let k=~a.indexOf("?")?"&":"?",V=m?m(p):new URLSearchParams(qe(p));a+=k+V}a=ke(e,a);let P=new Request(a,B);n={request:new Request(a,B)};let C,w=!1,O=b&&setTimeout(()=>{w=!0,t.abort()},b);try{C=await l(P)}catch(k){return{error:{status:w?"TIMEOUT_ERROR":"FETCH_ERROR",error:String(k)},meta:n}}finally{O&&clearTimeout(O)}let q=C.clone();n.response=q;let W,H="";try{let k;if(await Promise.all([d(C,Q).then(V=>W=V,V=>k=V),q.text().then(V=>H=V,()=>{})]),k)throw k}catch(k){return{error:{status:"PARSING_ERROR",originalStatus:C.status,data:H,error:String(k)},meta:n}}return D(C,W)?{data:W,meta:n}:{error:{status:C.status,data:W},meta:n}};async function d(r,t){if(typeof t=="function")return t(r);if(t==="content-type"&&(t=f(r.headers)?"json":"text"),t==="json"){let c=await r.text();return c.length?JSON.parse(c):null}return r.text()}}var N=class{constructor(i,l=void 0){this.value=i;this.meta=l}};async function lt(e=0,i=5){let l=Math.min(e,i),m=~~((Math.random()+.4)*(300<<l));await new Promise(f=>setTimeout(R=>f(R),m))}function ft(e){throw Object.assign(new N({error:e}),{throwImmediately:!0})}var Ne={},mt=(e,i)=>async(l,m,f)=>{let R=[5,(i||Ne).maxRetries,(f||Ne).maxRetries].filter(u=>u!==void 0),[S]=R.slice(-1),E={maxRetries:S,backoff:lt,retryCondition:(u,d,{attempt:r})=>r<=S,...i,...f},h=0;for(;;)try{let u=await e(l,m,f);if(u.error)throw new N(u);return u}catch(u){if(h++,u.throwImmediately){if(u instanceof N)return u.value;throw u}if(u instanceof N&&!E.retryCondition(u.value.error,l,{attempt:h,baseQueryApi:m,extraOptions:f}))return u.value;await E.backoff(h,E.maxRetries)}},gt=Object.assign(mt,{fail:ft});var L=j("__rtkq/focused"),Z=j("__rtkq/unfocused"),z=j("__rtkq/online"),ee=j("__rtkq/offline"),De=!1;function ht(e,i){function l(){let m=()=>e(L()),f=()=>e(Z()),R=()=>e(z()),S=()=>e(ee()),x=()=>{window.document.visibilityState==="visible"?m():f()};return De||typeof window<"u"&&window.addEventListener&&(window.addEventListener("visibilitychange",x,!1),window.addEventListener("focus",m,!1),window.addEventListener("online",R,!1),window.addEventListener("offline",S,!1),De=!0),()=>{window.removeEventListener("focus",m),window.removeEventListener("visibilitychange",x),window.removeEventListener("online",R),window.removeEventListener("offline",S),De=!1}}return i?i(e,{onFocus:L,onFocusLost:Z,onOffline:ee,onOnline:z}):l()}function ce(e){return e.type==="query"}function Ke(e){return e.type==="mutation"}function oe(e,i,l,m,f,R){return Tt(e)?e(i,l,m,f).map(pe).map(R):Array.isArray(e)?e.map(pe).map(R):[]}function Tt(e){return typeof e=="function"}function pe(e){return typeof e=="string"?{type:e}:e}import"@reduxjs/toolkit";function xe(e){return e!=null}function K(e){let i=0;for(let l in e)i++;return i}var te=Symbol("forceQueryFn"),ae=e=>typeof e[te]=="function";function je({serializeQueryArgs:e,queryThunk:i,mutationThunk:l,api:m,context:f}){let R=new Map,S=new Map,{unsubscribeQueryResult:x,removeMutationResult:E,updateSubscriptionOptions:h}=m.internalActions;return{buildInitiateQuery:A,buildInitiateMutation:g,getRunningQueryThunk:u,getRunningMutationThunk:d,getRunningQueriesThunk:r,getRunningMutationsThunk:t};function u(y,o){return s=>{let n=f.endpointDefinitions[y],a=e({queryArgs:o,endpointDefinition:n,endpointName:y});return R.get(s)?.[a]}}function d(y,o){return s=>S.get(s)?.[o]}function r(){return y=>Object.values(R.get(y)||{}).filter(xe)}function t(){return y=>Object.values(S.get(y)||{}).filter(xe)}function c(y){}function A(y,o){let s=(n,{subscribe:a=!0,forceRefetch:T,subscriptionOptions:p,[te]:Q}={})=>(D,b)=>{let M=e({queryArgs:n,endpointDefinition:o,endpointName:y}),B=i({type:"query",subscribe:a,forceRefetch:T,subscriptionOptions:p,endpointName:y,originalArgs:n,queryCacheKey:M,[te]:Q}),v=m.endpoints[y].select(n),P=D(B),F=v(b());let{requestId:C,abort:w}=P,O=F.requestId!==C,q=R.get(D)?.[M],W=()=>v(b()),H=Object.assign(Q?P.then(W):O&&!q?Promise.resolve(F):Promise.all([q,P]).then(W),{arg:n,requestId:C,subscriptionOptions:p,queryCacheKey:M,abort:w,async unwrap(){let k=await H;if(k.isError)throw k.error;return k.data},refetch:()=>D(s(n,{subscribe:!1,forceRefetch:!0})),unsubscribe(){a&&D(x({queryCacheKey:M,requestId:C}))},updateSubscriptionOptions(k){H.subscriptionOptions=k,D(h({endpointName:y,requestId:C,queryCacheKey:M,options:k}))}});if(!q&&!O&&!Q){let k=R.get(D)||{};k[M]=H,R.set(D,k),H.then(()=>{delete k[M],K(k)||R.delete(D)})}return H};return s}function g(y){return(o,{track:s=!0,fixedCacheKey:n}={})=>(a,T)=>{let p=l({type:"mutation",endpointName:y,originalArgs:o,track:s,fixedCacheKey:n}),Q=a(p);let{requestId:D,abort:b,unwrap:M}=Q,B=Q.unwrap().then(C=>({data:C})).catch(C=>({error:C})),v=()=>{a(E({requestId:D,fixedCacheKey:n}))},P=Object.assign(B,{arg:Q.arg,requestId:D,abort:b,unwrap:M,reset:v}),F=S.get(a)||{};return S.set(a,F),F[D]=P,P.then(()=>{delete F[D],K(F)||S.delete(a)}),n&&(F[n]=P,P.then(()=>{F[n]===P&&(delete F[n],K(F)||S.delete(a))})),P}}}import{isDraftable as Qt,produceWithPatches as Rt}from"immer";function Ue(e){return e}function He({reducerPath:e,baseQuery:i,context:{endpointDefinitions:l},serializeQueryArgs:m,api:f,assertTagType:R}){let S=(o,s,n,a)=>(T,p)=>{let Q=l[o],D=m({queryArgs:s,endpointDefinition:Q,endpointName:o});if(T(f.internalActions.queryResultPatched({queryCacheKey:D,patches:n})),!a)return;let b=f.endpoints[o].select(s)(p()),M=oe(Q.providesTags,b.data,void 0,s,{},R);T(f.internalActions.updateProvidedBy({queryCacheKey:D,providedTags:M}))},x=(o,s,n,a=!0)=>(T,p)=>{let D=f.endpoints[o].select(s)(p()),b={patches:[],inversePatches:[],undo:()=>T(f.util.patchQueryData(o,s,b.inversePatches,a))};if(D.status==="uninitialized")return b;let M;if("data"in D)if(Qt(D.data)){let[B,v,P]=Rt(D.data,n);b.patches.push(...v),b.inversePatches.push(...P),M=B}else M=n(D.data),b.patches.push({op:"replace",path:[],value:M}),b.inversePatches.push({op:"replace",path:[],value:D.data});return T(f.util.patchQueryData(o,s,b.patches,a)),b},E=(o,s,n)=>a=>a(f.endpoints[o].initiate(s,{subscribe:!1,forceRefetch:!0,[te]:()=>({data:n})})),h=async(o,{signal:s,abort:n,rejectWithValue:a,fulfillWithValue:T,dispatch:p,getState:Q,extra:D})=>{let b=l[o.endpointName];try{let M=Ue,B,v={signal:s,abort:n,dispatch:p,getState:Q,extra:D,endpoint:o.endpointName,type:o.type,forced:o.type==="query"?u(o,Q()):void 0},P=o.type==="query"?o[te]:void 0;if(P?B=P():b.query?(B=await i(b.query(o.originalArgs),v,b.extraOptions),b.transformResponse&&(M=b.transformResponse)):B=await b.queryFn(o.originalArgs,v,b.extraOptions,F=>i(F,v,b.extraOptions)),typeof process<"u",B.error)throw new N(B.error,B.meta);return T(await M(B.data,B.meta,o.originalArgs),{fulfilledTimeStamp:Date.now(),baseQueryMeta:B.meta,[ie]:!0})}catch(M){let B=M;if(B instanceof N){let v=Ue;b.query&&b.transformErrorResponse&&(v=b.transformErrorResponse);try{return a(await v(B.value,B.meta,o.originalArgs),{baseQueryMeta:B.meta,[ie]:!0})}catch(P){B=P}}throw typeof process<"u",console.error(B),B}};function u(o,s){let n=s[e]?.queries?.[o.queryCacheKey],a=s[e]?.config.refetchOnMountOrArgChange,T=n?.fulfilledTimeStamp,p=o.forceRefetch??(o.subscribe&&a);return p?p===!0||(Number(new Date)-Number(T))/1e3>=p:!1}let d=Se(`${e}/executeQuery`,h,{getPendingMeta(){return{startedTimeStamp:Date.now(),[ie]:!0}},condition(o,{getState:s}){let n=s(),a=n[e]?.queries?.[o.queryCacheKey],T=a?.fulfilledTimeStamp,p=o.originalArgs,Q=a?.originalArgs,D=l[o.endpointName];return ae(o)?!0:a?.status==="pending"?!1:u(o,n)||ce(D)&&D?.forceRefetch?.({currentArg:p,previousArg:Q,endpointState:a,state:n})?!0:!T},dispatchConditionRejection:!0}),r=Se(`${e}/executeMutation`,h,{getPendingMeta(){return{startedTimeStamp:Date.now(),[ie]:!0}}}),t=o=>"force"in o,c=o=>"ifOlderThan"in o,A=(o,s,n)=>(a,T)=>{let p=t(n)&&n.force,Q=c(n)&&n.ifOlderThan,D=(M=!0)=>f.endpoints[o].initiate(s,{forceRefetch:M}),b=f.endpoints[o].select(s)(T());if(p)a(D());else if(Q){let M=b?.fulfilledTimeStamp;if(!M){a(D());return}(Number(new Date)-Number(new Date(M)))/1e3>=Q&&a(D())}else a(D(!1))};function g(o){return s=>s?.meta?.arg?.endpointName===o}function y(o,s){return{matchPending:ue(ye(o),g(s)),matchFulfilled:ue(I(o),g(s)),matchRejected:ue(Y(o),g(s))}}return{queryThunk:d,mutationThunk:r,prefetch:A,updateQueryData:x,upsertQueryData:E,patchQueryData:S,buildMatchThunkActions:y}}function le(e,i,l,m){return oe(l[e.meta.arg.endpointName][i],I(e)?e.payload:void 0,G(e)?e.payload:void 0,e.meta.arg.originalArgs,"baseQueryMeta"in e.meta?e.meta.baseQueryMeta:void 0,m)}import{isDraft as St}from"immer";import{applyPatches as _e,original as At}from"immer";function fe(e,i,l){let m=e[i];m&&l(m)}function J(e){return("arg"in e?e.arg.fixedCacheKey:e.fixedCacheKey)??e.requestId}function Le(e,i,l){let m=e[J(i)];m&&l(m)}var se={};function ze({reducerPath:e,queryThunk:i,mutationThunk:l,context:{endpointDefinitions:m,apiUid:f,extractRehydrationInfo:R,hasRehydrationInfo:S},assertTagType:x,config:E}){let h=j(`${e}/resetApiState`),u=$({name:`${e}/queries`,initialState:se,reducers:{removeQueryResult:{reducer(s,{payload:{queryCacheKey:n}}){delete s[n]},prepare:X()},queryResultPatched:{reducer(s,{payload:{queryCacheKey:n,patches:a}}){fe(s,n,T=>{T.data=_e(T.data,a.concat())})},prepare:X()}},extraReducers(s){s.addCase(i.pending,(n,{meta:a,meta:{arg:T}})=>{let p=ae(T);n[T.queryCacheKey]??={status:"uninitialized",endpointName:T.endpointName},fe(n,T.queryCacheKey,Q=>{Q.status="pending",Q.requestId=p&&Q.requestId?Q.requestId:a.requestId,T.originalArgs!==void 0&&(Q.originalArgs=T.originalArgs),Q.startedTimeStamp=a.startedTimeStamp})}).addCase(i.fulfilled,(n,{meta:a,payload:T})=>{fe(n,a.arg.queryCacheKey,p=>{if(p.requestId!==a.requestId&&!ae(a.arg))return;let{merge:Q}=m[a.arg.endpointName];if(p.status="fulfilled",Q)if(p.data!==void 0){let{fulfilledTimeStamp:D,arg:b,baseQueryMeta:M,requestId:B}=a,v=ne(p.data,P=>Q(P,T,{arg:b.originalArgs,baseQueryMeta:M,fulfilledTimeStamp:D,requestId:B}));p.data=v}else p.data=T;else p.data=m[a.arg.endpointName].structuralSharing??!0?de(St(p.data)?At(p.data):p.data,T):T;delete p.error,p.fulfilledTimeStamp=a.fulfilledTimeStamp})}).addCase(i.rejected,(n,{meta:{condition:a,arg:T,requestId:p},error:Q,payload:D})=>{fe(n,T.queryCacheKey,b=>{if(!a){if(b.requestId!==p)return;b.status="rejected",b.error=D??Q}})}).addMatcher(S,(n,a)=>{let{queries:T}=R(a);for(let[p,Q]of Object.entries(T))(Q?.status==="fulfilled"||Q?.status==="rejected")&&(n[p]=Q)})}}),d=$({name:`${e}/mutations`,initialState:se,reducers:{removeMutationResult:{reducer(s,{payload:n}){let a=J(n);a in s&&delete s[a]},prepare:X()}},extraReducers(s){s.addCase(l.pending,(n,{meta:a,meta:{requestId:T,arg:p,startedTimeStamp:Q}})=>{p.track&&(n[J(a)]={requestId:T,status:"pending",endpointName:p.endpointName,startedTimeStamp:Q})}).addCase(l.fulfilled,(n,{payload:a,meta:T})=>{T.arg.track&&Le(n,T,p=>{p.requestId===T.requestId&&(p.status="fulfilled",p.data=a,p.fulfilledTimeStamp=T.fulfilledTimeStamp)})}).addCase(l.rejected,(n,{payload:a,error:T,meta:p})=>{p.arg.track&&Le(n,p,Q=>{Q.requestId===p.requestId&&(Q.status="rejected",Q.error=a??T)})}).addMatcher(S,(n,a)=>{let{mutations:T}=R(a);for(let[p,Q]of Object.entries(T))(Q?.status==="fulfilled"||Q?.status==="rejected")&&p!==Q?.requestId&&(n[p]=Q)})}}),r=$({name:`${e}/invalidation`,initialState:se,reducers:{updateProvidedBy:{reducer(s,n){let{queryCacheKey:a,providedTags:T}=n.payload;for(let p of Object.values(s))for(let Q of Object.values(p)){let D=Q.indexOf(a);D!==-1&&Q.splice(D,1)}for(let{type:p,id:Q}of T){let D=(s[p]??={})[Q||"__internal_without_id"]??=[];D.includes(a)||D.push(a)}},prepare:X()}},extraReducers(s){s.addCase(u.actions.removeQueryResult,(n,{payload:{queryCacheKey:a}})=>{for(let T of Object.values(n))for(let p of Object.values(T)){let Q=p.indexOf(a);Q!==-1&&p.splice(Q,1)}}).addMatcher(S,(n,a)=>{let{provided:T}=R(a);for(let[p,Q]of Object.entries(T))for(let[D,b]of Object.entries(Q)){let M=(n[p]??={})[D||"__internal_without_id"]??=[];for(let B of b)M.includes(B)||M.push(B)}}).addMatcher(re(I(i),G(i)),(n,a)=>{let T=le(a,"providesTags",m,x),{queryCacheKey:p}=a.meta.arg;r.caseReducers.updateProvidedBy(n,r.actions.updateProvidedBy({queryCacheKey:p,providedTags:T}))})}}),t=$({name:`${e}/subscriptions`,initialState:se,reducers:{updateSubscriptionOptions(s,n){},unsubscribeQueryResult(s,n){},internal_getRTKQSubscriptions(){}}}),c=$({name:`${e}/internalSubscriptions`,initialState:se,reducers:{subscriptionsUpdated:{reducer(s,n){return _e(s,n.payload)},prepare:X()}}}),A=$({name:`${e}/config`,initialState:{online:ve(),focused:Pe(),middlewareRegistered:!1,...E},reducers:{middlewareRegistered(s,{payload:n}){s.middlewareRegistered=s.middlewareRegistered==="conflict"||f!==n?"conflict":!0}},extraReducers:s=>{s.addCase(z,n=>{n.online=!0}).addCase(ee,n=>{n.online=!1}).addCase(L,n=>{n.focused=!0}).addCase(Z,n=>{n.focused=!1}).addMatcher(S,n=>({...n}))}}),g=Ce({queries:u.reducer,mutations:d.reducer,provided:r.reducer,subscriptions:c.reducer,config:A.reducer}),y=(s,n)=>g(h.match(n)?void 0:s,n),o={...A.actions,...u.actions,...t.actions,...c.actions,...d.actions,...r.actions,resetApiState:h};return{reducer:y,actions:o}}var me=Symbol.for("RTKQ/skipToken"),$e={status:"uninitialized"},We=ne($e,()=>{}),Ve=ne($e,()=>{});function Je({serializeQueryArgs:e,reducerPath:i}){let l=u=>We,m=u=>Ve;return{buildQuerySelector:S,buildMutationSelector:x,selectInvalidatedBy:E,selectCachedArgsForQuery:h};function f(u){return{...u,...Be(u.status)}}function R(u){return u[i]}function S(u,d){return r=>{let t=e({queryArgs:r,endpointDefinition:d,endpointName:u});return Re(r===me?l:g=>R(g)?.queries?.[t]??We,f)}}function x(){return u=>{let d;return typeof u=="object"?d=J(u)??me:d=u,Re(d===me?m:c=>R(c)?.mutations?.[d]??Ve,f)}}function E(u,d){let r=u[i],t=new Set;for(let c of d.map(pe)){let A=r.provided[c.type];if(!A)continue;let g=(c.id!==void 0?A[c.id]:Qe(Object.values(A)))??[];for(let y of g)t.add(y)}return Qe(Array.from(t.values()).map(c=>{let A=r.queries[c];return A?[{queryCacheKey:c,endpointName:A.endpointName,originalArgs:A.originalArgs}]:[]}))}function h(u,d){return Object.values(u[i].queries).filter(r=>r?.endpointName===d&&r.status!=="uninitialized").map(r=>r.originalArgs)}}var Ye=WeakMap?new WeakMap:void 0,ge=({endpointName:e,queryArgs:i})=>{let l="",m=Ye?.get(i);if(typeof m=="string")l=m;else{let f=JSON.stringify(i,(R,S)=>_(S)?Object.keys(S).sort().reduce((x,E)=>(x[E]=S[E],x),{}):S);_(i)&&Ye?.set(i,f),l=f}return`${e}(${l})`};import{weakMapMemoize as Ge}from"reselect";function be(...e){return function(l){let m=Ge(h=>l.extractRehydrationInfo?.(h,{reducerPath:l.reducerPath??"api"})),f={reducerPath:"api",keepUnusedDataFor:60,refetchOnMountOrArgChange:!1,refetchOnFocus:!1,refetchOnReconnect:!1,invalidationBehavior:"delayed",...l,extractRehydrationInfo:m,serializeQueryArgs(h){let u=ge;if("serializeQueryArgs"in h.endpointDefinition){let d=h.endpointDefinition.serializeQueryArgs;u=r=>{let t=d(r);return typeof t=="string"?t:ge({...r,queryArgs:t})}}else l.serializeQueryArgs&&(u=l.serializeQueryArgs);return u(h)},tagTypes:[...l.tagTypes||[]]},R={endpointDefinitions:{},batch(h){h()},apiUid:Fe(),extractRehydrationInfo:m,hasRehydrationInfo:Ge(h=>m(h)!=null)},S={injectEndpoints:E,enhanceEndpoints({addTagTypes:h,endpoints:u}){if(h)for(let d of h)f.tagTypes.includes(d)||f.tagTypes.push(d);if(u)for(let[d,r]of Object.entries(u))typeof r=="function"?r(R.endpointDefinitions[d]):Object.assign(R.endpointDefinitions[d]||{},r);return S}},x=e.map(h=>h.init(S,f,R));function E(h){let u=h.endpoints({query:d=>({...d,type:"query"}),mutation:d=>({...d,type:"mutation"})});for(let[d,r]of Object.entries(u)){if(!h.overrideExisting&&d in R.endpointDefinitions){typeof process<"u";continue}R.endpointDefinitions[d]=r;for(let t of x)t.injectEndpoint(d,r)}return S}return S.injectEndpoints({endpoints:l.endpoints})}}import{formatProdErrorMessage as Dt}from"@reduxjs/toolkit";function xt(){return function(){throw new Error(Dt(33))}}function bt(e){for(let i in e)return!1;return!0}var Mt=2147483647/1e3-1,Xe=({reducerPath:e,api:i,context:l,internalState:m})=>{let{removeQueryResult:f,unsubscribeQueryResult:R}=i.internalActions;function S(u){let d=m.currentSubscriptions[u];return!!d&&!bt(d)}let x={},E=(u,d,r)=>{if(R.match(u)){let t=d.getState()[e],{queryCacheKey:c}=u.payload;h(c,t.queries[c]?.endpointName,d,t.config)}if(i.util.resetApiState.match(u))for(let[t,c]of Object.entries(x))c&&clearTimeout(c),delete x[t];if(l.hasRehydrationInfo(u)){let t=d.getState()[e],{queries:c}=l.extractRehydrationInfo(u);for(let[A,g]of Object.entries(c))h(A,g?.endpointName,d,t.config)}};function h(u,d,r,t){let A=l.endpointDefinitions[d]?.keepUnusedDataFor??t.keepUnusedDataFor;if(A===1/0)return;let g=Math.max(0,Math.min(A,Mt));if(!S(u)){let y=x[u];y&&clearTimeout(y),x[u]=setTimeout(()=>{S(u)||r.dispatch(f({queryCacheKey:u})),delete x[u]},g*1e3)}}return E};var Ze=({reducerPath:e,context:i,context:{endpointDefinitions:l},mutationThunk:m,queryThunk:f,api:R,assertTagType:S,refetchQuery:x,internalState:E})=>{let{removeQueryResult:h}=R.internalActions,u=re(I(m),G(m)),d=re(I(m,f),Y(m,f)),r=[],t=(g,y)=>{u(g)?A(le(g,"invalidatesTags",l,S),y):d(g)?A([],y):R.util.invalidateTags.match(g)&&A(oe(g.payload,void 0,void 0,void 0,void 0,S),y)};function c(g){for(let y in g.queries)if(g.queries[y]?.status==="pending")return!0;for(let y in g.mutations)if(g.mutations[y]?.status==="pending")return!0;return!1}function A(g,y){let o=y.getState(),s=o[e];if(r.push(...g),s.config.invalidationBehavior==="delayed"&&c(s))return;let n=r;if(r=[],n.length===0)return;let a=R.util.selectInvalidatedBy(o,n);i.batch(()=>{let T=Array.from(a.values());for(let{queryCacheKey:p}of T){let Q=s.queries[p],D=E.currentSubscriptions[p]??{};Q&&(K(D)===0?y.dispatch(h({queryCacheKey:p})):Q.status!=="uninitialized"&&y.dispatch(x(Q,p)))}})}return t};var et=({reducerPath:e,queryThunk:i,api:l,refetchQuery:m,internalState:f})=>{let R={},S=(r,t)=>{(l.internalActions.updateSubscriptionOptions.match(r)||l.internalActions.unsubscribeQueryResult.match(r))&&E(r.payload,t),(i.pending.match(r)||i.rejected.match(r)&&r.meta.condition)&&E(r.meta.arg,t),(i.fulfilled.match(r)||i.rejected.match(r)&&!r.meta.condition)&&x(r.meta.arg,t),l.util.resetApiState.match(r)&&u()};function x({queryCacheKey:r},t){let A=t.getState()[e].queries[r],g=f.currentSubscriptions[r];if(!A||A.status==="uninitialized")return;let y=d(g);if(!Number.isFinite(y))return;let o=R[r];o?.timeout&&(clearTimeout(o.timeout),o.timeout=void 0);let s=Date.now()+y,n=R[r]={nextPollTimestamp:s,pollingInterval:y,timeout:setTimeout(()=>{n.timeout=void 0,t.dispatch(m(A,r))},y)}}function E({queryCacheKey:r},t){let A=t.getState()[e].queries[r],g=f.currentSubscriptions[r];if(!A||A.status==="uninitialized")return;let y=d(g);if(!Number.isFinite(y)){h(r);return}let o=R[r],s=Date.now()+y;(!o||s<o.nextPollTimestamp)&&x({queryCacheKey:r},t)}function h(r){let t=R[r];t?.timeout&&clearTimeout(t.timeout),delete R[r]}function u(){for(let r of Object.keys(R))h(r)}function d(r={}){let t=Number.POSITIVE_INFINITY;for(let c in r)r[c].pollingInterval&&(t=Math.min(r[c].pollingInterval,t));return t}return S};var tt=({reducerPath:e,context:i,api:l,refetchQuery:m,internalState:f})=>{let{removeQueryResult:R}=l.internalActions,S=(E,h)=>{L.match(E)&&x(h,"refetchOnFocus"),z.match(E)&&x(h,"refetchOnReconnect")};function x(E,h){let u=E.getState()[e],d=u.queries,r=f.currentSubscriptions;i.batch(()=>{for(let t of Object.keys(r)){let c=d[t],A=r[t];if(!A||!c)continue;(Object.values(A).some(y=>y[h]===!0)||Object.values(A).every(y=>y[h]===void 0)&&u.config[h])&&(K(A)===0?E.dispatch(R({queryCacheKey:t})):c.status!=="uninitialized"&&E.dispatch(m(c,t)))}})}return S};var nt=new Error("Promise never resolved before cacheEntryRemoved."),rt=({api:e,reducerPath:i,context:l,queryThunk:m,mutationThunk:f,internalState:R})=>{let S=Ae(m),x=Ae(f),E=I(m,f),h={},u=(t,c,A)=>{let g=d(t);if(m.pending.match(t)){let y=A[i].queries[g],o=c.getState()[i].queries[g];!y&&o&&r(t.meta.arg.endpointName,t.meta.arg.originalArgs,g,c,t.meta.requestId)}else if(f.pending.match(t))c.getState()[i].mutations[g]&&r(t.meta.arg.endpointName,t.meta.arg.originalArgs,g,c,t.meta.requestId);else if(E(t)){let y=h[g];y?.valueResolved&&(y.valueResolved({data:t.payload,meta:t.meta.baseQueryMeta}),delete y.valueResolved)}else if(e.internalActions.removeQueryResult.match(t)||e.internalActions.removeMutationResult.match(t)){let y=h[g];y&&(delete h[g],y.cacheEntryRemoved())}else if(e.util.resetApiState.match(t))for(let[y,o]of Object.entries(h))delete h[y],o.cacheEntryRemoved()};function d(t){return S(t)?t.meta.arg.queryCacheKey:x(t)?t.meta.requestId:e.internalActions.removeQueryResult.match(t)?t.payload.queryCacheKey:e.internalActions.removeMutationResult.match(t)?J(t.payload):""}function r(t,c,A,g,y){let o=l.endpointDefinitions[t],s=o?.onCacheEntryAdded;if(!s)return;let n={},a=new Promise(M=>{n.cacheEntryRemoved=M}),T=Promise.race([new Promise(M=>{n.valueResolved=M}),a.then(()=>{throw nt})]);T.catch(()=>{}),h[A]=n;let p=e.endpoints[t].select(o.type==="query"?c:A),Q=g.dispatch((M,B,v)=>v),D={...g,getCacheEntry:()=>p(g.getState()),requestId:y,extra:Q,updateCachedData:o.type==="query"?M=>g.dispatch(e.util.updateQueryData(t,c,M)):void 0,cacheDataLoaded:T,cacheEntryRemoved:a},b=s(c,D);Promise.resolve(b).catch(M=>{if(M!==nt)throw M})}return u};var it=({api:e,context:i,queryThunk:l,mutationThunk:m})=>{let f=ye(l,m),R=Y(l,m),S=I(l,m),x={};return(h,u)=>{if(f(h)){let{requestId:d,arg:{endpointName:r,originalArgs:t}}=h.meta,c=i.endpointDefinitions[r],A=c?.onQueryStarted;if(A){let g={},y=new Promise((a,T)=>{g.resolve=a,g.reject=T});y.catch(()=>{}),x[d]=g;let o=e.endpoints[r].select(c.type==="query"?t:d),s=u.dispatch((a,T,p)=>p),n={...u,getCacheEntry:()=>o(u.getState()),requestId:d,extra:s,updateCachedData:c.type==="query"?a=>u.dispatch(e.util.updateQueryData(r,t,a)):void 0,queryFulfilled:y};A(t,n)}}else if(S(h)){let{requestId:d,baseQueryMeta:r}=h.meta;x[d]?.resolve({data:h.payload,meta:r}),delete x[d]}else if(R(h)){let{requestId:d,rejectedWithValue:r,baseQueryMeta:t}=h.meta;x[d]?.reject({error:h.payload??h.error,isUnhandledError:!r,meta:t}),delete x[d]}}};var ot=({api:e,context:{apiUid:i},reducerPath:l})=>(m,f)=>{e.util.resetApiState.match(m)&&f.dispatch(e.internalActions.middlewareRegistered(i)),typeof process<"u"};import{produceWithPatches as Bt}from"immer";var at=({api:e,queryThunk:i,internalState:l})=>{let m=`${e.reducerPath}/subscriptions`,f=null,R=null,{updateSubscriptionOptions:S,unsubscribeQueryResult:x}=e.internalActions,E=(t,c)=>{if(S.match(c)){let{queryCacheKey:g,requestId:y,options:o}=c.payload;return t?.[g]?.[y]&&(t[g][y]=o),!0}if(x.match(c)){let{queryCacheKey:g,requestId:y}=c.payload;return t[g]&&delete t[g][y],!0}if(e.internalActions.removeQueryResult.match(c))return delete t[c.payload.queryCacheKey],!0;if(i.pending.match(c)){let{meta:{arg:g,requestId:y}}=c,o=t[g.queryCacheKey]??={};return o[`${y}_running`]={},g.subscribe&&(o[y]=g.subscriptionOptions??o[y]??{}),!0}let A=!1;if(i.fulfilled.match(c)||i.rejected.match(c)){let g=t[c.meta.arg.queryCacheKey]||{},y=`${c.meta.requestId}_running`;A||=!!g[y],delete g[y]}if(i.rejected.match(c)){let{meta:{condition:g,arg:y,requestId:o}}=c;if(g&&y.subscribe){let s=t[y.queryCacheKey]??={};s[o]=y.subscriptionOptions??s[o]??{},A=!0}}return A},h=()=>l.currentSubscriptions,r={getSubscriptions:h,getSubscriptionCount:t=>{let A=h()[t]??{};return K(A)},isRequestSubscribed:(t,c)=>!!h()?.[t]?.[c]};return(t,c)=>{if(f||(f=JSON.parse(JSON.stringify(l.currentSubscriptions))),e.util.resetApiState.match(t))return f=l.currentSubscriptions={},R=null,[!0,!1];if(e.internalActions.internal_getRTKQSubscriptions.match(t))return[!1,r];let A=E(l.currentSubscriptions,t),g=!0;if(A){R||(R=setTimeout(()=>{let s=JSON.parse(JSON.stringify(l.currentSubscriptions)),[,n]=Bt(f,()=>s);c.next(e.internalActions.subscriptionsUpdated(n)),f=s,R=null},500));let y=typeof t.type=="string"&&!!t.type.startsWith(m),o=i.rejected.match(t)&&t.meta.condition&&!!t.meta.arg.subscribe;g=!y&&!o}return[g,!1]}};function st(e){let{reducerPath:i,queryThunk:l,api:m,context:f}=e,{apiUid:R}=f,S={invalidateTags:j(`${i}/invalidateTags`)},x=d=>d.type.startsWith(`${i}/`),E=[ot,Xe,Ze,et,rt,it];return{middleware:d=>{let r=!1,c={...e,internalState:{currentSubscriptions:{}},refetchQuery:u,isThisApiSliceAction:x},A=E.map(o=>o(c)),g=at(c),y=tt(c);return o=>s=>{if(!we(s))return o(s);r||(r=!0,d.dispatch(m.internalActions.middlewareRegistered(R)));let n={...d,next:o},a=d.getState(),[T,p]=g(s,n,a),Q;if(T?Q=o(s):Q=p,d.getState()[i]&&(y(s,n,a),x(s)||f.hasRehydrationInfo(s)))for(let D of A)D(s,n,a);return Q}},actions:S};function u(d,r,t={}){return l({type:"query",endpointName:d.endpointName,originalArgs:d.originalArgs,subscribe:!1,forceRefetch:!0,queryCacheKey:r,...t})}}function U(e,...i){return Object.assign(e,...i)}import{enablePatches as Et}from"immer";var he=Symbol(),Me=()=>({name:he,init(e,{baseQuery:i,tagTypes:l,reducerPath:m,serializeQueryArgs:f,keepUnusedDataFor:R,refetchOnMountOrArgChange:S,refetchOnFocus:x,refetchOnReconnect:E,invalidationBehavior:h},u){Et();let d=w=>(typeof process<"u",w);Object.assign(e,{reducerPath:m,endpoints:{},internalActions:{onOnline:z,onOffline:ee,onFocus:L,onFocusLost:Z},util:{}});let{queryThunk:r,mutationThunk:t,patchQueryData:c,updateQueryData:A,upsertQueryData:g,prefetch:y,buildMatchThunkActions:o}=He({baseQuery:i,reducerPath:m,context:u,api:e,serializeQueryArgs:f,assertTagType:d}),{reducer:s,actions:n}=ze({context:u,queryThunk:r,mutationThunk:t,reducerPath:m,assertTagType:d,config:{refetchOnFocus:x,refetchOnReconnect:E,refetchOnMountOrArgChange:S,keepUnusedDataFor:R,reducerPath:m,invalidationBehavior:h}});U(e.util,{patchQueryData:c,updateQueryData:A,upsertQueryData:g,prefetch:y,resetApiState:n.resetApiState}),U(e.internalActions,n);let{middleware:a,actions:T}=st({reducerPath:m,context:u,queryThunk:r,mutationThunk:t,api:e,assertTagType:d});U(e.util,T),U(e,{reducer:s,middleware:a});let{buildQuerySelector:p,buildMutationSelector:Q,selectInvalidatedBy:D,selectCachedArgsForQuery:b}=Je({serializeQueryArgs:f,reducerPath:m});U(e.util,{selectInvalidatedBy:D,selectCachedArgsForQuery:b});let{buildInitiateQuery:M,buildInitiateMutation:B,getRunningMutationThunk:v,getRunningMutationsThunk:P,getRunningQueriesThunk:F,getRunningQueryThunk:C}=je({queryThunk:r,mutationThunk:t,api:e,serializeQueryArgs:f,context:u});return U(e.util,{getRunningMutationThunk:v,getRunningMutationsThunk:P,getRunningQueryThunk:C,getRunningQueriesThunk:F}),{name:he,injectEndpoint(w,O){let q=e;q.endpoints[w]??={},ce(O)?U(q.endpoints[w],{name:w,select:p(w,O),initiate:M(w,O)},o(r,w)):Ke(O)&&U(q.endpoints[w],{name:w,select:Q(),initiate:B(w)},o(t,w))}}}});var kt=be(Me());export{Te as QueryStatus,be as buildCreateApi,de as copyWithStructuralSharing,Me as coreModule,he as coreModuleName,kt as createApi,ge as defaultSerializeQueryArgs,xt as fakeBaseQuery,ct as fetchBaseQuery,gt as retry,ht as setupListeners,me as skipToken};
//# sourceMappingURL=rtk-query.browser.mjs.map